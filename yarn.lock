# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"

"@babel/code-frame@^7.0.0":
  version "7.23.5"
  dependencies:
    "@babel/highlight" "^7.23.4"
    chalk "^2.4.2"

"@babel/helper-module-imports@^7.16.7":
  version "7.22.15"
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-string-parser@^7.23.4":
  version "7.23.4"

"@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"

"@babel/highlight@^7.23.4":
  version "7.23.4"
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.18.3", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.9", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.24.0"
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.24.0", "@babel/runtime@^7.24.4":
  version "7.24.4"
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/types@^7.22.15":
  version "7.24.0"
  dependencies:
    "@babel/helper-string-parser" "^7.23.4"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@emotion/babel-plugin@^11.11.0":
  version "11.11.0"
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/serialize" "^1.1.2"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0":
  version "11.11.0"
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/hash@^0.9.1":
  version "0.9.1"

"@emotion/is-prop-valid@*", "@emotion/is-prop-valid@^1.2.1":
  version "1.2.2"
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.8.1":
  version "0.8.1"

"@emotion/react@^11.0.0-rc.0", "@emotion/react@^11.11.4", "@emotion/react@^11.4.1", "@emotion/react@^11.5.0", "@emotion/react@^11.9.0":
  version "11.11.4"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/cache" "^11.11.0"
    "@emotion/serialize" "^1.1.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.2", "@emotion/serialize@^1.1.3":
  version "1.1.3"
  dependencies:
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/unitless" "^0.8.1"
    "@emotion/utils" "^1.2.1"
    csstype "^3.0.2"

"@emotion/sheet@^1.2.2":
  version "1.2.2"

"@emotion/styled@^11.11.0", "@emotion/styled@^11.3.0", "@emotion/styled@^11.8.1":
  version "11.11.0"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/is-prop-valid" "^1.2.1"
    "@emotion/serialize" "^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"

"@emotion/unitless@^0.8.1":
  version "0.8.1"

"@emotion/use-insertion-effect-with-fallbacks@^1.0.1":
  version "1.0.1"

"@emotion/utils@^1.2.1":
  version "1.2.1"

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.6.1":
  version "4.10.0"

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.0":
  version "8.57.0"

"@floating-ui/core@^1.0.0":
  version "1.6.0"
  dependencies:
    "@floating-ui/utils" "^0.2.1"

"@floating-ui/dom@^1.6.1":
  version "1.6.3"
  dependencies:
    "@floating-ui/core" "^1.0.0"
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/react-dom@^2.0.8":
  version "2.0.8"
  dependencies:
    "@floating-ui/dom" "^1.6.1"

"@floating-ui/utils@^0.2.0", "@floating-ui/utils@^0.2.1":
  version "0.2.1"

"@hookform/resolvers@^5.2.1":
  version "5.2.1"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.2.1.tgz"
  integrity sha512-u0+6X58gkjMcxur1wRWokA7XsiiBJ6aK17aPZxhkoYiK5J+HcTx0Vhu9ovXe6H+dVpO6cjrn2FkJTryXEMlryQ==
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@humanwhocodes/config-array@^0.11.14":
  version "0.11.14"
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"

"@humanwhocodes/object-schema@^2.0.2":
  version "2.0.2"

"@img/sharp-darwin-arm64@0.33.5":
  version "0.33.5"
  resolved "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5.tgz"
  integrity sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==
  optionalDependencies:
    "@img/sharp-libvips-darwin-arm64" "1.0.4"

"@img/sharp-libvips-darwin-arm64@1.0.4":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.0.4.tgz"
  integrity sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@mui/base@^5.0.0-beta.40":
  version "5.0.0-beta.42"
  dependencies:
    "@babel/runtime" "^7.24.4"
    "@floating-ui/react-dom" "^2.0.8"
    "@mui/types" "^7.2.14"
    "@mui/utils" "^6.0.0-alpha.1"
    "@popperjs/core" "^2.11.8"
    clsx "^2.1.0"
    prop-types "^15.8.1"

"@mui/base@5.0.0-beta.40":
  version "5.0.0-beta.40"
  resolved "https://registry.npmjs.org/@mui/base/-/base-5.0.0-beta.40.tgz"
  integrity sha512-I/lGHztkCzvwlXpjD2+SNmvNQvB4227xBXhISPjEaJUXGImOQ9f3D2Yj/T3KasSI/h0MLWy74X0J6clhPmsRbQ==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@floating-ui/react-dom" "^2.0.8"
    "@mui/types" "^7.2.14"
    "@mui/utils" "^5.15.14"
    "@popperjs/core" "^2.11.8"
    clsx "^2.1.0"
    prop-types "^15.8.1"

"@mui/core-downloads-tracker@^5.15.15":
  version "5.15.15"
  resolved "https://registry.npmjs.org/@mui/core-downloads-tracker/-/core-downloads-tracker-5.15.15.tgz"
  integrity sha512-aXnw29OWQ6I5A47iuWEI6qSSUfH6G/aCsW9KmW3LiFqr7uXZBK4Ks+z8G+qeIub8k0T5CMqlT2q0L+ZJTMrqpg==

"@mui/icons-material@^5.15.14":
  version "5.15.14"
  dependencies:
    "@babel/runtime" "^7.23.9"

"@mui/material@^5.0.0", "@mui/material@^5.15.12", "@mui/material@^5.15.14":
  version "5.15.15"
  resolved "https://registry.npmjs.org/@mui/material/-/material-5.15.15.tgz"
  integrity sha512-3zvWayJ+E1kzoIsvwyEvkTUKVKt1AjchFFns+JtluHCuvxgKcLSRJTADw37k0doaRtVAsyh8bz9Afqzv+KYrIA==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/base" "5.0.0-beta.40"
    "@mui/core-downloads-tracker" "^5.15.15"
    "@mui/system" "^5.15.15"
    "@mui/types" "^7.2.14"
    "@mui/utils" "^5.15.14"
    "@types/react-transition-group" "^4.4.10"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"
    react-is "^18.2.0"
    react-transition-group "^4.4.5"

"@mui/private-theming@^5.15.14":
  version "5.15.14"
  resolved "https://registry.npmjs.org/@mui/private-theming/-/private-theming-5.15.14.tgz"
  integrity sha512-UH0EiZckOWcxiXLX3Jbb0K7rC8mxTr9L9l6QhOZxYc4r8FHUkefltV9VDGLrzCaWh30SQiJvAEd7djX3XXY6Xw==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/utils" "^5.15.14"
    prop-types "^15.8.1"

"@mui/styled-engine@^5.15.14":
  version "5.15.14"
  resolved "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-5.15.14.tgz"
  integrity sha512-RILkuVD8gY6PvjZjqnWhz8fu68dVkqhM5+jYWfB5yhlSQKg+2rHkmEwm75XIeAqI3qwOndK6zELK5H6Zxn4NHw==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@emotion/cache" "^11.11.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^5.15.14", "@mui/system@^5.15.15":
  version "5.15.15"
  resolved "https://registry.npmjs.org/@mui/system/-/system-5.15.15.tgz"
  integrity sha512-aulox6N1dnu5PABsfxVGOZffDVmlxPOVgj56HrUnJE8MCSh8lOvvkd47cebIVQQYAjpwieXQXiDPj5pwM40jTQ==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/private-theming" "^5.15.14"
    "@mui/styled-engine" "^5.15.14"
    "@mui/types" "^7.2.14"
    "@mui/utils" "^5.15.14"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/types@^7.2.14":
  version "7.2.14"
  resolved "https://registry.npmjs.org/@mui/types/-/types-7.2.14.tgz"
  integrity sha512-MZsBZ4q4HfzBsywtXgM1Ksj6HDThtiwmOKUXH1pKYISI9gAVXCNHNpo7TlGoGrBaYWZTdNoirIN7JsQcQUjmQQ==

"@mui/utils@^5.15.14":
  version "5.15.14"
  resolved "https://registry.npmjs.org/@mui/utils/-/utils-5.15.14.tgz"
  integrity sha512-0lF/7Hh/ezDv5X7Pry6enMsbYyGKjADzvHyo3Qrc/SSlTsQ1VkbDMbH0m2t3OR5iIVLwMoxwM7yGd+6FCMtTFA==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@types/prop-types" "^15.7.11"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/utils@^6.0.0-alpha.1":
  version "6.0.0-alpha.1"
  dependencies:
    "@babel/runtime" "^7.24.4"
    "@types/prop-types" "^15.7.12"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/x-date-pickers@^7.1.1":
  version "7.1.1"
  dependencies:
    "@babel/runtime" "^7.24.0"
    "@mui/base" "^5.0.0-beta.40"
    "@mui/system" "^5.15.14"
    "@mui/utils" "^5.15.14"
    "@types/react-transition-group" "^4.4.10"
    clsx "^2.1.0"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

"@next/env@14.1.3":
  version "14.1.3"

"@next/eslint-plugin-next@14.1.3":
  version "14.1.3"
  dependencies:
    glob "10.3.10"

"@next/swc-darwin-arm64@14.1.3":
  version "14.1.3"
  resolved "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-14.1.3.tgz"
  integrity sha512-LALu0yIBPRiG9ANrD5ncB3pjpO0Gli9ZLhxdOu6ZUNf3x1r3ea1rd9Q+4xxUkGrUXLqKVK9/lDkpYIJaCJ6AHQ==

"@next/third-parties@^15.1.7":
  version "15.4.4"
  resolved "https://registry.npmjs.org/@next/third-parties/-/third-parties-15.4.4.tgz"
  integrity sha512-0GIEDSxfl9hTU4Ne1TVr/lZUS1wGhDPB4gt6TNvLBA3Fioum+qZRoPt3fiOW9hHQynyLsojglRDaB97T92h0Og==
  dependencies:
    third-party-capital "1.0.20"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"

"@popperjs/core@^2.11.8":
  version "2.11.8"

"@rushstack/eslint-patch@^1.3.3":
  version "1.7.2"

"@standard-schema/utils@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@standard-schema/utils/-/utils-0.3.0.tgz"
  integrity sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g==

"@swc/helpers@0.5.2":
  version "0.5.2"
  dependencies:
    tslib "^2.4.0"

"@types/hoist-non-react-statics@^3.3.1":
  version "3.3.5"
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/json5@^0.0.29":
  version "0.0.29"

"@types/node@^20":
  version "20.11.25"
  dependencies:
    undici-types "~5.26.4"

"@types/parse-json@^4.0.0":
  version "4.0.2"

"@types/prop-types@*", "@types/prop-types@^15.7.11":
  version "15.7.11"

"@types/prop-types@^15.7.12":
  version "15.7.12"

"@types/react-dom@^18":
  version "18.2.21"
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.10":
  version "4.4.10"
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^17.0.0 || ^18.0.0", "@types/react@^18":
  version "18.2.64"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/scheduler@*":
  version "0.16.8"

"@typescript-eslint/parser@^5.4.2 || ^6.0.0":
  version "6.21.0"
  dependencies:
    "@typescript-eslint/scope-manager" "6.21.0"
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/typescript-estree" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@6.21.0":
  version "6.21.0"
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"

"@typescript-eslint/types@6.21.0":
  version "6.21.0"

"@typescript-eslint/typescript-estree@6.21.0":
  version "6.21.0"
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    minimatch "9.0.3"
    semver "^7.5.4"
    ts-api-utils "^1.0.1"

"@typescript-eslint/visitor-keys@6.21.0":
  version "6.21.0"
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    eslint-visitor-keys "^3.4.1"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"

acorn-jsx@^5.3.2:
  version "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.9.0:
  version "8.11.3"

ajv@^6.12.4:
  version "6.12.6"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"

ansi-regex@^6.0.1:
  version "6.0.1"

ansi-styles@^3.2.1:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"

argparse@^2.0.1:
  version "2.0.1"

aria-query@^5.3.0:
  version "5.3.0"
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind "^1.0.5"
    is-array-buffer "^3.0.4"

array-includes@^3.1.6, array-includes@^3.1.7:
  version "3.1.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"

array.prototype.filter@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-array-method-boxes-properly "^1.0.0"
    is-string "^1.0.7"

array.prototype.findlast@^1.2.4:
  version "1.2.4"
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.3:
  version "1.2.4"
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.2"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.toreversed@^1.1.2:
  version "1.1.2"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.3:
  version "1.1.3"
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.1.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.3:
  version "1.0.3"
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.2.1"
    get-intrinsic "^1.2.3"
    is-array-buffer "^3.0.4"
    is-shared-array-buffer "^1.0.2"

ast-types-flow@^0.0.8:
  version "0.0.8"

asynciterator.prototype@^1.0.0:
  version "1.0.0"
  dependencies:
    has-symbols "^1.0.3"

asynckit@^0.4.0:
  version "0.4.0"

available-typed-arrays@^1.0.6, available-typed-arrays@^1.0.7:
  version "1.0.7"
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@=4.7.0:
  version "4.7.0"

axios@^1.6.8:
  version "1.6.8"
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^3.2.1:
  version "3.2.1"
  dependencies:
    dequal "^2.0.3"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

balanced-match@^1.0.0:
  version "1.0.2"

brace-expansion@^1.1.7:
  version "1.1.11"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2:
  version "3.0.2"
  dependencies:
    fill-range "^7.0.1"

busboy@1.6.0:
  version "1.6.0"
  dependencies:
    streamsearch "^1.1.0"

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"

caniuse-lite@^1.0.30001579:
  version "1.0.30001596"

chalk@^2.4.2:
  version "2.4.2"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

classnames@^2.2.5:
  version "2.5.1"

client-only@0.0.1:
  version "0.0.1"

clsx@^2.1.0:
  version "2.1.0"

color-convert@^1.9.0:
  version "1.9.3"
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"

color-name@1.1.3:
  version "1.1.3"

color-string@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/color/-/color-4.2.3.tgz"
  integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

concat-map@0.0.1:
  version "0.0.1"

convert-source-map@^1.5.0:
  version "1.9.0"

cosmiconfig@^7.0.0:
  version "7.1.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

countup.js@^2.8.0:
  version "2.9.0"
  resolved "https://registry.npmjs.org/countup.js/-/countup.js-2.9.0.tgz"
  integrity sha512-llqrvyXztRFPp6+i8jx25phHWcVWhrHO4Nlt0uAOSKHB8778zzQswa4MU3qKBvkXfJKftRYFJuVHez67lyKdHg==

cross-spawn@^7.0.0, cross-spawn@^7.0.2:
  version "7.0.3"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto@^1.0.1:
  version "1.0.1"

csstype@^3.0.2, csstype@^3.1.3:
  version "3.1.3"

damerau-levenshtein@^1.0.8:
  version "1.0.8"

data-view-buffer@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-length@^1.0.0:
  version "1.0.1"
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

data-view-byte-offset@^1.0.0:
  version "1.0.0"
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

"date-fns@^2.25.0 || ^3.2.0", date-fns@^3.6.0:
  version "3.6.0"

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  dependencies:
    ms "2.1.2"

deep-is@^0.1.3:
  version "0.1.4"

deepmerge@^2.1.1:
  version "2.2.1"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"

dequal@^2.0.3:
  version "2.0.3"

detect-libc@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz"
  integrity sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==

dir-glob@^3.0.1:
  version "3.0.1"
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  dependencies:
    esutils "^2.0.2"

dom-helpers@^5.0.1:
  version "5.2.1"
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

eastasianwidth@^0.2.0:
  version "0.2.0"

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

enhanced-resolve@^5.12.0:
  version "5.15.1"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

error-ex@^1.3.1:
  version "1.3.2"
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.22.4:
  version "1.23.0"
  dependencies:
    array-buffer-byte-length "^1.0.1"
    arraybuffer.prototype.slice "^1.0.3"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    data-view-buffer "^1.0.1"
    data-view-byte-length "^1.0.0"
    data-view-byte-offset "^1.0.0"
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.4"
    get-symbol-description "^1.0.2"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.3"
    has-symbols "^1.0.3"
    hasown "^2.0.1"
    internal-slot "^1.0.7"
    is-array-buffer "^3.0.4"
    is-callable "^1.2.7"
    is-data-view "^1.0.1"
    is-negative-zero "^2.0.3"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.3"
    is-string "^1.0.7"
    is-typed-array "^1.1.13"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.5"
    regexp.prototype.flags "^1.5.2"
    safe-array-concat "^1.1.0"
    safe-regex-test "^1.0.3"
    string.prototype.trim "^1.2.8"
    string.prototype.trimend "^1.0.7"
    string.prototype.trimstart "^1.0.7"
    typed-array-buffer "^1.0.2"
    typed-array-byte-length "^1.0.1"
    typed-array-byte-offset "^1.0.2"
    typed-array-length "^1.0.5"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.14"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"

es-define-property@^1.0.0:
  version "1.0.0"
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.0.0, es-errors@^1.1.0, es-errors@^1.2.1, es-errors@^1.3.0:
  version "1.3.0"

es-iterator-helpers@^1.0.15, es-iterator-helpers@^1.0.17:
  version "1.0.17"
  dependencies:
    asynciterator.prototype "^1.0.0"
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.22.4"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.2"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    globalthis "^1.0.3"
    has-property-descriptors "^1.0.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.7"
    iterator.prototype "^1.1.2"
    safe-array-concat "^1.1.0"

es-set-tostringtag@^2.0.2, es-set-tostringtag@^2.0.3:
  version "2.0.3"
  dependencies:
    get-intrinsic "^1.2.4"
    has-tostringtag "^1.0.2"
    hasown "^2.0.1"

es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
  version "1.0.2"
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escape-string-regexp@^1.0.5:
  version "1.0.5"

escape-string-regexp@^4.0.0:
  version "4.0.0"

eslint-config-next@14.1.3:
  version "14.1.3"
  dependencies:
    "@next/eslint-plugin-next" "14.1.3"
    "@rushstack/eslint-patch" "^1.3.3"
    "@typescript-eslint/parser" "^5.4.2 || ^6.0.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-import-resolver-typescript "^3.5.2"
    eslint-plugin-import "^2.28.1"
    eslint-plugin-jsx-a11y "^6.7.1"
    eslint-plugin-react "^7.33.2"
    eslint-plugin-react-hooks "^4.5.0 || 5.0.0-canary-7118f5dd7-20230705"

eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-import-resolver-typescript@^3.5.2:
  version "3.6.1"
  dependencies:
    debug "^4.3.4"
    enhanced-resolve "^5.12.0"
    eslint-module-utils "^2.7.4"
    fast-glob "^3.3.1"
    get-tsconfig "^4.5.0"
    is-core-module "^2.11.0"
    is-glob "^4.0.3"

eslint-module-utils@^2.7.4, eslint-module-utils@^2.8.0:
  version "2.8.1"
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@*, eslint-plugin-import@^2.28.1:
  version "2.29.1"
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlastindex "^1.2.3"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.8.0"
    hasown "^2.0.0"
    is-core-module "^2.13.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.7"
    object.groupby "^1.0.1"
    object.values "^1.1.7"
    semver "^6.3.1"
    tsconfig-paths "^3.15.0"

eslint-plugin-jsx-a11y@^6.7.1:
  version "6.8.0"
  dependencies:
    "@babel/runtime" "^7.23.2"
    aria-query "^5.3.0"
    array-includes "^3.1.7"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "=4.7.0"
    axobject-query "^3.2.1"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    es-iterator-helpers "^1.0.15"
    hasown "^2.0.0"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.entries "^1.1.7"
    object.fromentries "^2.0.7"

"eslint-plugin-react-hooks@^4.5.0 || 5.0.0-canary-7118f5dd7-20230705":
  version "5.0.0-canary-7118f5dd7-20230705"

eslint-plugin-react@^7.33.2:
  version "7.34.0"
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlast "^1.2.4"
    array.prototype.flatmap "^1.3.2"
    array.prototype.toreversed "^1.1.2"
    array.prototype.tosorted "^1.1.3"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.0.17"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.7"
    object.fromentries "^2.0.7"
    object.hasown "^1.1.3"
    object.values "^1.1.7"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.10"

eslint-scope@^7.2.2:
  version "7.2.2"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.0.0 || ^8.0.0", "eslint@^7.23.0 || ^8.0.0", eslint@^8:
  version "8.57.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.0"
    "@humanwhocodes/config-array" "^0.11.14"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.5.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"

esutils@^2.0.2:
  version "2.0.3"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"

fast-glob@^3.2.9, fast-glob@^3.3.1:
  version "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"

fast-levenshtein@^2.0.6:
  version "2.0.6"

fastq@^1.6.0:
  version "1.17.1"
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.0.1:
  version "7.0.1"
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"

find-up@^5.0.0:
  version "5.0.0"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.1"

follow-redirects@^1.15.6:
  version "1.15.6"

for-each@^0.3.3:
  version "0.3.3"
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.1.1"
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.0"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formik@^2.4.5:
  version "2.4.5"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    deepmerge "^2.1.1"
    hoist-non-react-statics "^3.3.0"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    react-fast-compare "^2.0.1"
    tiny-warning "^1.0.2"
    tslib "^2.0.0"

framer-motion@^11.0.18:
  version "11.0.18"
  dependencies:
    tslib "^2.4.0"

fs.realpath@^1.0.0:
  version "1.0.0"

function-bind@^1.1.2:
  version "1.1.2"

function.prototype.name@^1.1.5, function.prototype.name@^1.1.6:
  version "1.1.6"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functions-have-names@^1.2.3:
  version "1.2.3"

get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-symbol-description@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"

get-tsconfig@^4.5.0:
  version "4.7.3"
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@10.3.10:
  version "10.3.10"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^2.3.5"
    minimatch "^9.0.1"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry "^1.10.1"

globals@^13.19.0:
  version "13.24.0"
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.2.11, graceful-fs@^4.2.4:
  version "4.2.11"

graphemer@^1.4.0:
  version "1.4.0"

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"

has-flag@^3.0.0:
  version "3.0.0"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1, has-proto@^1.0.3:
  version "1.0.3"

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"

has-tostringtag@^1.0.0, has-tostringtag@^1.0.1, has-tostringtag@^1.0.2:
  version "1.0.2"
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.0, hasown@^2.0.1:
  version "2.0.1"
  dependencies:
    function-bind "^1.1.2"

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  dependencies:
    react-is "^16.7.0"

ignore@^5.2.0:
  version "5.3.1"

import-fresh@^3.2.1:
  version "3.3.0"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"

internal-slot@^1.0.5, internal-slot@^1.0.7:
  version "1.0.7"
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.0"
    side-channel "^1.0.4"

is-array-buffer@^3.0.4:
  version "3.0.4"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.0.0"
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.0.1:
  version "1.0.4"
  dependencies:
    has-bigints "^1.0.1"

is-boolean-object@^1.1.0:
  version "1.1.2"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"

is-core-module@^2.11.0, is-core-module@^2.13.0, is-core-module@^2.13.1:
  version "2.13.1"
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1:
  version "1.0.1"
  dependencies:
    is-typed-array "^1.1.13"

is-date-object@^1.0.1, is-date-object@^1.0.5:
  version "1.0.5"
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"

is-finalizationregistry@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-generator-function@^1.0.10:
  version "1.0.10"
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"

is-negative-zero@^2.0.3:
  version "2.0.3"

is-number-object@^1.0.4:
  version "1.0.7"
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"

is-path-inside@^3.0.3:
  version "3.0.3"

is-regex@^1.1.4:
  version "1.1.4"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.3:
  version "2.0.3"

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.7"

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.13:
  version "1.1.13"
  dependencies:
    which-typed-array "^1.1.14"

is-weakmap@^2.0.2:
  version "2.0.2"

is-weakref@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"

is-weakset@^2.0.3:
  version "2.0.3"
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"

isarray@^2.0.5:
  version "2.0.5"

isexe@^2.0.0:
  version "2.0.0"

iterator.prototype@^1.1.2:
  version "1.1.2"
  dependencies:
    define-properties "^1.2.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    reflect.getprototypeof "^1.0.4"
    set-function-name "^2.0.1"

jackspeak@^2.3.5:
  version "2.3.6"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.1:
  version "3.0.1"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"

json-schema-traverse@^0.4.1:
  version "0.4.1"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"

json5@^1.0.2:
  version "1.0.2"
  dependencies:
    minimist "^1.2.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.5.3:
  version "4.5.4"
  dependencies:
    json-buffer "3.0.1"

language-subtag-registry@^0.3.20:
  version "0.3.22"

language-tags@^1.0.9:
  version "1.0.9"
  dependencies:
    language-subtag-registry "^0.3.20"

levn@^0.4.1:
  version "0.4.1"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"

locate-path@^6.0.0:
  version "6.0.0"
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"

lodash.merge@^4.6.2:
  version "4.6.2"

lodash@^4.17.21:
  version "4.17.21"

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^6.0.0:
  version "6.0.0"
  dependencies:
    yallist "^4.0.0"

"lru-cache@^9.1.1 || ^10.0.0":
  version "10.2.0"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"

micromatch@^4.0.4:
  version "4.0.5"
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.1:
  version "9.0.3"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@9.0.3:
  version "9.0.3"
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "7.0.4"

ms@^2.1.1:
  version "2.1.3"

ms@2.1.2:
  version "2.1.2"

nanoid@^3.3.6:
  version "3.3.7"

natural-compare@^1.4.0:
  version "1.4.0"

"next@^13.0.0 || ^14.0.0 || ^15.0.0", next@14.1.3:
  version "14.1.3"
  dependencies:
    "@next/env" "14.1.3"
    "@swc/helpers" "0.5.2"
    busboy "1.6.0"
    caniuse-lite "^1.0.30001579"
    graceful-fs "^4.2.11"
    postcss "8.4.31"
    styled-jsx "5.1.1"
  optionalDependencies:
    "@next/swc-darwin-arm64" "14.1.3"
    "@next/swc-darwin-x64" "14.1.3"
    "@next/swc-linux-arm64-gnu" "14.1.3"
    "@next/swc-linux-arm64-musl" "14.1.3"
    "@next/swc-linux-x64-gnu" "14.1.3"
    "@next/swc-linux-x64-musl" "14.1.3"
    "@next/swc-win32-arm64-msvc" "14.1.3"
    "@next/swc-win32-ia32-msvc" "14.1.3"
    "@next/swc-win32-x64-msvc" "14.1.3"

object-assign@^4.1.1:
  version "4.1.1"

object-inspect@^1.13.1:
  version "1.13.1"

object-keys@^1.1.1:
  version "1.1.1"

object.assign@^4.1.4, object.assign@^4.1.5:
  version "4.1.5"
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.7:
  version "1.1.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.fromentries@^2.0.7:
  version "2.0.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.groupby@^1.0.1:
  version "1.0.2"
  dependencies:
    array.prototype.filter "^1.0.3"
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.0.0"

object.hasown@^1.1.3:
  version "1.1.3"
  dependencies:
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.values@^1.1.6, object.values@^1.1.7:
  version "1.1.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

once@^1.3.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

optionator@^0.9.3:
  version "0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

p-limit@^3.0.2:
  version "3.1.0"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  dependencies:
    p-limit "^3.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-exists@^4.0.0:
  version "4.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^3.1.0:
  version "3.1.1"

path-parse@^1.0.7:
  version "1.0.7"

path-scurry@^1.10.1:
  version "1.10.1"
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"

picocolors@^1.0.0:
  version "1.0.0"

picomatch@^2.3.1:
  version "2.3.1"

possible-typed-array-names@^1.0.0:
  version "1.0.0"

postcss@8.4.31:
  version "8.4.31"
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prelude-ls@^1.2.1:
  version "1.2.1"

prop-types@^15.5.8, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.5:
  version "2.0.6"

proxy-from-env@^1.1.0:
  version "1.1.0"

punycode@^2.1.0:
  version "2.3.1"

queue-microtask@^1.2.2:
  version "1.2.3"

react-countup@^6.5.3:
  version "6.5.3"
  resolved "https://registry.npmjs.org/react-countup/-/react-countup-6.5.3.tgz"
  integrity sha512-udnqVQitxC7QWADSPDOxVWULkLvKUWrDapn5i53HE4DPRVgs+Y5rr4bo25qEl8jSh+0l2cToJgGMx+clxPM3+w==
  dependencies:
    countup.js "^2.8.0"

"react-dom@^17.0.0 || ^18.0.0", react-dom@^18, react-dom@^18.0.0, react-dom@^18.2.0, react-dom@>=16.6.0, react-dom@>=16.8.0:
  version "18.2.0"
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.0"

react-easy-swipe@^0.0.21:
  version "0.0.21"
  dependencies:
    prop-types "^15.5.8"

react-fast-compare@^2.0.1:
  version "2.0.4"

react-hook-form@^7.55.0, react-hook-form@^7.62.0:
  version "7.62.0"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.62.0.tgz"
  integrity sha512-7KWFejc98xqG/F4bAxpL41NB3o1nnvQO1RWZT3TqRZYL8RryQETGfEdVnJN2fy1crCiBLLjkRBVK05j24FxJGA==

react-is@^16.13.1:
  version "16.13.1"

react-is@^16.7.0:
  version "16.13.1"

react-is@^18.2.0:
  version "18.2.0"

react-responsive-carousel@^3.2.23:
  version "3.2.23"
  dependencies:
    classnames "^2.2.5"
    prop-types "^15.5.8"
    react-easy-swipe "^0.0.21"

react-transition-group@^4.4.5:
  version "4.4.5"
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

"react@^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^17.0.0 || ^18.0.0", react@^18, react@^18.0.0, react@^18.2.0, "react@^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0", "react@>= 16.3.0", "react@>= 16.8.0 || 17.x.x || ^18.0.0-0", react@>=16.6.0, react@>=16.8.0:
  version "18.2.0"
  dependencies:
    loose-envify "^1.1.0"

reflect.getprototypeof@^1.0.4:
  version "1.0.5"
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    es-abstract "^1.22.3"
    es-errors "^1.0.0"
    get-intrinsic "^1.2.3"
    globalthis "^1.0.3"
    which-builtin-type "^1.1.3"

regenerator-runtime@^0.14.0:
  version "0.14.1"

regexp.prototype.flags@^1.5.0, regexp.prototype.flags@^1.5.2:
  version "1.5.2"
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

resolve-from@^4.0.0:
  version "4.0.0"

resolve-pkg-maps@^1.0.0:
  version "1.0.0"

resolve@^1.19.0, resolve@^1.22.4:
  version "1.22.8"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"

rimraf@^3.0.2:
  version "3.0.2"
  dependencies:
    glob "^7.1.3"

run-parallel@^1.1.9:
  version "1.2.0"
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.0:
  version "1.1.2"
  dependencies:
    call-bind "^1.0.7"
    get-intrinsic "^1.2.4"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-regex-test@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.6"
    es-errors "^1.3.0"
    is-regex "^1.1.4"

scheduler@^0.23.0:
  version "0.23.0"
  dependencies:
    loose-envify "^1.1.0"

semver@^6.3.1:
  version "6.3.1"

semver@^7.5.4:
  version "7.6.0"
  dependencies:
    lru-cache "^6.0.0"

semver@^7.6.3:
  version "7.7.2"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz"
  integrity sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==

set-function-length@^1.2.1:
  version "1.2.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.0, set-function-name@^2.0.1:
  version "2.0.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

sharp@^0.33.3:
  version "0.33.5"
  resolved "https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz"
  integrity sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==
  dependencies:
    color "^4.2.3"
    detect-libc "^2.0.3"
    semver "^7.6.3"
  optionalDependencies:
    "@img/sharp-darwin-arm64" "0.33.5"
    "@img/sharp-darwin-x64" "0.33.5"
    "@img/sharp-libvips-darwin-arm64" "1.0.4"
    "@img/sharp-libvips-darwin-x64" "1.0.4"
    "@img/sharp-libvips-linux-arm" "1.0.5"
    "@img/sharp-libvips-linux-arm64" "1.0.4"
    "@img/sharp-libvips-linux-s390x" "1.0.4"
    "@img/sharp-libvips-linux-x64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-arm64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-x64" "1.0.4"
    "@img/sharp-linux-arm" "0.33.5"
    "@img/sharp-linux-arm64" "0.33.5"
    "@img/sharp-linux-s390x" "0.33.5"
    "@img/sharp-linux-x64" "0.33.5"
    "@img/sharp-linuxmusl-arm64" "0.33.5"
    "@img/sharp-linuxmusl-x64" "0.33.5"
    "@img/sharp-wasm32" "0.33.5"
    "@img/sharp-win32-ia32" "0.33.5"
    "@img/sharp-win32-x64" "0.33.5"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

side-channel@^1.0.4:
  version "1.0.6"
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^4.0.1:
  version "4.1.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

slash@^3.0.0:
  version "3.0.0"

source-map-js@^1.0.2:
  version "1.0.2"

source-map@^0.5.7:
  version "0.5.7"

streamsearch@^1.1.0:
  version "1.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.10:
  version "4.0.10"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    regexp.prototype.flags "^1.5.0"
    set-function-name "^2.0.0"
    side-channel "^1.0.4"

string.prototype.trim@^1.2.8:
  version "1.2.8"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimend@^1.0.7:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimstart@^1.0.7:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"

strip-json-comments@^3.1.1:
  version "3.1.1"

styled-jsx@5.1.1:
  version "5.1.1"
  dependencies:
    client-only "0.0.1"

stylis@4.2.0:
  version "4.2.0"

supports-color@^5.3.0:
  version "5.5.0"
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"

swiper@^11.2.10:
  version "11.2.10"
  resolved "https://registry.npmjs.org/swiper/-/swiper-11.2.10.tgz"
  integrity sha512-RMeVUUjTQH+6N3ckimK93oxz6Sn5la4aDlgPzB+rBrG/smPdCTicXyhxa+woIpopz+jewEloiEE3lKo1h9w2YQ==

swr@^2.3.6:
  version "2.3.6"
  resolved "https://registry.npmjs.org/swr/-/swr-2.3.6.tgz"
  integrity sha512-wfHRmHWk/isGNMwlLGlZX5Gzz/uTgo0o2IRuTMcf4CPuPFJZlq0rDaKUx+ozB5nBOReNV1kiOyzMfj+MBMikLw==
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

tapable@^2.2.0:
  version "2.2.1"

text-table@^0.2.0:
  version "0.2.0"

third-party-capital@1.0.20:
  version "1.0.20"
  resolved "https://registry.npmjs.org/third-party-capital/-/third-party-capital-1.0.20.tgz"
  integrity sha512-oB7yIimd8SuGptespDAZnNkzIz+NWaJCu2RMsbs4Wmp9zSDUM8Nhi3s2OOcqYuv3mN4hitXc8DVx+LyUmbUDiA==

tiny-case@^1.0.3:
  version "1.0.3"

tiny-warning@^1.0.2:
  version "1.0.3"

to-fast-properties@^2.0.0:
  version "2.0.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

toposort@^2.0.2:
  version "2.0.2"

ts-api-utils@^1.0.1:
  version "1.3.0"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.0.0, tslib@^2.4.0:
  version "2.6.2"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"

type-fest@^2.19.0:
  version "2.19.0"

typed-array-buffer@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    is-typed-array "^1.1.13"

typed-array-byte-length@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-byte-offset@^1.0.2:
  version "1.0.2"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"

typed-array-length@^1.0.5:
  version "1.0.5"
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-proto "^1.0.3"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"

typescript@^5, typescript@>=3.3.1, typescript@>=4.2.0:
  version "5.4.2"

unbox-primitive@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici-types@~5.26.4:
  version "5.26.5"

uri-js@^4.2.2:
  version "4.4.1"
  dependencies:
    punycode "^2.1.0"

use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  integrity sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-builtin-type@^1.1.3:
  version "1.1.3"
  dependencies:
    function.prototype.name "^1.1.5"
    has-tostringtag "^1.0.0"
    is-async-function "^2.0.0"
    is-date-object "^1.0.5"
    is-finalizationregistry "^1.0.2"
    is-generator-function "^1.0.10"
    is-regex "^1.1.4"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.1"
    which-typed-array "^1.1.9"

which-collection@^1.0.1:
  version "1.0.2"
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.14, which-typed-array@^1.1.9:
  version "1.1.14"
  dependencies:
    available-typed-arrays "^1.0.6"
    call-bind "^1.0.5"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.1"

which@^2.0.1:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"

yallist@^4.0.0:
  version "4.0.0"

yaml@^1.10.0:
  version "1.10.2"

yocto-queue@^0.1.0:
  version "0.1.0"

yup@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/yup/-/yup-1.7.0.tgz"
  integrity sha512-VJce62dBd+JQvoc+fCVq+KZfPHr+hXaxCcVgotfwWvlR0Ja3ffYKaJBT8rptPOSKOGJDCUnW2C2JWpud7aRP6Q==
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"
