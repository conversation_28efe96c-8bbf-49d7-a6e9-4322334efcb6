{"name": "ginan_landing_page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^5.2.1", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.12", "@mui/x-date-pickers": "^7.1.1", "@next/third-parties": "^15.1.7", "axios": "^1.6.8", "crypto": "^1.0.1", "date-fns": "^3.6.0", "formik": "^2.4.5", "framer-motion": "^11.0.18", "next": "14.1.3", "react": "^18", "react-countup": "^6.5.3", "react-dom": "^18", "react-hook-form": "^7.62.0", "react-responsive-carousel": "^3.2.23", "sharp": "^0.33.3", "swiper": "^11.2.10", "swr": "^2.3.6", "yup": "^1.7.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.1.3", "typescript": "^5"}}