'use server';

import React from 'react';
import HeroSection from '@/components/new-version/HeroSection';
import Layout from '@/components/new-version/Layout';
import Gallery from '@/components/new-version/Gallery';
import FacilitiesAndAmenities from '@/components/new-version/FacilitiesAndAmenities';
import RoomOptions from '@/components/new-version/RoomOptions';
import GuestReviews from '@/components/new-version/GuestReviews';
import GuideKliaT2 from '@/components/new-version/GuideKliaT2';
import BookYourReservation from '@/components/new-version/BookYourReservation';
import { getOutlets } from '@/actions/getOutlets';
import { getReviews } from '@/actions/getReviews';
import { getRoomImages } from '@/actions/getRoomImages';

async function Klia1SleepLoungePage() {
  const [outlets, reviews, roomImageData] = await Promise.all([
    getOutlets(),
    getReviews(),
    getRoomImages()
  ]);

  const currentOutlet = outlets.find((outlet) => String(outlet.key).toLowerCase() === 'sleep lounge');

  return (
    <Layout>
      <HeroSection
        // outlets={currentOutlet ? [currentOutlet] : outlets}
        outlets={outlets}
        lotId={currentOutlet?.lotId}
      />
      <Gallery outlet={currentOutlet} roomImageData={roomImageData} />
      <FacilitiesAndAmenities />
      <RoomOptions selectedOutlet={currentOutlet} />
      <GuestReviews reviewsData={reviews['data']} />
      <GuideKliaT2 currentOutlet={currentOutlet} />
      <BookYourReservation
        outletName='CapsuleTransit Sleep Lounge'
        selectedOutlet={currentOutlet}
      />
    </Layout>
  );
}

export default Klia1SleepLoungePage;
