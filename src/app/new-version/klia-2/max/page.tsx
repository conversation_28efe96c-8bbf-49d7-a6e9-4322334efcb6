'use server';

import React from 'react';
import Layout from '@/components/new-version/Layout';
import HeroSection from '@/components/new-version/CapsuleTransitMax/HeroSection';
import ProperPlace from '@/components/new-version/CapsuleTransitMax/ProperPlace';
import Extension from '@/components/new-version/CapsuleTransitMax/Extension';
import RoomType from '@/components/new-version/CapsuleTransitMax/RoomType';
import GuestReviews from '@/components/new-version/GuestReviews';
import GuideKliaT2 from '@/components/new-version/GuideKliaT2';
import BookYourReservation from '@/components/new-version/BookYourReservation';
import { getOutlets } from '@/actions/getOutlets';
import { getReviews } from '@/actions/getReviews';

async function Klia2MaxPage() {
  const [outlets, reviews] = await Promise.all([
    getOutlets(),
    getReviews(),
  ]);

  const currentOutlet = outlets.find(
    (outlet) => String(outlet.key).toLowerCase() === 'max'
  );

  return (
    <Layout>
      <HeroSection />
      <ProperPlace selectedOutlet={currentOutlet} />
      <Extension />
      <RoomType selectedOutlet={currentOutlet} />
      <GuestReviews reviewsData={reviews['data']} />
      <GuideKliaT2 currentOutlet={currentOutlet} />
      <BookYourReservation
        outletName='CapsuleTransit MAX'
        selectedOutlet={currentOutlet}
      />
    </Layout>
  );
}

export default Klia2MaxPage;
