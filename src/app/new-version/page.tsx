'use server';

import React from 'react';
import HeroSection from '@/components/new-version/HeroSection';
import WelcomeSection from '@/components/new-version/WelcomeSection';
import OutletsSection from '@/components/new-version/OutletsSection';
import BookDirectSection from '@/components/new-version/BookDirectSection';
import ReviewsSection from '@/components/new-version/Home/ReviewsSection';
import CSRCampaignSection from '@/components/new-version/CSRCampaignSection';
import Layout from '@/components/new-version/Layout';
import { getOutlets } from '@/actions/getOutlets';
import { getReviews } from '@/actions/getReviews';

async function NewHomePage() {
  // Fetch data in parallel for better performance
  const [outlets, reviews] = await Promise.all([
    getOutlets(),
    getReviews(),
  ]);

  return (
    <Layout>
      <HeroSection outlets={outlets} />
      <WelcomeSection />
      <OutletsSection outlets={outlets} />
      <BookDirectSection />
      <ReviewsSection reviewsData={reviews['data']} />
      <CSRCampaignSection />
    </Layout>
  );
}

export default NewHomePage;
