'use client';

import React, { useState } from 'react';
import { Box, Typography, Fade, Slide, Collapse } from '@mui/material';
import { styled } from '@mui/material/styles';
import FilterRoomSection from './FilterRoomSection';
import { AreaType } from './types';
import DepartingFrom from './OutletArea/DepartingFrom';
import TransitingThrough from './OutletArea/TransitingThrough';

const StyledOutletSelection = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(3),
  width: '100%',
  padding: theme.spacing(4, 2),
  paddingBottom: '200px',
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2, 1),
    gap: theme.spacing(2),
    paddingBottom: '500px',
  },
}));

const TitleSection = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  margin: '0 auto',
  marginBottom: '40px',
}));

const StyledTitle = styled(Typography)(({ theme }) => ({
  color: '#1A1A1A',
  fontWeight: 700,
  fontSize: '32px',
  lineHeight: 1.2,
  marginBottom: theme.spacing(1.4),
  [theme.breakpoints.down('sm')]: {
    fontSize: '24px',
  },
}));

const StyledDescription = styled(Typography)(({ theme }) => ({
  color: '#666666',
  fontSize: '16px',
  lineHeight: 1.5,
  [theme.breakpoints.down('sm')]: {
    fontSize: '14px',
  },
}));

const OutletAreaContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  maxWidth: '1200px',
  display: 'flex',
  justifyContent: 'center',
  '& img': {
    width: '100%',
    height: 'auto',
    maxWidth: '100%',
  },
  [theme.breakpoints.down('sm')]: {
    '& img': {
      maxWidth: '100%',
    },
  },
}));

const FilterSectionContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  marginTop: theme.spacing(4),
  [theme.breakpoints.down('sm')]: {
    marginTop: theme.spacing(3),
  },
}));

const OutletSelection = ({ navigateThrough }: { navigateThrough: string }) => {
  const [selectedArea, setSelectedArea] = useState<AreaType | null>(null);

  return (
    <StyledOutletSelection>
      <TitleSection>
        <Fade in timeout={800}>
          <StyledTitle variant='h1' sx={{ fontSize: '32px', fontWeight: 500 }}>
            Where would you like to stay?
          </StyledTitle>
        </Fade>
        <Collapse in={!selectedArea} timeout={600}>
          <StyledDescription
            variant='body1'
            sx={{ fontSize: '16px', fontWeight: 400, color: '#011589' }}
          >
            Please select an outlet to proceed.
          </StyledDescription>
        </Collapse>
      </TitleSection>

      <Fade in timeout={1000}>
        <OutletAreaContainer>
          <Box sx={{ maxWidth: '1440px', maxHeight: '960px' }}>
            {navigateThrough === 'departing_from' ? (
              <DepartingFrom
                selectedArea={selectedArea}
                setSelectedArea={setSelectedArea}
              />
            ) : (
              <TransitingThrough
                selectedArea={selectedArea}
                setSelectedArea={setSelectedArea}
              />
            )}

          </Box>
        </OutletAreaContainer>
      </Fade>

      <Slide
        direction='up'
        in={!!selectedArea}
        timeout={800}
        mountOnEnter
        unmountOnExit
      >
        <FilterSectionContainer>
          {selectedArea && <FilterRoomSection selectedOutlet={selectedArea} />}
        </FilterSectionContainer>
      </Slide>
    </StyledOutletSelection>
  );
};

export default OutletSelection;
