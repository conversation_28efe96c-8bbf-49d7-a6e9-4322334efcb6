import React, { useEffect, useState } from 'react';
import { Box, Typography, Button, Divider } from '@mui/material';
import { styled } from '@mui/material/styles';
import DatetimeSelections from '@/components/new-version/FilterRoomsSection/DatetimeSelections';
import { FilterData } from '@/components/new-version/FilterRoomsSection/types';
import { OUTLETS } from '@/constant/outlet';
import { useRouter } from 'next/navigation';

const FilterContainer = styled(Box)(({ theme }) => ({
  maxWidth: '1144px',
  position: 'fixed',
  bottom: '20px',
  padding: theme.spacing(2, 3),
  borderRadius: '8px',
  gap: theme.spacing(2),
  boxShadow: '1px 4px 4px 4px rgba(0, 0, 0, 0.05)',
  width: '100%',
  backgroundColor: '#EEFCFA',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(2),
  },
}));

const FilterHeadingSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderRadius: '8px',
  gap: theme.spacing(2),
  width: '100%',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(2),
  },
}));

const LeftSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(3),
  flex: 1,
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'center',
    gap: theme.spacing(1),
    width: '100%',
  },
}));

const OutletInfo = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  [theme.breakpoints.down('md')]: {
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

const OutletLabel = styled(Typography)(({ theme }) => ({
  fontSize: '14px',
  fontWeight: 700,
  color: '#1A1A1A',
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  width: '100%',
  [theme.breakpoints.down('md')]: {
    textAlign: 'center',
  },
}));

const OutletName = styled(Typography)(({ theme }) => ({
  fontSize: '32px',
  fontWeight: 600,
  color: '#011589',
  lineHeight: 1.2,
  [theme.breakpoints.down('md')]: {
    textAlign: 'center',
    fontSize: '28px',
  },
}));

const ImportantNotice = styled(Box)(({ theme }) => ({
  backgroundColor: '#FA25A81A',
  padding: theme.spacing(1.5, 2),
  borderRadius: '4px',
  flex: 2,
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

const ImportantText = styled(Typography)(({ theme }) => ({
  fontSize: '16px',
  lineHeight: 1.4,
  fontWeight: 600,
  color: '#1A1A1A',
  '& .important-label': {
    fontWeight: 700,
    color: '#FA25A8',
    fontSize: '16px',
  },
}));

const ProceedButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#011589',
  color: '#ffffff',
  fontWeight: 600,
  fontSize: '14px',
  textTransform: 'uppercase',
  padding: theme.spacing(1.5, 3),
  borderRadius: '4px',
  minWidth: '140px',
  '&:hover': {
    backgroundColor: '#011589',
  },
  [theme.breakpoints.down('md')]: {
    width: '100%',
  },
}));

interface FilterRoomSectionProps {
  selectedOutlet?: string;
  onProceed?: () => void;
}

const FilterRoomSection: React.FC<FilterRoomSectionProps> = ({
  selectedOutlet = 'Airside',
  onProceed,
}) => {
  const outlet = OUTLETS.find((outlet) => outlet.key === selectedOutlet);
  const lotId = outlet?.lotId;
  const router = useRouter();
  const [isProceed, setIsProceed] = useState(false);

  const [filters, setFilters] = useState<FilterData>({});

  const handleDateChange = (date: Date | null) => {
    setFilters((prev) => ({ ...prev, date }));
  };

  const handleCheckInTimeChange = (time: string) => {
    setFilters((prev) => ({ ...prev, checkInTime: time }));
  };

  const handleStayDurationChange = (duration: number) => {
    setFilters((prev) => ({ ...prev, stayDuration: duration }));
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const onCheckAvailability = async () => {
    try {
      setIsSubmitting(true);
      console.log('data', filters);

      if (
        !filters.date ||
        !filters.checkInTime ||
        !filters.stayDuration ||
        !lotId
      ) {
        return;
      }

      // Convert date to Unix timestamp (seconds since epoch)
      const checkInDatetime = Math.floor(filters.date.getTime() / 1000);

      // Store the form data in localStorage for the booking page
      const formData = {
        lotId: lotId,
        date: filters.date.toISOString(),
        checkInTime: filters.checkInTime,
        duration: filters.stayDuration,
        checkInDatetime: checkInDatetime,
      };

      // Redirect to the booking page with query parameters
      const queryParams = new URLSearchParams(formData as any);

      router.push(`/new-booking?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error fetching data:', error);
      // Handle error here (show toast, error message, etc.)
      setIsSubmitting(false);
    } finally {
    }
  };

  return (
    <FilterContainer>
      <FilterHeadingSection>
        <LeftSection>
          <OutletInfo>
            <OutletLabel>Selected Outlet</OutletLabel>
            <OutletName>{selectedOutlet}</OutletName>
          </OutletInfo>
          {!isProceed && outlet?.isRestrictedArea && (
            <ImportantNotice>
              <ImportantText>
                <span className='important-label'>IMPORTANT:</span> As this
                outlet is located in the restricted area, you will need a
                boarding pass and complete your flight check-in process to
                access the hotel.
              </ImportantText>
            </ImportantNotice>
          )}
        </LeftSection>

        {!isProceed && (
          <ProceedButton variant='contained' onClick={() => setIsProceed(true)}>
            Yes, Proceed
          </ProceedButton>
        )}
      </FilterHeadingSection>
      {isProceed && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box
            sx={{
              width: '100%',
              mt: '20px',
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              gap: '20px',
            }}
          >
            <DatetimeSelections
              filters={{ ...filters, lotId: lotId?.toString() }}
              handleDateChange={handleDateChange}
              handleCheckInTimeChange={handleCheckInTimeChange}
              handleStayDurationChange={handleStayDurationChange}
            />
            {/* CHECK AVAILABILITY BUTTON */}
            <Box
              sx={{
                flex: 1,
                paddingTop: { xs: '10px', md: '32px' },
              }}
            >
              <Button
                variant='contained'
                type='button'
                onClick={onCheckAvailability}
                disabled={
                  isSubmitting ||
                  !filters.date ||
                  !filters.checkInTime ||
                  !filters.stayDuration
                }
                sx={{
                  bgcolor: '#011589',
                  color: 'white',
                  fontWeight: 'bold',
                  px: { xs: 3, md: 4 },
                  py: { xs: 1.25, md: 1.5 },
                  borderRadius: '4px',
                  boxShadow: 2,
                  '&:hover': { bgcolor: '#011589' },
                  width: { xs: '100%', lg: 'auto' },
                  fontSize: '14px',
                  whiteSpace: 'nowrap',
                  maxHeight: '40px',
                }}
              >
                {isSubmitting ? 'CHECKING...' : 'CHECK AVAILABILITY'}
              </Button>
            </Box>
          </Box>
        </>
      )}
    </FilterContainer>
  );
};

export default FilterRoomSection;
