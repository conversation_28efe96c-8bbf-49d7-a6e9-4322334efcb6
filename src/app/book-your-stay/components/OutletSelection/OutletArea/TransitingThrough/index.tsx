import React from 'react';
import { AreaType } from '../../types';
import { useMediaQuery } from '@mui/material';
import DesktopTransitingThrough from './DesktopTransitingThrough';
import { MobileTransitingThrough } from './MobileTransitingThrough';

const TransitingThrough = ({
  selectedArea,
  setSelectedArea,
}: {
  selectedArea: AreaType | null;
  setSelectedArea: (area: AreaType | null) => void;
}) => {
  const isMobile = useMediaQuery('(max-width:768px)');

  return isMobile ? (
    <MobileTransitingThrough
      selectedArea={selectedArea}
      setSelectedArea={setSelectedArea}
    />
  ) : (
    <DesktopTransitingThrough
      selectedArea={selectedArea}
      setSelectedArea={setSelectedArea}
    />
  );
};

export default TransitingThrough;
