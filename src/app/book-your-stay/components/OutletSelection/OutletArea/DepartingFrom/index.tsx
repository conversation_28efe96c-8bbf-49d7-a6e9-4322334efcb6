import { useMediaQuery } from '@mui/material';
import { AreaType } from '../../types';
import { DesktopDepartingFrom } from './DesktopDepartingFrom';
import { MobileDepartingFrom } from './MobileDepartingFrom';


const DepartingFrom = ({
  selectedArea,
  setSelectedArea,
}: {
  selectedArea: AreaType | null;
  setSelectedArea: (area: AreaType | null) => void;
}) => {

  const isMobile = useMediaQuery('(max-width:768px)');


  return isMobile ? (
    <MobileDepartingFrom
      selectedArea={selectedArea}
      setSelectedArea={setSelectedArea}
    />
  ) : (
    <DesktopDepartingFrom
      selectedArea={selectedArea}
      setSelectedArea={setSelectedArea}
    />
  );
};

export default DepartingFrom;
