'use client';

import React, { useState } from 'react';
import { Box, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import Image from 'next/image';
import kliaT1 from '@/assets/images/klia-t1.svg';
import kliaT2 from '@/assets/images/klia-t2.svg';
import transportation from '@/assets/images/CT-transport.gif';
import { Help, HelpOutline } from '@mui/icons-material';

const StyledTerminalVisualization = styled(Paper)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(3),
  backgroundColor: 'transparent',
  borderRadius: '16px',
  width: '100%',
  maxWidth: '886px',
  marginTop: theme.spacing(2),
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(1.5),
    gap: theme.spacing(0.5),
    maxWidth: '100%',
    marginTop: theme.spacing(1),
  },
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
}));

const TerminalIcon = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(0.5),
  },
}));

const RouteLine = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
  flex: 1,
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(0.5),
  },
}));

const TransportationContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
  width: '100%',
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(0.5),
  },
}));

const TerminalVisualization = ({
  selectedTerminal,
}: {
  selectedTerminal: string;
}) => {
  const [informationHovered, setInformationHovered] = useState(false);

  return (
    <StyledTerminalVisualization elevation={0}>
      <TerminalIcon>
        <Box
          sx={{
            position: 'relative',
            width: { xs: '50px', sm: '100px', md: '180px' },
            height: { xs: '39px', sm: '70px', md: '140px' },
          }}
        >
          <Image
            src={kliaT1}
            alt='KLIA Terminal 1'
            width={180}
            height={140}
            style={{
              opacity: selectedTerminal === 'Terminal_1' ? 1 : 0.3,
              filter:
                selectedTerminal === 'Terminal_1' ? 'none' : 'grayscale(100%)',
              width: '100%',
              height: '100%',
            }}
          />
        </Box>
      </TerminalIcon>

      <RouteLine>
        <TransportationContainer>
          <Box
            sx={{
              position: 'relative',
              width: { xs: '100px', sm: '120px', md: '300px' },
              height: { xs: '50px', sm: '60px', md: '80px' },
            }}
          >
            <Image
              src={transportation}
              alt='Transportation Route'
              width={150}
              height={40}
              style={{
                opacity: 0.7,
                width: '100%',
                height: '100%',
              }}
            />
          </Box>
          <Box
            display={'flex'}
            flexDirection={'column'}
            onMouseEnter={() => setInformationHovered(true)}
            onMouseLeave={() => setInformationHovered(false)}
            justifyContent={'center'}
            alignItems={'center'}
            position={'relative'}
          >
            {informationHovered ? (
              <HelpOutline color='inherit' sx={{ color: '#011589' }} />
            ) : (
              <Help color='inherit' sx={{ color: '#011589' }} />
            )}
            {informationHovered && (
              <Box
                marginTop={'160px'}
                position={'absolute'}
                display={'flex'}
                width={'300px'}
                bgcolor={'#fff'}
                padding={2}
                zIndex={10}
                color={'#011589'}
                fontSize={'14px'}
                boxShadow={'2px 4px 4px 2px rgba(0, 0, 0, 0.25)'}
                borderRadius={'8px'}
              >
                If you wish to travel between KLIA Terminal 1 and Terminal 2,
                you can take either the free shuttle bus service (30 minutes
                ride) or ERL (Train 10 minutes ride).
              </Box>
            )}
          </Box>
        </TransportationContainer>
      </RouteLine>

      <TerminalIcon>
        <Box
          sx={{
            position: 'relative',
            width: { xs: '50px', sm: '100px', md: '180px' },
            height: { xs: '39px', sm: '70px', md: '140px' },
          }}
        >
          <Image
            src={kliaT2}
            alt='KLIA Terminal 2'
            width={180}
            height={140}
            style={{
              opacity: selectedTerminal === 'Terminal_2' ? 1 : 0.3,
              filter:
                selectedTerminal === 'Terminal_2' ? 'none' : 'grayscale(100%)',
              width: '100%',
              height: '100%',
            }}
          />
        </Box>
      </TerminalIcon>
    </StyledTerminalVisualization>
  );
};

export default TerminalVisualization;
