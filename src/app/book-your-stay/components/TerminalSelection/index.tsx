'use client';

import React, { useState } from 'react';
import { Box, Typography, SelectChangeEvent } from '@mui/material';
import { styled } from '@mui/material/styles';
import SelectionSection from '../SelectionSection';
import OutletSelection from '../OutletSelection';
import TerminalVisualization from '../TerminalVisualization';

// Styled components for custom styling
const StyledContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  padding: theme.spacing(2),
  maxWidth: '1144px',
  margin: '0 auto',
  gap: theme.spacing(3),
  width: '100%',
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(4),
    gap: theme.spacing(4),
  },
}));

const StyledHeading = styled(Typography)(({ theme }) => ({
  textAlign: 'center',
  color: '#1A1A1A',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  lineHeight: 1.3,
  paddingX: theme.spacing(1),
  maxWidth: '886px',
  width: '100%',
  [theme.breakpoints.down('sm')]: {
    fontSize: '22px',
    marginBottom: theme.spacing(1.5),
  },
  [theme.breakpoints.between('sm', 'md')]: {
    fontSize: '26px',
    marginBottom: theme.spacing(2),
  },
  [theme.breakpoints.up('md')]: {
    fontSize: '32px',
    marginBottom: theme.spacing(3),
  },
}));

const TerminalSelection = () => {
  const [navigateThrough, setNavigateThrough] = useState<
    'departing_from' | 'transiting_through' | string
  >('departing_from');
  const [selectedTerminal, setSelectedTerminal] = useState('Terminal_2');
  const [currentStep, setCurrentStep] = useState(1);

  const handleDepartingChange = (event: SelectChangeEvent) => {
    setNavigateThrough(event.target.value);
  };

  const handleTerminalChange = (event: SelectChangeEvent) => {
    setSelectedTerminal(event.target.value);
  };

  const handleNext = () => {
    // Handle next button click
    console.log('Departing from:', navigateThrough);
    console.log('Selected terminal:', selectedTerminal);
    setCurrentStep(2);
  };

  return (
    <StyledContainer>
      {currentStep === 1 && (
        <StyledHeading variant='h2'>
          Tell us your travel plan so we can guide you to the best booking
          options.
        </StyledHeading>
      )}

      <SelectionSection
        currentStep={currentStep}
        navigateThrough={navigateThrough}
        selectedTerminal={selectedTerminal}
        handleDepartingChange={handleDepartingChange}
        handleTerminalChange={handleTerminalChange}
        handleNext={handleNext}
      />

      {currentStep === 1 && (
        <TerminalVisualization selectedTerminal={selectedTerminal} />
      )}

      {currentStep !== 1 && <OutletSelection navigateThrough={navigateThrough} />}
    </StyledContainer>
  );
};

export default TerminalSelection;
