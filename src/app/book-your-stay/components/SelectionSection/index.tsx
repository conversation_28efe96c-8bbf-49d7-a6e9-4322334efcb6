'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const NextButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#011589 !important',
  color: 'white',
  borderRadius: '4px',
  padding: '12px 32px',
  textTransform: 'none',
  fontSize: '14px',
  maxHeight: '42px',
  fontWeight: 600,
  [theme.breakpoints.down('md')]: {
    width: '100%',
    marginTop: theme.spacing(1),
  },
}));

const StyledSelectionSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  flexWrap: 'wrap',
  justifyContent: 'center',
  maxWidth: '886px',
  width: '100%',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: theme.spacing(1.5),
  },
}));

const StyledSelect = styled(FormControl)(({ theme }) => ({
  minWidth: '312px',
  maxHeight: '42px',
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    maxHeight: '42px',
    fontSize: '16px',
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
  [theme.breakpoints.down('md')]: {
    minWidth: '100%',
    width: '100%',
  },
}));

const SelectionSection = ({
  currentStep,
  navigateThrough,
  selectedTerminal,
  handleDepartingChange,
  handleTerminalChange,
  handleNext,
}: {
  currentStep: number;
  navigateThrough: string;
  selectedTerminal: string;
  handleDepartingChange: (event: SelectChangeEvent) => void;
  handleTerminalChange: (event: SelectChangeEvent) => void;
  handleNext: () => void;
}) => {
  return (
    <StyledSelectionSection>
      <Typography
        variant='body1'
        sx={{ color: '#1A1A1A', fontWeight: 600, fontSize: '16px' }}
      >
        I am
      </Typography>

      <StyledSelect variant='outlined' size='medium'>
        <Select
          value={navigateThrough}
          // label='Navigate through'
          onChange={handleDepartingChange}
        >
          <MenuItem value='departing_from'>departing from</MenuItem>
          <MenuItem value='transiting_through'>transiting through</MenuItem>
        </Select>
      </StyledSelect>

      <StyledSelect variant='outlined' size='medium'>
        {/* <InputLabel>Select Terminal</InputLabel> */}
        <Select
          value={selectedTerminal}
          // label='Select Terminal'
          onChange={handleTerminalChange}
        >
          <MenuItem value='Terminal_1'>KLIA Terminal 1</MenuItem>
          <MenuItem value='Terminal_2'>KLIA Terminal 2</MenuItem>
        </Select>
      </StyledSelect>
      {currentStep === 1 && (
        <NextButton
          variant='contained'
          onClick={handleNext}
          disabled={!navigateThrough || !selectedTerminal}
        >
          NEXT
        </NextButton>
      )}
    </StyledSelectionSection>
  );
};

export default SelectionSection;
