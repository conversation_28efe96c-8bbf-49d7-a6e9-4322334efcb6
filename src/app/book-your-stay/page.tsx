import NavbarNewBooking from '@/components/new-version/NavbarNewBooking';
import { Box } from '@mui/material';
import React from 'react';
import TerminalSelection from './components/TerminalSelection';

const BookYourStayPage = () => {
  return (
    <Box
      display={'flex'}
      width={'100%'}
      minHeight={'100vh'}
      flexDirection={'column'}
      sx={{ 
        overflowX: 'hidden', 
        paddingTop: { xs: '80px' },
        paddingX: { xs: 2, sm: 3, md: 4 },
        paddingBottom: { xs: 4, md: 6 }
      }}
    >
      <NavbarNewBooking />
      <TerminalSelection />
    </Box>
  );
};

export default BookYourStayPage;
