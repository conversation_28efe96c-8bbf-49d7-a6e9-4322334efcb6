
// API Response Types
export interface ApiRoomResponse {
    roomTypeName: string;
    roomZoneNames: string;
    maxPax: string;
    price: string;
    availableCount: number;
  }
  
  export interface ApiResponse {
    status: string;
    data: ApiRoomResponse[];
  }
  
  // Hook Parameters
  export interface GetRoomsParams {
    checkInDatetime: number; // Unix timestamp in seconds
    duration: number; // Duration in hours
    lotId: number; // Lot ID (1, 2, etc.)
    durations?: number[];
  }
  
  // Transformed Room Data
  export interface RoomData {
    id: string;
    name: string;
    zone: string;
    maxPax: number;
    price: number;
    availableCount: number;
    bedType: string;
    capacity: string;
  }
  
  // Error type
  export interface ApiError {
    message: string;
    status?: number;
  }