import { API_BASE_URL } from '@/config/app';
import {
  ApiResponse,
  ApiRoomResponse,
  GetRoomsParams,
  RoomData,
} from '../types';

/**
 * Custom fetcher function for the rooms API
 */
export const roomsFetcher = async (url: string): Promise<ApiResponse> => {
  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (data.status !== 'success') {
    throw new Error(data.message || 'API request failed');
  }

  return data;
};

/**
 * Transform API response to internal room data format
 */
export const transformRoomData = (apiRooms: ApiRoomResponse[]): RoomData[] => {
  return apiRooms
    .map((room, index) => ({
      id: `${room.roomTypeName}-${index}`,
      name: room.roomTypeName,
      zone: room.roomZoneNames === '-' ? 'All Zones' : room.roomZoneNames,
      maxPax: parseInt(room.maxPax),
      price: parseFloat(room.price),
      availableCount: room.availableCount,
      bedType: getBedType(parseInt(room.maxPax)),
      capacity: `${room.maxPax} ${
        parseInt(room.maxPax) > 1 ? 'Adults' : 'Adult'
      }`,
    }))
    .filter((room) => Number(room.price) > 0);
};

/**
 * Determine bed type based on max capacity
 */
export const getBedType = (maxPax: number): string => {
  switch (maxPax) {
    case 1:
      return 'Single Bed';
    case 2:
      return 'Queen Bed';
    case 3:
      return 'Queen + Single Bed';
    default:
      return 'Multiple Beds';
  }
};

/**
 * Build API URL with query parameters
 */
export const buildApiUrl = (params: GetRoomsParams): string => {
  const searchParams = new URLSearchParams({
    checkInDatetime: params.checkInDatetime.toString(),
    duration: params.duration.toString(),
    lotId: params.lotId.toString(),
  });

  return `${API_BASE_URL}/landing-page/list-for-booking/?${searchParams.toString()}`;
};

/**
 * Validate that all required parameters are present and valid
 */
export const validateParams = (params: GetRoomsParams | null): boolean => {
  if (!params) return false;

  return (
    params.checkInDatetime > 0 && // Must be a valid timestamp
    params.duration > 0 &&
    params.duration <= 24 && // Duration between 1-24 hours
    params.lotId > 0 // Must be a valid lot ID
  );
};
