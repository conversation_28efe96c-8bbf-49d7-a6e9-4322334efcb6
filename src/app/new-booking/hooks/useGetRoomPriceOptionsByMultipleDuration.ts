import { useEffect, useState } from 'react';
import axios from 'axios';
import { buildApiUrl, validateParams } from './utils';
import { ApiRoomResponse } from './types';

export const useGetRoomPriceOptionsByMultipleDuration = (params: {
  checkInDatetime: number;
  durations: number[];
  lotId: number;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [roomPriceOptions, setRoomPriceOptions] = useState<{
    [k: string]: { roomTypeName: string; hours: number; price: number }[];
  }>({});

  const getRoomPriceOptionsByMultipleDuration = async () => {
    setIsLoading(true);

    const roomsByDurations = await Promise.all(
      params.durations.map(async (duration) => {
        const queryParams = {
          checkInDatetime: params.checkInDatetime,
          duration,
          lotId: params.lotId,
        };

        const result = await axios
          .get(buildApiUrl(queryParams))
          .then((res) => res.data?.data)
          .catch((err) => {
            console.error(
              'Error when fetching rooms by multiple duration',
              err
            );
            return [];
          });

        return {
          duration,
          rooms: result as ApiRoomResponse[],
        };
      })
    );

    // convert to {[roomType]: [{duration, price}]}
    const roomsByRoomType = roomsByDurations.reduce<{
      [k: string]: { roomTypeName: string; hours: number; price: number }[];
    }>((obj, item) => {
      item.rooms.forEach((room) => {
        const roomTypeName = room.roomTypeName;
        if (!obj[roomTypeName]) {
          obj[roomTypeName] = [];
        }

        if (Number(room.price) > 0) {
          obj[roomTypeName].push({
            roomTypeName,
            hours: item.duration,
            price: parseFloat(room.price),
          });
        }
      });
      return obj;
    }, {});

    setRoomPriceOptions(roomsByRoomType);
    setIsLoading(false);
    return roomsByDurations;
  };

  useEffect(() => {
    const isValidParams = validateParams({
      ...params,
      duration: params.durations?.[0],
    });
    if (params.durations.length > 0 && isValidParams) {
      getRoomPriceOptionsByMultipleDuration();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.checkInDatetime, params.lotId, JSON.stringify(params.durations)]);

  return {
    getRoomPriceOptionsByMultipleDuration,
    isLoading,
    roomPriceOptions,
  };
};
