import useS<PERSON> from 'swr';
import { useCallback, useEffect, useState } from 'react';
import axios from 'axios';
import {
  ApiError,
  ApiResponse,
  ApiRoomResponse,
  GetRoomsParams,
} from './types';
import {
  validateParams,
  buildApiUrl,
  transformRoomData,
  roomsFetcher,
} from './utils';

/**
 * Hook for fetching rooms data using SWR with multi-duration caching
 *
 * Features:
 * - Caches rooms data by duration to avoid duplicate API calls
 * - Automatically uses cached data when available
 * - Provides utilities to check cache status and preload durations
 * - Clears cache when parameters change (checkInDatetime or lotId)
 *
 * Usage:
 * ```tsx
 * const {
 *   rooms,
 *   isLoading,
 *   isDurationCached,
 *   preloadDuration,
 *   clearCache
 * } = useGetRooms({
 *   checkInDatetime: 1640995200,
 *   duration: 3,
 *   lotId: 1,
 *   durations: [3, 6, 12, 24] // Optional: for multi-duration fetching
 * });
 * ```
 */
export const useGetRooms = (params: GetRoomsParams | null) => {
  // Only make the API call if all parameters are valid
  const shouldFetch = validateParams(params);

  const [isLoadingRoomsByDurations, setIsLoadingRoomsByDurations] =
    useState(false);
  const [roomsByDurations, setRoomsByDurations] = useState<
    { duration: number; rooms: ApiRoomResponse[] }[]
  >([]);
  const [roomPriceOptions, setRoomPriceOptions] = useState<{
    [k: string]: { roomTypeName: string; hours: number; price: number }[];
  }>({});

  const [cachedKeyGetMultipleDuration, setCachedKeyGetMultipleDuration] =
    useState<string | null>(null);

  // Cache for individual duration requests to avoid duplicate API calls
  const [roomsCache, setRoomsCache] = useState<{
    [key: string]: ApiRoomResponse[];
  }>({});

  const cachedKeyParams = `${String(params?.checkInDatetime)}-${params?.lotId}`;

  /**
   * Get rooms from cache for a specific duration
   */
  const getRoomsFromCache = useCallback(
    (duration: number): ApiRoomResponse[] | null => {
      const cacheKey = `${cachedKeyParams}-${duration}`;
      return roomsCache[cacheKey] || null;
    },
    [cachedKeyParams, roomsCache]
  );

  /**
   * Check if rooms for a specific duration are available in cache
   */
  const isDurationCached = useCallback(
    (duration: number): boolean => {
      return getRoomsFromCache(duration) !== null;
    },
    [getRoomsFromCache]
  );

  /**
   * Add rooms to cache for a specific duration
   */
  const addToCache = useCallback(
    (duration: number, rooms: ApiRoomResponse[]) => {
      const cacheKey = `${cachedKeyParams}-${duration}`;
      setRoomsCache((prev) => ({
        ...prev,
        [cacheKey]: rooms,
      }));
    },
    [cachedKeyParams]
  );

  /**
   * Clear the entire cache
   */
  const clearCache = useCallback(() => {
    setRoomsCache({});
  }, []);

  /**
   * Preload rooms for a specific duration
   */
  const preloadDuration = useCallback(
    async (duration: number) => {
      if (
        !params?.checkInDatetime ||
        !params?.lotId ||
        isDurationCached(duration)
      ) {
        return;
      }

      const queryParams = {
        checkInDatetime: params.checkInDatetime,
        duration,
        lotId: params.lotId,
      };

      try {
        const result = await axios
          .get(buildApiUrl(queryParams))
          .then((res) => res.data?.data)
          .catch((err) => {
            console.error('Error when preloading duration', err);
            return [];
          });

        const rooms = result as ApiRoomResponse[];
        addToCache(duration, rooms);
      } catch (error) {
        console.error('Failed to preload duration:', error);
      }
    },
    [params?.checkInDatetime, params?.lotId, isDurationCached, addToCache]
  );

  const getRoomPriceOptionsByMultipleDuration = useCallback(async () => {
    const isInvalidParams =
      !params?.durations?.length || !params?.checkInDatetime || !params?.lotId;

    if (isInvalidParams) {
      return;
    }
    setIsLoadingRoomsByDurations(true);

    const roomsByDurations = await Promise.all(
      params.durations!.map(async (duration) => {
        // Check if we already have this duration cached
        const cachedRooms = getRoomsFromCache(duration);
        if (cachedRooms) {
          return {
            duration,
            rooms: cachedRooms,
          };
        }

        // If not cached, fetch from API
        const queryParams = {
          checkInDatetime: params.checkInDatetime,
          duration,
          lotId: params.lotId,
        };

        const result = await axios
          .get(buildApiUrl(queryParams))
          .then((res) => res.data?.data)
          .catch((err) => {
            console.error(
              'Error when fetching rooms by multiple duration',
              err
            );
            return [];
          });

        const rooms = result as ApiRoomResponse[];

        // Cache the result for future use
        addToCache(duration, rooms);

        return {
          duration,
          rooms,
        };
      })
    );

    // convert to {[roomType]: [{duration, price}]}
    const roomsByRoomType = roomsByDurations.reduce<{
      [k: string]: { roomTypeName: string; hours: number; price: number }[];
    }>((obj, item) => {
      item.rooms.forEach((room) => {
        const roomTypeName = room.roomTypeName;
        if (!obj[roomTypeName]) {
          obj[roomTypeName] = [];
        }

        if (Number(room.price) > 0) {
          obj[roomTypeName].push({
            roomTypeName,
            hours: item.duration,
            price: parseFloat(room.price),
          });
        }
      });
      return obj;
    }, {});

    setRoomPriceOptions(roomsByRoomType);
    setRoomsByDurations(roomsByDurations);
    setIsLoadingRoomsByDurations(false);

    return roomsByDurations;
  }, [
    params?.durations,
    params?.checkInDatetime,
    params?.lotId,
    getRoomsFromCache,
    addToCache,
  ]);

  // Clear cache when parameters change (different checkInDatetime or lotId)
  useEffect(() => {
    if (cachedKeyParams && cachedKeyParams !== cachedKeyGetMultipleDuration) {
      setCachedKeyGetMultipleDuration(cachedKeyParams);
      // Clear the cache when parameters change
      setRoomsCache({});
      getRoomPriceOptionsByMultipleDuration();
    }
  }, [
    cachedKeyParams,
    cachedKeyGetMultipleDuration,
    getRoomPriceOptionsByMultipleDuration,
  ]);

  // Check if we can get rooms from cache instead of making API call
  const shouldUseCache =
    shouldFetch && params && isDurationCached(params.duration);
  const cachedRooms = shouldUseCache
    ? getRoomsFromCache(params.duration)
    : null;

  // Extract complex expression for SWR key
  const swrKey = shouldFetch && !shouldUseCache ? buildApiUrl(params!) : null;

  const { data, error, isLoading, mutate } = useSWR<ApiResponse, ApiError>(
    swrKey,
    roomsFetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 30000, // 30 seconds
      errorRetryCount: 3,
      errorRetryInterval: 5000, // 5 seconds
    }
  );

  // Use cached data if available, otherwise use SWR data
  const apiRooms = shouldUseCache ? cachedRooms : data?.data;

  // Transform the data
  const rooms = apiRooms ? transformRoomData(apiRooms) : [];

  // Sort rooms by capacity (ascending)
  const sortedRooms = [...rooms].sort((a, b) => a.maxPax - b.maxPax);

  // If we used cached data, add it to cache for consistency
  useEffect(() => {
    if (shouldUseCache && cachedRooms && params?.duration) {
      addToCache(params.duration, cachedRooms);
    }
  }, [shouldUseCache, cachedRooms, params?.duration, addToCache]);

  return {
    rooms: sortedRooms,
    isLoading: shouldFetch ? (shouldUseCache ? false : isLoading) : false,
    error: shouldFetch ? (shouldUseCache ? null : error) : null,
    mutate,
    // Additional computed values
    totalAvailableRooms: rooms.reduce(
      (sum, room) => sum + room.availableCount,
      0
    ),
    hasRooms: rooms.length > 0,
    // Price range
    minPrice: rooms.length > 0 ? Math.min(...rooms.map((r) => r.price)) : 0,
    maxPrice: rooms.length > 0 ? Math.max(...rooms.map((r) => r.price)) : 0,
    // Validation state
    isParamsValid: shouldFetch,
    // Cache utilities
    isDurationCached,
    getRoomsFromCache,
    clearCache,
    preloadDuration,
    // Multi-duration data
    roomsByDurations,
    roomPriceOptions,
    isLoadingRoomsByDurations,
  };
};

/**
 * Hook for fetching rooms with automatic date conversion
 */
export const useGetRoomsWithDate = (
  date: Date | null,
  duration: number,
  lotId: number
) => {
  // Only create params if we have a valid date and other parameters
  const params: GetRoomsParams | null =
    date && duration > 0 && lotId > 0
      ? {
          checkInDatetime: Math.floor(date.getTime() / 1000), // Convert to Unix timestamp
          duration,
          lotId,
        }
      : null;

  return useGetRooms(params);
};

export default useGetRooms;
