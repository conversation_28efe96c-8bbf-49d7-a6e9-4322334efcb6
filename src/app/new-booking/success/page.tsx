'use client';

import { Box } from '@mui/material';
import SuccessfulBooking from '@/components/new-version/BookingPayment/BookingResult/Successful';
import Navbar from '@/components/new-version/Navbar';
import Footer from '@/components/new-version/Footer';
import { useBookingData } from '@/context/BookingContext';
// import { redirect } from 'next/navigation';

const BookingSuccessPage = () => {
  const { getBookingData } = useBookingData();
  const bookingData = getBookingData();

  if (!bookingData) {
    // return redirect('/new-version');

    return <div>...</div>;
  }

  return (
    <>
      <Navbar />
      <Box sx={{ marginTop: '90px', padding: '40px 0' }}>
        <SuccessfulBooking bookingData={bookingData} />
      </Box>
      <Footer />
    </>
  );
};

export default BookingSuccessPage;
