"use client";

import { <PERSON>, <PERSON><PERSON>, Container, <PERSON>ack, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import CTLogo from "@/assets/icons/general/Logo-CT.svg";

export default function EInvoicePortalPage() {
    return (
        <Box
            minHeight="100vh"
            width="100vw"
            sx={{
                backgroundColor: "#1A1A1A",
                color: "#fff",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
            }}
        >
            {/* Logo centered at the very top */}
            <Box
                width="100%"
                display="flex"
                justifyContent="center"
                alignItems="center"
                sx={{ pt: { xs: 4, md: 8 }, pb: { xs: 2, md: 6 } }}
            >
                <Link href="/" aria-label="Go to home page">
                    <Image src={CTLogo} alt="CapsuleTransit Logo" width={220} height={44} style={{ cursor: "pointer" }} />
                </Link>
            </Box>
            {/* Main content vertically centered */}
            <Box
                flex={1}
                width="100%"
                display="flex"
                alignItems="center"
                justifyContent="center"
                sx={{ minHeight: { md: 'calc(100vh - 180px)' } }}
            >
                <Container maxWidth="md">
                    <Stack
                        direction={{ xs: "column", md: "row" }}
                        spacing={{ xs: 6, md: 8 }}
                        alignItems={{ xs: "center", md: "flex-start" }}
                        justifyContent="center"
                        width="100%"
                    >
                        {/* Left Section */}
                        <Stack
                            spacing={4}
                            width={{ xs: '100%', md: '70%' }}
                            justifyContent="center"
                            alignItems={{ xs: "center", md: "flex-start" }}
                            sx={{ pr: { md: 8 } }}
                        >
                            <Typography
                                fontWeight={700}
                                color="#FFFFFF"
                                sx={{
                                    fontSize: { xs: "2.5rem", md: "3.5rem" },
                                    lineHeight: 1.1,
                                    textAlign: { xs: "center", md: "left" },
                                    mb: 1,
                                }}
                            >
                                Access Your <br /> e-Invoicing Portal
                            </Typography>
                            <Typography
                                fontWeight={400}
                                color="#FFFFFF"
                                sx={{
                                    fontSize: { xs: "1.25rem", md: "1.25rem" },
                                    textAlign: { xs: "center", md: "left" },
                                    mb: 1,
                                    lineHeight: 1.5,
                                }}
                            >
                                Easily manage and submit your invoices online through our e-Invoicing system.
                            </Typography>
                            <Button
                                variant="contained"
                                sx={{
                                    background: "#77FBDE",
                                    color: "#1A1A1A",
                                    fontWeight: 700,
                                    width: { xs: "100%", md: 280 },
                                    px: 0,
                                    py: 1.5,
                                    borderRadius: 0,
                                    boxShadow: "none",
                                    textTransform: "none",
                                    fontSize: "1rem",
                                    alignSelf: { xs: "center", md: "flex-start" },
                                    "&:hover": { background: "#16FFDD" },
                                    maxWidth: { xs: "280px" },
                                }}
                                href="https://connector-landing.smartinvoicesandbox.asia/capsule-transit/EXTERNAL/CapsuleTransit"
                                target="_blank"
                            >
                                GO TO E-INVOICING PORTAL
                            </Button>
                        </Stack>

                        {/* Right Section */}
                        <Stack
                            width={{ xs: '100%', md: '30%' }}
                            height={{ xs: '100%', md: '100%' }}
                            direction={{ xs: "column", md: "column" }}
                            alignItems="center"
                            justifyContent="flex-end"
                            spacing={2}
                            mt={{ xs: 4, md: 2 }}
                        >
                            <Box
                                sx={{
                                    width: { xs: 180, md: 220 },
                                    height: { xs: 180, md: 220 },
                                    background: "#fff",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderRadius: 2,
                                    mb: 1,
                                    overflow: 'hidden',
                                }}
                            >
                                <img
                                    src="/qrcode-capsuletransit.png"
                                    alt="QR code to CapsuleTransit e-Invoice portal"
                                    style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                                />
                            </Box>
                            <Typography
                                fontWeight={400}
                                color="#E2E6E0"
                                sx={{
                                    fontSize: { xs: "1rem", md: "1.1rem" },
                                    textAlign: "center",
                                    mt: 0,
                                }}
                            >
                                Or scan with your phone to access
                            </Typography>
                        </Stack>
                    </Stack>
                </Container>
            </Box>
            {/* Help text at the bottom */}
            <Box width="100%" py={2} textAlign="center" sx={{ mt: 'auto' }}>
                <Typography
                    variant="body2"
                    fontSize={{ xs: "1rem", md: "1.1rem" }}
                    sx={{ wordBreak: 'break-word', color: "#FFFFFF !important" }}
                >
                    Need help? Contact our support team at
                    <Box component="br" sx={{ display: { xs: "block", md: "none" } }} />
                    <a href="mailto:<EMAIL>" style={{ color: "#77FBDE", textDecoration: "underline" }}><EMAIL></a>.
                </Typography>
            </Box>
        </Box >
    );
} 