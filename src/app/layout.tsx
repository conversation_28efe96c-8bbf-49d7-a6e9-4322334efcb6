'use client';

import { Inter } from 'next/font/google';
import './globals.css';
import ProviderContext from '@/context/ProviderContexts';
import '../assets/font/IBM_Plex_Sans.css';
import { GoogleAnalytics } from '@next/third-parties/google';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import MetaPixel from '@/components/tracker/MetaPixel';

const inter = Inter({ subsets: ['latin'] });

// export const metadata: Metadata = {
//   title: "Capsule Transit",
//   description: "Airport Transit Hotel In KLIA 1 and KLIA 2",
// };

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en'>
      <head>
        <title>CapsuleTransit | Airport Transit Hotel in KLIA1 and KLIA2</title>
        <meta
          name='description'
          content='Experience the ultimate convenience at CapsuleTransit Hotel, located within KLIA T1 and T2. Enjoy flexible booking by the hour, check in anytime, gender-specific zones and shower service.'
        />
        <meta
          property='og:title'
          content='CapsuleTransit | Airport Transit Hotel in KLIA1 and KLIA2'
        />
        <meta
          property='og:description'
          content='Experience the ultimate convenience at CapsuleTransit Hotel, located within KLIA T1 and T2. Enjoy flexible booking by the hour, check in anytime, gender-specific zones and shower service.'
        />
        <link rel='icon' href='/favicon.ico' />
        <link rel='apple-touch-icon' sizes='180x180' href='/favicon.ico' />
        <link rel='icon' type='image/png' sizes='32x32' href='/favicon.ico' />
        <link rel='icon' type='image/png' sizes='16x16' href='/favicon.ico' />
        <link rel='manifest' href='/site.webmanifest' />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:6392419,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
        <MetaPixel pixelId='1388530488876753' />
      </head>
      <body style={{ margin: 0 }}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <ProviderContext>{children}</ProviderContext>
        </LocalizationProvider>
      </body>
      <GoogleAnalytics gaId='G-85KEJYPXW7' />
    </html>
  );
}
