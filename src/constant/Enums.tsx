export enum featuresEnum {
  OneHourStay = "1h",
  ThreeHourStay = "3h",
  SixHourStay = "6h",
  TwelveHourStay = "12h",
  ThreeHourLounge = "(Lounge) 3h",
  amMin3Hour = "(a.m) min. 3h",
  pmMin6Hour = "(p.m) min. 6h",
  FemaleSingle = "femaleSingle",
  MaleSingle = "maleSingle",
  Single = "single",
  Queen = "queen",
  Double = "double",
  Suite = "suit",
  Concierge = "concierge",
  Wifi = "wifi",
  Shower = "shower",
  Toileteries = "toileteries",
  Slippers = "slippers",
  Drinks = "drinks",
}

export enum lotNumberEnum {
  landside = 1,
  airside = 2,
  sleepLounge = 3,
  max = 4,
}
