import LockerIcon from '@/assets/icons/icons/icon-service-locker.svg';
import WifiIcon from '@/assets/icons/icons/icon-service-wifi.svg';
import SharedBathroomTowelIcon from '@/assets/icons/icons/icon-service-shower.svg';
import ToiletriesIcon from '@/assets/icons/icons/icon-toiletries.svg';
import SlippersIcon from '@/assets/icons/icons/icon-service-slipper.svg';
import WaterCoffeeIcon from '@/assets/icons/icons/icon-water-coffee.svg';
import ReceptionDeskIcon from '@/assets/icons/icons/icon-reception-desk.svg';
import EnsuiteBathroomIcon from '@/assets/icons/icons/icon-shared-bathroom.svg';
import ShowerTowelIcon from '@/assets/icons/icons/icon-shower-towel.svg';
import BidetHairdryerIcon from '@/assets/icons/icons/icon-bidet-hairdryer.svg';
import SafetyDepositBoxIcon from '@/assets/icons/icons/icon-deposit-box.svg';
import BottledWaterIcon from '@/assets/icons/icons/icon-bottled-water.svg';
import AirConditioningIcon from '@/assets/icons/icons/icon-air-conditioning.svg';
import DentalKitIcon from '@/assets/icons/icons/icon-detal-kit-20.svg';
import ConnectivityPanelIcon from '@/assets/icons/icons/icon-socket-near-bed.svg';
import RainShowerIcon from '@/assets/icons/icons/icon-service-shower.svg';
import LoungeIcon from '@/assets/icons/icons/icon-lounge-library.svg'; // Using locker icon as placeholder
import FemaleZoneIcon from '@/assets/icons/icons/icon-room-single_female.svg';
import ShoeLockerIcon from '@/assets/icons/icons/icon-shoe-locker.svg';

import SharedBathroomIcon from '@/assets/icons/icons/icon-shared-bathroom.svg'; // Using shower icon as placeholder
import DrinksIcon from '@/assets/icons/icons/icon-tee-coffee.svg';
import EyeMaskIcon from '@/assets/icons/icons/icon-eye-mask.svg'; // Using slipper icon as placeholder
import EarplugIcon from '@/assets/icons/icons/icon-earplug.svg'; // Using slipper icon as placeholder
import SocketIcon from '@/assets/icons/icons/icon-socket-near-bed.svg';

export const FACILITIES = {
  locker: {
    icon: LockerIcon,
    name: 'Lockers',
  },
  wifi: {
    icon: WifiIcon,
    name: 'Wifi',
  },
  shower: {
    icon: SharedBathroomTowelIcon,
    name: 'En-suite Bathroom & Towel',
  },
  toiletries: {
    icon: ToiletriesIcon,
    name: 'Toiletries',
  },
  slippers: {
    icon: SlippersIcon,
    name: 'Slippers',
  },
  waterCoffee: {
    icon: WaterCoffeeIcon,
    name: 'Water & Coffee',
  },
  airConditioning: {
    icon: AirConditioningIcon,
    name: 'Air Conditioning',
  },
  receptionDesk: {
    icon: ReceptionDeskIcon,
    name: '24-hour Reception Desk',
  },
  workDesk: {
    icon: null,
    name: 'Work Desk',
  },
  flatScreenTV: {
    icon: null,
    name: 'Flat-screen TV',
  },
  wardrobeCloset: {
    icon: null,
    name: 'Wardrobe & Closet',
  },
  inHouseSlippers: {
    icon: SlippersIcon,
    name: 'In-house Slippers',
  },
  dentalKit: {
    icon: DentalKitIcon,
    name: 'Dental Kit',
  },
  enSuiteBathroom: {
    icon: EnsuiteBathroomIcon,
    name: 'Ensuite Bathroom',
  },
  rainShower: {
    icon: RainShowerIcon,
    name: 'Rain Shower',
  },
  bathTub: {
    icon: null,
    name: 'Bathtub',
  },
  showerTowel: {
    icon: ShowerTowelIcon,
    name: 'Shower Towel',
  },
  bathrobes: {
    icon: null,
    name: 'Bathrobes',
  },
  bidetHairdryer: {
    icon: BidetHairdryerIcon,
    name: 'Bidet & Hairdryer',
  },
  bottledWater: {
    icon: BottledWaterIcon,
    name: 'Bottled Water',
  },
  safetyDepositBox: {
    icon: SafetyDepositBoxIcon,
    name: 'Safety Deposit Box',
  },
  connectivityPanel: {
    icon: ConnectivityPanelIcon,
    name: 'Connectivity Panel',
  },
  teaCoffeeMakingFacilities: {
    icon: null,
    name: 'Tea/coffee Making Facilities',
  },
  runwayView: {
    icon: null,
    name: 'Runway View',
  },
  blackoutCurtains: {
    icon: null,
    name: 'Black-out Curtains',
  },
  loungeLibrary: {
    icon: LoungeIcon,
    name: 'Lounge and Library',
  },
  femaleZoneAvailable: { name: 'Female Zone Available', icon: FemaleZoneIcon },
  luggageStorageLocker: { name: 'Luggage Storage Locker', icon: LockerIcon },
  shoeLocker: { name: 'Shoe Locker', icon: ShoeLockerIcon },
  sleepingEyeMask: { name: 'Sleeping Eye Mask', icon: EyeMaskIcon },
  earplug: { name: 'Earplug', icon: EarplugIcon },
  sharedBathroom: { name: 'Shared Bathroom', icon: SharedBathroomIcon },
  freeFlowTeaCoffee: { name: 'Free Flow Tea and Coffee', icon: DrinksIcon },
  socketNearBed: { name: 'Socket near the bed', icon: SocketIcon },
};

export const OUTLETS = [
  {
    terminal: 'KLIA Terminal 2',
    outlet: 'Capsule Transit MAX',
    title: 'Capsule Transit MAX',
    subtitle: 'KLIA Terminal 2, Public Area',
    overlay: 'rgba(0,0,0,0.4)',
    lotId: 4,
    isPublicArea: true,
    location: 'Terminal 2',
    isNewOpening: true,
    description:
      'Capsule hotel with showers and lockers, located in the public area.',
    key: 'MAX',
    roomTypes: [
      {
        name: 'Deluxe',
        beds: 'Twin / Queen / King Bed for 2 Adults',
        priceFrom: 'RM200',
      },
      { name: 'Executive', beds: 'King Bed for 2 Adults', priceFrom: 'RM400' },
      {
        name: 'Runway Suite',
        beds: 'King Bed with Sofa for 2 Adults',
        priceFrom: 'RM600',
      },
    ],
    facilities: [
      FACILITIES.locker,
      FACILITIES.wifi,
      FACILITIES.shower,
      FACILITIES.toiletries,
      FACILITIES.slippers,
      FACILITIES.waterCoffee,
    ],
  },

  {
    terminal: 'KLIA Terminal 2',
    outlet: 'Capsule Transit Landside',
    title: 'Capsule Transit Landside',
    subtitle: 'KLIA Terminal 2, Public Area',
    overlay: 'rgba(0,0,0,0.4)',
    lotId: 1,
    isPublicArea: true,
    location: 'Terminal 2',
    description:
      'Capsule hotel with showers and lockers, located in the public area.',
    key: 'Landside',
    roomTypes: [
      {
        name: 'Single Capsule',
        beds: 'Female Zone / Mixed Zone for 1 Adult',
        priceFrom: 'RM200',
      },
      {
        name: 'Queen Capsule',
        beds: 'Queen Bed for 2 Adults',
        priceFrom: 'RM400',
      },
      {
        name: 'Private Capsule Suite',
        beds: 'Queen Bed + Single Bed for 3 Adults',
        priceFrom: 'RM600',
      },
    ],
    facilities: [
      FACILITIES.locker,
      FACILITIES.wifi,
      FACILITIES.shower,
      FACILITIES.toiletries,
      FACILITIES.slippers,
      FACILITIES.waterCoffee,
    ],
  },

  {
    terminal: 'KLIA Terminal 2',
    outlet: 'Capsule Transit Airside',
    title: 'Capsule Transit Airside',
    subtitle: 'KLIA Terminal 2, Restricted Area',
    overlay: 'rgba(0,0,0,0.4)',
    lotId: 2,
    isRestrictedArea: true,
    location: 'Terminal 2',
    description:
      'Capsule hotel with showers and lockers, located in the restricted area.',
    key: 'Airside',
    roomTypes: [
      {
        name: 'Single Capsule',
        beds: 'Female Zone / Mixed Zone for 1 Adult',
        priceFrom: 'RM200',
      },
    ],
    facilities: [
      FACILITIES.locker,
      FACILITIES.wifi,
      FACILITIES.shower,
      FACILITIES.toiletries,
      FACILITIES.slippers,
      FACILITIES.waterCoffee,
    ],
  },
  {
    terminal: 'KLIA Terminal 1',
    outlet: 'Capsule Transit Sleep Lounge',
    title: 'Capsule Transit Sleep Lounge',
    subtitle: 'KLIA Terminal 1, Public Area',
    overlay: 'rgba(0,0,0,0.5)',
    lotId: 3,
    isPublicArea: true,
    description:
      'Capsule hotel with showers and lockers, located in the public area.',
    location: 'Terminal 1',
    key: 'Sleep Lounge',
    roomTypes: [
      {
        name: 'Single Capsule',
        beds: 'Mixed Zone for 1 Adult',
        priceFrom: 'RM200',
      },
    ],
    facilities: [
      FACILITIES.locker,
      FACILITIES.wifi,
      FACILITIES.toiletries,
      FACILITIES.slippers,
      FACILITIES.waterCoffee,
    ],
  },
];

export type PriceOption = {
  hours: number;
  price: number;
};

export const ROOM_OPTIONS: {
  [k: string]: {
    description?: string;
    zones?: string[];
    roomSize?: string;
    capacity: string;
    bedType: string;
    prices: PriceOption[];
    loungePackages?: string[];
    facilities?: {
      icon: React.ReactNode;
      name: string;
    }[];
  };
} = {
  'landside_female single': {
    zones: ['Female Zone', 'Mixed Zone'],
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 135 },
      { hours: 6, price: 155 },
      { hours: 12, price: 175 },
      { hours: 24, price: 195 },
    ],
  },
  'landside_male single': {
    zones: ['Female Zone', 'Mixed Zone'],
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 135 },
      { hours: 6, price: 155 },
      { hours: 12, price: 175 },
      { hours: 24, price: 195 },
    ],
  },
  'landside_mixed single': {
    zones: ['Mixed Zone'],
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 135 },
      { hours: 6, price: 155 },
      { hours: 12, price: 175 },
      { hours: 24, price: 195 },
    ],
  },
  landside_queen: {
    capacity: '2 Adults',
    bedType: '1 Queen Bed',
    prices: [
      { hours: 3, price: 235 },
      { hours: 6, price: 265 },
      { hours: 12, price: 295 },
      { hours: 24, price: 315 },
    ],
  },
  landside_suite: {
    capacity: '3 Adults',
    bedType: '1 Queen Bed, 1 Single Bed',
    prices: [
      { hours: 3, price: 275 },
      { hours: 6, price: 305 },
      { hours: 12, price: 335 },
      { hours: 24, price: 365 },
    ],
  },
  'max_deluxe queen': {
    description:
      'Queen bed with private shower and ambient lighting. Just right for a restful pause.',
    capacity: '2 Adults',
    bedType: '2 Single Beds, Queen Bed, 1 King Bed',
    roomSize: '22 m²',
    prices: [
      { hours: 3, price: 260 },
      { hours: 6, price: 290 },
      { hours: 12, price: 330 },
      { hours: 24, price: 400 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'max_deluxe king': {
    description:
      'King bed with private shower and ambient lighting. Just right for a restful pause.',
    capacity: '2 Adults',
    bedType: '2 Single Beds, Queen Bed, 1 King Bed',
    roomSize: '22 m²',
    prices: [
      { hours: 3, price: 260 },
      { hours: 6, price: 290 },
      { hours: 12, price: 330 },
      { hours: 24, price: 400 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'max_deluxe twin': {
    description:
      'Twin bed with private shower and ambient lighting. Just right for a restful pause.',

    capacity: '2 Adults',
    bedType: '2 Single Beds, Queen Bed, 1 King Bed',
    roomSize: '22 m²',
    prices: [
      { hours: 3, price: 260 },
      { hours: 6, price: 290 },
      { hours: 12, price: 330 },
      { hours: 24, price: 400 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'max_executive king': {
    description:
      'King bed, ambient mood, private shower and a bathtub. A deeper reset for the frequent flyer.',
    capacity: '4 Adults',
    bedType: '1 King Bed, 1 Full-size Sofa Bed',
    roomSize: '32 m²',
    prices: [
      { hours: 3, price: 300 },
      { hours: 6, price: 340 },
      { hours: 12, price: 370 },
      { hours: 24, price: 450 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.bathTub,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'max_family room': {
    description: 'A room for family of 3, private shower and a bathtub.',
    capacity: '4 Adults',
    bedType: '1 King Bed, 1 Single Bed',
    roomSize: '32 m²',
    prices: [
      { hours: 3, price: 500 },
      { hours: 6, price: 540 },
      { hours: 12, price: 590 },
      { hours: 24, price: 680 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.bathTub,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'max_runway suite': {
    description:
      'King bed and a full-size sofa. With perfect runway view, a bathtub and more room to unwind.',
    capacity: '4 Adults',
    bedType: '1 King Bed, 1 Full-size Sofa Bed',
    roomSize: '42 m²',
    prices: [
      { hours: 3, price: 945 },
      { hours: 6, price: 1150 },
      { hours: 12, price: 1400 },
      { hours: 24, price: 1850 },
    ],
    loungePackages: [
      'Lounge Access',
      'A Plant-based Meal',
      'A Drink of Your Choice',
      'Free Flow Buffet Nibbles & Drinks',
      'Steam Bath Facilities',
      'Fully-Equipped Gym Facilities',
      'Meeting Room (upon request)',
    ],
    facilities: [
      FACILITIES.wifi,
      FACILITIES.airConditioning,
      FACILITIES.receptionDesk,
      FACILITIES.runwayView,
      FACILITIES.blackoutCurtains,
      FACILITIES.workDesk,
      FACILITIES.flatScreenTV,
      FACILITIES.wardrobeCloset,
      FACILITIES.inHouseSlippers,
      FACILITIES.dentalKit,
      FACILITIES.enSuiteBathroom,
      FACILITIES.rainShower,
      FACILITIES.bathTub,
      FACILITIES.toiletries,
      FACILITIES.showerTowel,
      FACILITIES.bathrobes,
      FACILITIES.bidetHairdryer,
      FACILITIES.bottledWater,
      FACILITIES.safetyDepositBox,
      FACILITIES.connectivityPanel,
      FACILITIES.teaCoffeeMakingFacilities,
    ],
  },
  'airside_mixed single': {
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 121.5 },
      { hours: 6, price: 162 },
      { hours: 12, price: 216 },
      { hours: 24, price: 270 },
    ],
  },
  'airside_female single': {
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 121.5 },
      { hours: 6, price: 162 },
      { hours: 12, price: 216 },
      { hours: 24, price: 270 },
    ],
  },
  'sleep lounge_mixed single': {
    capacity: '1 Adult',
    bedType: '1 Single Bed',
    prices: [
      { hours: 3, price: 112.5 },
      { hours: 6, price: 162.5 },
      { hours: 12, price: 233.5 },
      { hours: 24, price: 275 },
    ],
  },
};
