import useSWR from 'swr';
import { API_BASE_URL } from '@/config/app';
import axios from 'axios';

export const hotelDetailFetcher = async (lotId: number): Promise<any> => {
  if (!lotId) {
    return {};
  }

  const result = await axios
    .get(`${API_BASE_URL}/landing-page/lot-info/${lotId}`, {})
    .then((response) => {
      const detail = response.data.data || {};

      if (Object.keys(detail).length > 0) {
        detail.hotelDetailedLocation = detail.address;
        detail.hotelPhoneNumber = detail.tel;

        return detail;
      }

      return {};
    })
    .catch((error) => {
      console.error('error when fetching hotel detail', error);

      return {};
    });

  return result;
};

export const useGetHotelDetail = ({ lotId }: { lotId: number }) => {
  const { data, error, isLoading, mutate, isValidating } = useSWR<any, any>(
    `hotel-detail-${lotId}`, // Static cache key for global caching
    () => hotelDetailFetcher(lotId),
    {
      // Aggressive caching configuration for optimal performance
      revalidateOnFocus: false, // Don't refetch when window gets focus
      revalidateOnReconnect: true, // Refetch when connection is restored
      revalidateIfStale: false, // Don't refetch if data exists (even if stale)
      dedupingInterval: 5 * 60 * 1000, // 5 minutes - avoid duplicate requests
      refreshInterval: 0, // No automatic refresh
      errorRetryCount: 3, // Retry failed requests 3 times
      errorRetryInterval: 1000, // Wait 1 second between retries
      keepPreviousData: true, // Keep showing old data while fetching new data

      // Cache for 1 hour in the browser
      focusThrottleInterval: 60 * 60 * 1000, // 1 hour

      // Only consider data stale after 1 hour
      onSuccess: (data) => {
        // You can add any success callbacks here
        console.log(`Hotel detail loaded successfully by lotId: ${lotId}`);
      },

      onError: (error) => {
        console.error(
          `Failed to load hotel detail by lotId: ${lotId}`,
          error.message
        );
      },
    }
  );

  return {
    // Loading states
    isLoading, // Initial loading
    isValidating, // Background revalidation
    isRefreshing: isValidating && !isLoading, // Specifically for background refresh

    // Error state
    error,

    data,
    // Manual refetch function (rarely needed due to caching)
    refetch: mutate,

    // Cache status (useful for debugging)
    cacheKey: `hotel-detail-${lotId}`,
    lastUpdated: data ? new Date().toISOString() : null,
  };
};

// Default export
export default useGetHotelDetail;
