import useSWR from 'swr';
import { API_BASE_URL } from '@/config/app';
import axios from 'axios';

export type TaxAndServiceChargeResponse = {
  serviceChargeAmount?: `${number}%`;
  taxAmount?: `${number}%`;
};

export const taxAndServiceChargeFetcher = async (
  lotId: number
): Promise<TaxAndServiceChargeResponse> => {
  if (!lotId) {
    return {};
  }

  const result = await axios
    .get(
      `${API_BASE_URL}/landing-page/tax-and-service-charge/?lot=${lotId}`,
      {}
    )
    .then((response) => {
      const detail = response.data.data || {};

      return detail;
    })
    .catch((error) => {
      console.error('error when fetching tax and service charge', error);

      return {};
    });

  return result;
};

export const useGetTaxAndServiceChargeByLot = ({
  lotId,
}: {
  lotId: number;
}) => {
  const { data, error, isLoading, mutate, isValidating } = useSWR<
    TaxAndServiceChargeResponse,
    any
  >(
    `tax-and-service-charge-by-lot-${lotId}`, // Static cache key for global caching
    () => taxAndServiceChargeFetcher(lotId),
    {
      // Aggressive caching configuration for optimal performance
      revalidateOnFocus: false, // Don't refetch when window gets focus
      revalidateOnReconnect: true, // Refetch when connection is restored
      revalidateIfStale: false, // Don't refetch if data exists (even if stale)
      dedupingInterval: 5 * 60 * 1000, // 5 minutes - avoid duplicate requests
      refreshInterval: 0, // No automatic refresh
      errorRetryCount: 3, // Retry failed requests 3 times
      errorRetryInterval: 1000, // Wait 1 second between retries
      keepPreviousData: true, // Keep showing old data while fetching new data

      // Cache for 1 hour in the browser
      focusThrottleInterval: 60 * 60 * 1000, // 1 hour

      // Only consider data stale after 1 hour
      onSuccess: (data) => {
        // You can add any success callbacks here
        console.log(
          `Tax and service charge loaded successfully by lotId: ${lotId}`
        );
      },

      onError: (error) => {
        console.error(
          `Failed to load tax and service charge by lotId: ${lotId}`,
          error.message
        );
      },
    }
  );

  return {
    // Loading states
    isLoading, // Initial loading
    isValidating, // Background revalidation
    isRefreshing: isValidating && !isLoading, // Specifically for background refresh

    // Error state
    error,

    data,
    // Manual refetch function (rarely needed due to caching)
    refetch: mutate,

    // Cache status (useful for debugging)
    cacheKey: `tax-and-service-charge-by-lot-${lotId}`,
    lastUpdated: data ? new Date().toISOString() : null,
  };
};

// Default export
export default useGetTaxAndServiceChargeByLot;
