import useSWR from 'swr';
import { API_BASE_URL } from '@/config/app';
import axios from 'axios';
import { ICountry } from '@/models/Booking';

const filterDuplicateCountries = (countries: ICountry[]): ICountry[] => {
  const seen = new Set<string>();
  return countries.filter((country) => {
    const duplicate = seen.has(country.countryName);
    seen.add(country.countryName);
    return !duplicate;
  });
};

export const guestsCountryFetcher = async (): Promise<ICountry[]> => {
  const result = await axios
    .get(`${API_BASE_URL}/guests/country`, {})
    .then((response) => {
      const sortedCountry: ICountry[] = filterDuplicateCountries(
        response.data.data
      )
        .sort((prev: ICountry, curr: ICountry) =>
          prev.countryName.localeCompare(curr.countryName)
        )
        // .sort((prev: ICountry, curr: ICountry) =>
        //   prev.favorite === curr.favorite ? 0 : prev.favorite ? -1 : 1
        // )
        .sort((prev: ICountry, curr: ICountry) => {
          if (prev.countryName === 'Malaysia') {
            return -1; // 'Malaysia' comes before all other countries
          } else if (curr.countryName === 'Malaysia') {
            return 1; // 'Malaysia' comes before all other countries
          } else {
            return 0; // Maintain the existing order for other countries
          }
        });

      return sortedCountry;
    })
    .catch((error) => {
      console.error('error when fetching guests country', error);

      return [];
    });

  return result;
};

export const useGetGuestsCountry = () => {
  const { data, error, isLoading, mutate, isValidating } = useSWR<ICountry[], any>(
    `guests-country`, // Static cache key for global caching
    () => guestsCountryFetcher(),
    {
      // Aggressive caching configuration for optimal performance
      revalidateOnFocus: false, // Don't refetch when window gets focus
      revalidateOnReconnect: true, // Refetch when connection is restored
      revalidateIfStale: false, // Don't refetch if data exists (even if stale)
      dedupingInterval: 5 * 60 * 1000, // 5 minutes - avoid duplicate requests
      refreshInterval: 0, // No automatic refresh
      errorRetryCount: 3, // Retry failed requests 3 times
      errorRetryInterval: 1000, // Wait 1 second between retries
      keepPreviousData: true, // Keep showing old data while fetching new data

      // Cache for 1 hour in the browser
      focusThrottleInterval: 60 * 60 * 1000, // 1 hour

      // Only consider data stale after 1 hour
      onSuccess: (data) => {
        // You can add any success callbacks here
        console.log(`Guests country loaded successfully`);
      },

      onError: (error) => {
        console.error(
          `Failed to load guests country`,
          error.message
        );
      },
    }
  );

  return {
    // Loading states
    isLoading, // Initial loading
    isValidating, // Background revalidation
    isRefreshing: isValidating && !isLoading, // Specifically for background refresh

    // Error state
    error,

    data,
    // Manual refetch function (rarely needed due to caching)
    refetch: mutate,

    // Cache status (useful for debugging)
    cacheKey: `guests-country`,
    lastUpdated: data ? new Date().toISOString() : null,
  };
};

// Default export
export default useGetGuestsCountry;
