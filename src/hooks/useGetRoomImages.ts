import use<PERSON><PERSON> from 'swr';
import {
  RoomImagesApiResponse,
  RoomImagesError,
} from '@/actions/getRoomImages/types';
import { roomImagesFetcher } from '@/actions/getRoomImages';

/**
 * Hook for fetching room images with advanced caching
 * This hook leverages SWR's powerful caching to avoid unnecessary API calls
 */
export const useGetRoomImages = (options?: { isAutoFetch?: boolean }) => {
  const { data, error, isLoading, mutate, isValidating } = useSWR<
    RoomImagesApiResponse,
    RoomImagesError
  >(
    'room-images', // Static cache key for global caching
    options?.isAutoFetch ? () => roomImagesFetcher() : null,
    {
      isPaused: () => !options?.isAutoFetch,
      // Aggressive caching configuration for optimal performance
      revalidateOnFocus: false, // Don't refetch when window gets focus
      revalidateOnReconnect: true, // Refetch when connection is restored
      revalidateIfStale: false, // Don't refetch if data exists (even if stale)
      dedupingInterval: 5 * 60 * 1000, // 5 minutes - avoid duplicate requests
      refreshInterval: 0, // No automatic refresh
      errorRetryCount: 3, // Retry failed requests 3 times
      errorRetryInterval: 1000, // Wait 1 second between retries
      keepPreviousData: true, // Keep showing old data while fetching new data

      // Cache for 1 hour in the browser
      focusThrottleInterval: 60 * 60 * 1000, // 1 hour

      // Only consider data stale after 1 hour
      onSuccess: (data) => {
        // You can add any success callbacks here
        console.log(
          'Room images loaded successfully:',
          data.data.length,
          'room types'
        );
      },

      onError: (error) => {
        console.error('Failed to load room images:', error.message);
      },
    }
  );

  const getImagesByOutlet = (outlet: string) => {
    if (!data?.groupImagesByType) return [];

    const imagesByOutlet =
      data.groupImagesByType[String(outlet)?.toLowerCase()];

    if (!imagesByOutlet) return [];

    return Object.values(imagesByOutlet).flatMap((roomType) => roomType.images);
  };

  return {
    // Loading states
    isLoading, // Initial loading
    isValidating, // Background revalidation
    isRefreshing: isValidating && !isLoading, // Specifically for background refresh

    // Error state
    error,

    data,
    getImagesByOutlet,
    // Manual refetch function (rarely needed due to caching)
    refetch: mutate,

    // Cache status (useful for debugging)
    cacheKey: 'room-images',
    lastUpdated: data ? new Date().toISOString() : null,
  };
};

// Default export
export default useGetRoomImages;
