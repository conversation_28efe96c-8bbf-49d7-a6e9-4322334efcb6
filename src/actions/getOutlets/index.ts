import { OUTLETS } from '@/constant/outlet';
import { getImagesByOutlet, roomImagesFetcher } from '../getRoomImages';
import { Outlet } from './types';

export const getOutlets = async (): Promise<Outlet[]> => {
  const roomImages = await roomImagesFetcher();

  const outlets = OUTLETS.map((outlet) => {
    const outletImages = getImagesByOutlet(
      roomImages.groupImagesByType,
      outlet.key
    );

    return {
      ...outlet,
      images: outletImages,
      imagesByRoomType:
        roomImages.groupImagesByType[String(outlet.key).toLowerCase()] || {},
    };
  });

  return outlets as Outlet[];
};
