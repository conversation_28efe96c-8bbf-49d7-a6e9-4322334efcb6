import { TransformImage } from '../getRoomImages/types';

type RoomTypeName = string;

export interface Outlet {
  terminal: string;
  outlet: string;
  title: string;
  subtitle: string;
  overlay: string;
  lotId: number;
  isPublicArea?: boolean;
  isRestrictedArea?: boolean;
  location: string;
  isNewOpening: boolean;
  images: TransformImage[];
  description: string;
  key: string;
  roomTypes: { name: string; beds: string; priceFrom: string }[];
  facilities: { name: string; icon: any }[];
  imagesByRoomType?: {
    [key: RoomTypeName]: {
      images: TransformImage[];
      outlet: string;
      roomType: string;
    };
  };
}
