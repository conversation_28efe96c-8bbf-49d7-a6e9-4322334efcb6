import { unstable_cache } from 'next/cache';
import axios from 'axios';
import { BookingHotelReviewRequestParams, BookingReviewsApiResponse } from '../types/booking';

/**
 * Fetch reviews from the API (uncached version)
 */
// from graphql
const fetchReviewsFromAPI = async (params: BookingHotelReviewRequestParams): Promise<BookingReviewsApiResponse> => {
  const url = 'https://www.booking.com/dml/graphql';
  console.log('calling reviews api', url);
  try {
    const response = await axios.post(url, params.body, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
      params: params.params,
    });

    console.log('Booking reviewCard', response.data);

    return {
      ...(response.data || {}),
      platform: 'Booking',
      platformUrl: 'https://www.booking.com',
    };
  } catch (error: any) {
    console.error('Error fetching reviews:', error);

    // Return empty data structure on error to prevent app crashes
    return {
      platformUrl: 'https://www.booking.com',
      platform: 'Booking',
      data: {
        reviewListFrontend: {
          '__typename': 'ReviewListFrontendResult',
          sorters: [],
          reviewCard: [],
          ratingScores: [],
          languageFilter: [],
          reviewScoreFilter: [],
          customerTypeFilter: [],
          reviewsCount: 0,
          timeOfYearFilter: [],
          topicFilters: []
        }
      },
      extensions: {
        latency_insights: {
          ReviewList: {
              duration_ms: 0,
              end_time: 0,
              start_time: 0
          }
        }
      }
    };
  }
};

/**
 * Cached version of the reviews fetcher with 10-day cache duration
 */
const getCachedReviews = unstable_cache(
  async (params: BookingHotelReviewRequestParams) => {
    console.log('Fetching reviews from Booking API (cache miss)');
    return await fetchReviewsFromAPI(params);
  },
  ['booking-reviews'], // Cache key base
  {
    revalidate: 20 * 24 * 60 * 60, // 10 days in seconds
    tags: ['reviews'], // Cache tags for manual invalidation
  }
);

/**
 * Server action to get Agoda reviews with caching
 *
 * This server action fetches reviews with a 10-day cache duration.
 * It uses Next.js's unstable_cache for automatic cache management.
 */
export async function getBookingReviews(): Promise<BookingReviewsApiResponse> {
  const params = {
    body: {
      query:
        'query ReviewList($input: ReviewListFrontendInput!, $shouldShowReviewListPhotoAltText: Boolean = false) {\n  reviewListFrontend(input: $input) {\n    ... on ReviewListFrontendResult {\n      ratingScores {\n        name\n        translation\n        value\n        ufiScoresAverage {\n          ufiScoreLowerBound\n          ufiScoreHigherBound\n          __typename\n        }\n        __typename\n      }\n      topicFilters {\n        id\n        name\n        isSelected\n        translation {\n          id\n          name\n          __typename\n        }\n        __typename\n      }\n      reviewScoreFilter {\n        name\n        value\n        count\n        __typename\n      }\n      languageFilter {\n        name\n        value\n        count\n        countryFlag\n        __typename\n      }\n      timeOfYearFilter {\n        name\n        value\n        count\n        __typename\n      }\n      customerTypeFilter {\n        count\n        name\n        value\n        __typename\n      }\n      reviewCard {\n        reviewUrl\n        guestDetails {\n          username\n          avatarUrl\n          countryCode\n          countryName\n          avatarColor\n          showCountryFlag\n          anonymous\n          guestTypeTranslation\n          __typename\n        }\n        bookingDetails {\n          customerType\n          roomId\n          roomType {\n            id\n            name\n            __typename\n          }\n          checkoutDate\n          checkinDate\n          numNights\n          stayStatus\n          __typename\n        }\n        reviewedDate\n        isTranslatable\n        helpfulVotesCount\n        reviewScore\n        textDetails {\n          title\n          positiveText\n          negativeText\n          textTrivialFlag\n          lang\n          __typename\n        }\n        isApproved\n        partnerReply {\n          reply\n          __typename\n        }\n        positiveHighlights {\n          start\n          end\n          __typename\n        }\n        negativeHighlights {\n          start\n          end\n          __typename\n        }\n        editUrl\n        photos {\n          id\n          urls {\n            size\n            url\n            __typename\n          }\n          kind\n          mlTagHighestProbability @include(if: $shouldShowReviewListPhotoAltText)\n          __typename\n        }\n        __typename\n      }\n      reviewsCount\n      sorters {\n        name\n        value\n        __typename\n      }\n      __typename\n    }\n    ... on ReviewsFrontendError {\n      statusCode\n      message\n      __typename\n    }\n    __typename\n  }\n}\n',
      variables: {
        shouldShowReviewListPhotoAltText: true,
        input: {
          hotelId: 2590533,
          ufi: -2403350,
          hotelCountryCode: 'my',
          sorter: 'MOST_RELEVANT',
          filters: { text: '' },
          skip: 0,
          limit: 10,
          hotelScore: 8.3,
          upsortReviewUrl: '',
          searchFeatures: { destId: -2403350, destType: 'CITY' },
        },
      },
      operationName: 'ReviewList',
      extensions: {},
    },
    params: {
      label:
        'gen173nr-10CAsooQFCG2NhcHN1bGUtYnktY29udGFpbmVyLXNlcGFuZ0gJWARo9AGIAQGYATO4AQfIAQzYAQPoAQH4AQGIAgGoAgG4Avbog8YGwAIB0gIkYzQ0Njc1NDktOGRiZS00ZjQ1LWI1YTEtY2NlNTE1NzMzMjQ42AIB4AIB',
      sid: '7d1c8328a1ca6135d7b326fe407da841',
      dist: '0',
      keep_landing: '1',
      sb_price_type: 'total',
      type: 'total',
      lang: 'en-gb',
    },
  };

  try {
    console.log('Attempting to get reviews from cache or API');
    const reviews = await getCachedReviews(params);
    console.log(
      `Successfully fetched Booking reviews: ${
        reviews.data?.reviewListFrontend?.reviewCard?.length || 0
      } reviews`
    );
    return reviews;
  } catch (error) {
    console.error('Error in getBookingReviews server action:', error);
    // Return the empty structure on any error
    return await fetchReviewsFromAPI(params);
  }
}
