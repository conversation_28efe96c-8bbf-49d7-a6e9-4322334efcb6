import { unstable_cache } from 'next/cache';
import axios from 'axios';
import {
  TripReviewsApiResponse,
  TripHotelReviewRequestParams,
} from '../types/trip';

/**
 * Fetch reviews from the API (uncached version)
 */
const fetchReviewsFromAPI = async (
  params: TripHotelReviewRequestParams
): Promise<TripReviewsApiResponse> => {
  const url = 'https://www.trip.com/restapi/soa2/33269/getHotelCommentList';
  console.log('calling reviews api', url);
  try {
    const response = await axios.post(url, params, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    });

    return {
      ...(response.data || {}),
      platform: 'Trip',
      platformUrl: 'https://www.trip.com',
    };
  } catch (error: any) {
    console.error('Error fetching reviews:', error);

    // Return empty data structure on error to prevent app crashes
    return {
      platformUrl: 'https://www.trip.com',
      platform: 'Trip',
      data: {
        imageDomain: '',
        totalCount: 0,
        repeatCommentCount: 0,
        filterList: [],
        commentList: [],
        taCommentList: [],
        unUsefulCommentList: [],
        allCommentCount: 0,
        aISummaryList: [],
        totalCountForPage: 0,
      },
      ResponseStatus: {},
    };
  }
};

/**
 * Cached version of the reviews fetcher with 10-day cache duration
 */
const getCachedReviews = unstable_cache(
  async (params: TripHotelReviewRequestParams) => {
    console.log('Fetching reviews from Trip API (cache miss)');
    return await fetchReviewsFromAPI(params);
  },
  ['trip-reviews'], // Cache key base
  {
    revalidate: 20 * 24 * 60 * 60, // 10 days in seconds
    tags: ['reviews'], // Cache tags for manual invalidation
  }
);

/**
 * Server action to get Agoda reviews with caching
 *
 * This server action fetches reviews with a 10-day cache duration.
 * It uses Next.js's unstable_cache for automatic cache management.
 */
export async function getTripReviews(): Promise<TripReviewsApiResponse> {
  const params: TripHotelReviewRequestParams = {
    hotelId: 1320098,
    pageIndex: 1,
    pageSize: 10,
    repeatComment: 1,
    needStaticInfo: false,
    functionOptions: [
      'IntegratedTARating',
      'hidePicAndVideoAgg',
      'TripReviewsToServerOnline',
      'IntegratedExpediaList',
      'tripShuffled',
      'taAdvisorCount',
      'filterComment',
      'noShowNewExpedia',
    ],
    head: {
      platform: 'PC',
      cver: '0',
      cid: '1757435385663.90f2hAXzzXgA',
      bu: 'IBU',
      group: 'trip',
      aid: '',
      sid: '',
      ouid: '',
      locale: 'en-XX',
      timezone: '7',
      currency: 'USD',
      pageId: '10320668147',
      vid: '1757435385663.90f2hAXzzXgA',
      guid: '',
      isSSR: false,
    },
  };

  try {
    console.log('Attempting to get reviews from cache or API');
    const reviews = await getCachedReviews(params);
    console.log(
      `Successfully fetched Trip reviews: ${
        reviews.data?.commentList?.length || 0
      } reviews`
    );
    return reviews;
  } catch (error) {
    console.error('Error in getTripReviews server action:', error);
    // Return the empty structure on any error
    return await fetchReviewsFromAPI(params);
  }
}
