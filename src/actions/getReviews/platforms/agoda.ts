
import { unstable_cache } from 'next/cache';
import axios from 'axios';
import { AgodaReviewsApiResponse, AgodaHotelReviewRequestParams } from '../types/agoda';

/**
 * Fetch reviews from the API (uncached version)
 */
const fetchReviewsFromAPI = async (
  params: AgodaHotelReviewRequestParams
): Promise<AgodaReviewsApiResponse> => {
  const url = 'https://www.agoda.com/api/cronos/property/review/HotelReviews';
  console.log('calling reviews api', url);
  try {
    const response = await axios.post(url, params, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    });

    return {
      ...(response.data || {}),
      platform: 'Agoda',
      platformUrl: 'https://www.agoda.com',
    };
  } catch (error: any) {
    console.error('Error fetching reviews:', error);

    // Return empty data structure on error to prevent app crashes
    return {
      platformUrl: 'https://www.agoda.com',
      platform: 'Agoda',
      hasReviewsData: false,
      pageSize: 0,
      hotelId: 0,
      hotelName: '',
      additionalReviewProviders: [],
      recentReviewScores: [],
      combinedReview: {
        isShowCombinedRating: false,
        score: {
          maxScore: 0,
          providerId: 0,
          reviewCommentsCount: 0,
          reviewCount: 0,
          score: 0,
          demographic: '',
          formattedReviewCount: '',
          formattedScore: '',
          scoreText: '',
        },
        providers: [],
        grades: [],
      },
      commentList: {
        reviewPageUrl: '',
        currentPage: 0,
        hotelID: 0,
        pageSize: 0,
        selectedSortOption: 0,
        haveMoreThanOneComments: false,
        isCrawlablePage: false,
        isCurated: false,
        isReviewPage: false,
        shouldShowProminentLink: false,
        pageNumberToPageUrl: {},
        reviewsSortOptions: {
          '1': 'Most recent',
          '2': 'Rating, high to low',
          '3': 'Rating, low to high',
          '7': 'Most helpful',
        },
        comments: [],
      },
      facilityClassWordsCloud: {
        reviewWordCloud: [],
        hasReviewWordCloud: false,
      },
    };
  }
};

/**
 * Cached version of the reviews fetcher with 10-day cache duration
 */
const getCachedReviews = unstable_cache(
  async (params: AgodaHotelReviewRequestParams) => {
    console.log('Fetching reviews from API (cache miss)');
    return await fetchReviewsFromAPI(params);
  },
  ['agoda-reviews'], // Cache key base
  {
    revalidate: 20 * 24 * 60 * 60, // 10 days in seconds
    tags: ['reviews'], // Cache tags for manual invalidation
  }
);

/**
 * Server action to get Agoda reviews with caching
 *
 * This server action fetches reviews with a 10-day cache duration.
 * It uses Next.js's unstable_cache for automatic cache management.
 */
export async function getAgodaReviews(): Promise<AgodaReviewsApiResponse> {
  const params: AgodaHotelReviewRequestParams = {
    hotelId: 625831,
    hotelProviderId: 332,
    demographicId: 0,
    pageNo: 1,
    pageSize: 5,
    sorting: 7,
    reviewProviderIds: [
      332, 3038, 27901, 28999, 29100, 27999, 27980, 27989, 29014,
    ],
    isReviewPage: false,
    isCrawlablePage: true,
    paginationSize: 10,
  };

  try {
    console.log('Attempting to get reviews from cache or API');
    const reviews = await getCachedReviews(params);
    console.log(
      `Successfully fetched reviews: ${
        reviews.commentList?.comments?.length || 0
      } reviews`
    );
    return reviews;
  } catch (error) {
    console.error('Error in getAgodaReviews server action:', error);
    // Return the empty structure on any error
    return await fetchReviewsFromAPI(params);
  }
}