import { Comment as AgodaComment } from './agoda';
import { ReviewCard as BookingComment } from './booking';
import { Comment as TripComment } from './trip';

export interface ReviewsError {
  message: string;
  status?: number;
  code?: string;
}

export interface ReviewData {
  reviewPageUrl: string;
  title: string;
  content: string;
  rating: number;
  ratingText: string;
  checkInDate: string;
  platform: 'Agoda' | 'Trip' | 'Booking' | string;
  platformUrl: string;
  reviewerInfo: {
    countryName: string;
    reviewerName: string;
    travelTypeText: string;
  };
  detail: AgodaComment | TripComment | BookingComment;
  [k: string]: any;
}

export interface ReviewResponse {
  data: ReviewData[];
}
