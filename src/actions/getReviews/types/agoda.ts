// If you want to make some fields optional, you can create a partial version:
export type AgodaHotelReviewRequestParams = {
  hotelId: number;
  hotelProviderId?: number;
  demographicId?: number;
  pageNo?: number;
  pageSize?: number;
  sorting?: number;
  reviewProviderIds?: number[];
  isReviewPage?: boolean;
  isCrawlablePage?: boolean;
  paginationSize?: number;
};

export type RecentReviewScore = {
  providerId: number;
  recentReviewScores: number[];
  recentReviewScoresFormatted: string[];
};

export type CombineReviewProvider = {
  providerId: number;
  logoUrl: string;
  logoName: string;
  logoAltText: string;
};

export type CombineReviewGrade = {
  id: string;
  name: string;
  score: number;
  scoreText: string;
  formattedScore: string;
};

type ReviewerInfo = {
  countryName: string;
  displayMemberName: string;
  flagName: string;
  reviewGroupName: string;
  roomTypeName: string;
  countryId: number;
  lengthOfStay: number;
  reviewGroupId: number;
  roomTypeId: number;
  reviewerReviewedCount: number;
  isExpertReviewer: boolean;
  isShowGlobalIcon: boolean;
  isShowReviewedCount: boolean;
};

export type Comment = {
  isHelpfulComment: boolean;
  isReviewVoted: boolean;
  isShowReviewResponse: boolean;
  isShowReviewResponseTranslateButton: boolean;
  isShowReviewTranslateButton: boolean;
  helpfulVotes: number;
  responseLanguageId: number;
  unHelpfulVotes: number;
  hotelReviewId: number;
  providerId: number;
  rating: number;
  checkInDateMonthAndYear: string;
  encryptedReviewData: string;
  formattedRating: string;
  formattedReviewDate: string;
  formattedReviewHelpfulText: string;
  ratingText: string;
  responderName: string;
  responseDateText: string;
  responseTranslateSource: string;
  reviewComments: string;
  reviewNegatives: string;
  reviewPositives: string;
  reviewProviderLogo: string;
  reviewProviderText: string;
  reviewTitle: string;
  translateSource: string;
  translateTarget: string;
  checkInDate: string; // ISO date string
  checkOutDate: string; // ISO date string
  reviewDate: string; // ISO date string
  reviewerInfo: ReviewerInfo;
  originalTitle: string;
  originalComment: string;
  formattedResponseDate: string;
};

type WordCloudItem = {
  count: number;
  filterTypeId: number;
  hotelId: number;
  wordsCloudId: number;
  representors: string;
  formattedCount: string;
};

type ReviewWordCloud = {
  wordCloudList: WordCloudItem[];
  hasWordCloud: boolean;
  providerId: number;
};

type FacilityClassWordsCloud = {
  reviewWordCloud: ReviewWordCloud[];
  hasReviewWordCloud: boolean;
};

export interface AgodaReviewsApiResponse {
  platformUrl?: string;
  platform?: string;
  hasReviewsData: boolean;
  pageSize: number;
  hotelId: number;
  hotelName: string;
  additionalReviewProviders: number[];
  recentReviewScores: RecentReviewScore[];
  combinedReview: {
    isShowCombinedRating: boolean;
    score: {
      maxScore: number;
      providerId: number;
      reviewCommentsCount: number;
      reviewCount: number;
      score: number;
      demographic: string;
      formattedReviewCount: string;
      formattedScore: string;
      scoreText: string;
    };
    providers: CombineReviewProvider[];
    grades: CombineReviewGrade[];
  };
  commentList: {
    reviewPageUrl: string;
    currentPage: number;
    hotelID: number;
    pageSize: number;
    selectedSortOption: number;
    haveMoreThanOneComments: boolean;
    isCrawlablePage: boolean;
    isCurated: boolean;
    isReviewPage: boolean;
    shouldShowProminentLink: boolean;
    pageNumberToPageUrl: {};
    reviewsSortOptions: {
      '1': 'Most recent';
      '2': 'Rating, high to low';
      '3': 'Rating, low to high';
      '7': 'Most helpful';
    };
    comments: Comment[];
  };
  facilityClassWordsCloud: FacilityClassWordsCloud;
  score?: {
    reviewPageUrl?: string;
    [k: string]: any;
  };
}
