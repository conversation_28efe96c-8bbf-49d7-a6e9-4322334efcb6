export type BookingHotelReviewRequestParams = {
  body: {
    query: string;
    variables: Record<string, any>;
    operationName: string;
    extensions: {};
  };
  params: Record<string, string>;
};

// Main response interface
export interface BookingReviewsApiResponse {
  platform?: 'Booking';
  platformUrl?: string;
  data: {
    reviewListFrontend: ReviewListFrontendResult;
  };
  extensions: {
    latency_insights: {
      ReviewList: {
        duration_ms: number;
        end_time: number;
        start_time: number;
      };
    };
  };
}

// Core review list result
interface ReviewListFrontendResult {
  __typename: 'ReviewListFrontendResult';
  sorters: ReviewSorter[];
  reviewCard: ReviewCard[];
  ratingScores: RatingScore[];
  languageFilter: LanguageFilter[];
  reviewScoreFilter: ReviewScoreFilter[];
  customerTypeFilter: CustomerTypeFilter[];
  reviewsCount: number;
  timeOfYearFilter: TimeOfYearFilter[];
  topicFilters: TopicFilter[];
}

// Review sorter interface
interface ReviewSorter {
  __typename: 'ReviewSorter';
  name: string;
  value:
    | 'MOST_RELEVANT'
    | 'NEWEST_FIRST'
    | 'OLDEST_FIRST'
    | 'SCORE_DESC'
    | 'SCORE_ASC';
}

// Main review card interface
export interface ReviewCard {
  __typename: 'ReviewCard';
  reviewScore: number;
  guestDetails: GuestDetails;
  negativeHighlights: string | null;
  textDetails: TextDetails;
  isTranslatable: boolean;
  isApproved: boolean;
  reviewUrl: string;
  photos: ReviewPhoto[] | null;
  bookingDetails: BookingDetails;
  reviewedDate: number;
  editUrl: string | null;
  helpfulVotesCount: number;
  partnerReply: string | null;
  positiveHighlights: string | null;
}

// Guest details interface
interface GuestDetails {
  __typename: 'GuestDetails';
  showCountryFlag: boolean;
  avatarUrl: string;
  countryName: string;
  guestTypeTranslation: 'Solo traveller' | 'Group' | 'Family' | 'Couple';
  countryCode: string;
  avatarColor: string | null;
  username: string;
  anonymous: boolean;
}

// Text details interface
interface TextDetails {
  __typename: 'TextDetails';
  textTrivialFlag: number;
  positiveText: string;
  negativeText: string | null;
  lang: string;
  title: string | null;
}

// Review photo interface
interface ReviewPhoto {
  __typename: 'ReviewPhoto';
  id: number;
  mlTagHighestProbability: number;
  urls: ReviewPhotoUrl[];
  kind: 'PROPERTY';
}

interface ReviewPhotoUrl {
  __typename: 'ReviewPhotoUrl';
  url: string;
  size: 'max1280x900' | 'square80' | 'square160';
}

// Booking details interface
interface BookingDetails {
  __typename: 'BookingDetails';
  checkoutDate: string;
  stayStatus: 'stayed';
  roomType: RoomTranslation;
  roomId: number;
  numNights: number;
  customerType:
    | 'SOLO_TRAVELLERS'
    | 'GROUP_OF_FRIENDS'
    | 'FAMILIES'
    | 'COUPLES'
    | 'BUSINESS_TRAVELLERS';
  checkinDate: string;
}

interface RoomTranslation {
  __typename: 'RoomTranslation';
  id: string;
  name: string;
}

// Rating score interface
interface RatingScore {
  __typename: 'RatingScore';
  name:
    | 'hotel_staff'
    | 'hotel_services'
    | 'hotel_clean'
    | 'hotel_comfort'
    | 'hotel_value'
    | 'hotel_location'
    | 'hotel_free_wifi';
  value: number;
  translation: string;
  ufiScoresAverage: UfiScoreAverage;
}

interface UfiScoreAverage {
  __typename: 'UfiScoreAverage';
  ufiScoreLowerBound: number;
  ufiScoreHigherBound: number;
}

// Language filter interface
interface LanguageFilter {
  __typename: 'LanguageFilter';
  countryFlag: string | null;
  count: number;
  value: string;
  name: string;
}

// Review score filter interface
interface ReviewScoreFilter {
  __typename: 'ReviewScoreFilter';
  name: string;
  value:
    | 'ALL'
    | 'REVIEW_ADJ_SUPERB'
    | 'REVIEW_ADJ_GOOD'
    | 'REVIEW_ADJ_AVERAGE_PASSABLE'
    | 'REVIEW_ADJ_POOR'
    | 'REVIEW_ADJ_VERY_POOR';
  count: number;
}

// Customer type filter interface
interface CustomerTypeFilter {
  __typename: 'CustomerTypeFilter';
  count: number;
  name: string;
  value:
    | 'ALL'
    | 'FAMILIES'
    | 'COUPLES'
    | 'GROUP_OF_FRIENDS'
    | 'SOLO_TRAVELLERS'
    | 'BUSINESS_TRAVELLERS';
}

// Time of year filter interface
interface TimeOfYearFilter {
  __typename: 'TimeOfYearFilter';
  name: string;
  value: 'ALL' | '_03_05' | '_06_08' | '_09_11' | '_12_02';
  count: number;
}

// Topic filter interface
interface TopicFilter {
  __typename: 'TopicFilter';
  isSelected: boolean;
  name: string;
  id: number;
  translation: string | null;
}
