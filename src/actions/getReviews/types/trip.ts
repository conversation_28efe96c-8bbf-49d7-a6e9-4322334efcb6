export interface TripHotelReviewRequestParams {
  hotelId: number;
  pageIndex: number;
  pageSize: number;
  repeatComment: number;
  needStaticInfo: boolean;
  functionOptions: string[];
  head: {
    platform: string;
    cver: string;
    cid: string;
    bu: string;
    group: string;
    aid: string;
    sid: string;
    ouid: string;
    locale: string;
    timezone: string;
    currency: string;
    pageId: string;
    vid: string;
    guid: string;
    isSSR: boolean;
  };
}

export interface TripReviewsApiResponse {
  platform?: 'Trip';
  platformUrl?: string;
  data: {
    imageDomain: string;
    totalCount: number;
    repeatCommentCount: number;
    filterList: FilterItem[];
    commentList: Comment[];
    taCommentList: any[];
    unUsefulCommentList: any[];
    allCommentCount: number;
    aISummaryList: AISummaryItem[];
    totalCountForPage: number;
  };
  ResponseStatus: {
    Timestamp?: string;
    Ack?: string;
    Errors?: any[];
    Build?: string;
    Version?: string;
    Extension?: Extension[];
  };
}

interface FilterItem {
  id: number;
  name: string;
  commentCount: number;
  filterType: number;
  type?: number;
}

export interface Comment {
  id: number;
  usefulCount: number;
  source: number;
  language: string;
  canMarkUseful: boolean;
  checkInDate: string;
  content: string;
  createDate: string;
  imageList: string[];
  rating: number;
  ratingMax: number;
  commentLevel: string;
  roomTypeName: string;
  travelType: number;
  travelTypeText: string;
  feedbackList: any[];
  userInfo: UserInfo;
  videoList: any[];
  ratingInfo: RatingInfo;
}

interface UserInfo {
  headPictureUrl: string;
  commentCount: number;
  nickName: string;
}

interface RatingInfo {
  commentLevel: string;
  ratingAll: string;
  ratingMax: number;
}

interface AISummaryItem {
  type: string;
  icon: string;
  title: string;
  content: string;
  descHover?: string;
}

interface Extension {
  Id: string;
  Value: string;
}
