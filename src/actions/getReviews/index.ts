'use server';

import { getAgodaReviews } from './platforms/agoda';
import { getBookingReviews } from './platforms/booking';
import { getTripReviews } from './platforms/trip';
import { ReviewResponse } from './types';

const getFromRatingNumber = () => {
  const envValue = process.env.FROM_RATING_NUMBER;
  const defaultValue = 8.8;

  if (!envValue) {
    return defaultValue;
  }

  const parsed = parseFloat(envValue);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Server action to get Agoda reviews with caching
 *
 * This server action fetches reviews with a 10-day cache duration.
 * It uses Next.js's unstable_cache for automatic cache management.
 */

export async function getReviews(): Promise<ReviewResponse> {
  const [agodaReviews, tripReviews, bookingReviews] = await Promise.all([
    getAgodaReviews(),
    getTripReviews(),
    getBookingReviews(),
  ]);

  const data: ReviewResponse['data'] = [];

  const FROM_RATING_NUMBER = getFromRatingNumber();

  if (agodaReviews?.commentList?.comments?.length) {
    const reviewPageUrl = agodaReviews.commentList.reviewPageUrl
      ? agodaReviews.commentList.reviewPageUrl
      : '';
    agodaReviews.commentList.comments.forEach((comment) => {
      const content = comment.reviewComments || comment.reviewPositives;
      if (
        comment.rating >= FROM_RATING_NUMBER &&
        !!content &&
        !String(content)
          .toLowerCase()
          .includes('this guest did not leave comments about this hotel')
      ) {
        data.push({
          reviewPageUrl: reviewPageUrl.includes('https://www.agoda.com')
            ? reviewPageUrl
            : `https://www.agoda.com${reviewPageUrl}`,
          platform: 'Agoda',
          platformUrl: 'https://www.agoda.com',
          checkInDate: comment.checkInDate,
          title: comment.reviewTitle,
          content: comment.reviewComments || comment.reviewPositives,
          rating: comment.rating,
          ratingText: comment.ratingText,
          detail: comment,
          reviewerInfo: {
            countryName: comment.reviewerInfo?.countryName,
            reviewerName: comment.reviewerInfo?.displayMemberName,
            travelTypeText: comment.reviewerInfo?.reviewGroupName,
          },
        });
      }
    });
  }

  if (tripReviews?.data?.commentList?.length) {
    tripReviews.data.commentList.forEach((comment) => {
      if (comment.rating >= FROM_RATING_NUMBER) {
        data.push({
          reviewPageUrl:
            'https://www.trip.com/hotels/v2/sepang-hotel-detail-1320098',
          platform: 'Trip',
          platformUrl: 'https://www.trip.com',
          checkInDate: comment.checkInDate,
          title: '',
          content: comment.content,
          rating: comment.rating,
          ratingText: comment.ratingInfo?.commentLevel || '',
          detail: comment,
          reviewerInfo: {
            reviewerName: comment.userInfo?.nickName,
            countryName: '',
            travelTypeText: comment.travelTypeText,
          },
        });
      }
    });
  }

  if(bookingReviews?.data?.reviewListFrontend?.reviewCard?.length) {
    bookingReviews.data.reviewListFrontend?.reviewCard.forEach((comment) => {
      if (comment.reviewScore >= FROM_RATING_NUMBER && !!comment.textDetails?.positiveText) {
        data.push({
          reviewPageUrl:
            'https://www.booking.com/hotel/my/capsule-by-container-sepang.en-gb.html',
          platform: 'Booking',
          platformUrl: 'https://www.booking.com',
          checkInDate: comment.bookingDetails?.checkinDate,
          title: comment.textDetails?.title || '',
          content: comment.textDetails?.positiveText,
          rating: comment.reviewScore,
          ratingText: String(comment.reviewScore || '') || '',
          detail: comment,
          reviewerInfo: {
            reviewerName: comment.guestDetails?.username,
            countryName: comment.guestDetails?.countryName,
            travelTypeText: comment.guestDetails?.guestTypeTranslation,
          },
        });
      }
    });
  }

  // Randomly shuffle the data array using Fisher-Yates algorithm
  for (let i = data.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [data[i], data[j]] = [data[j], data[i]];
  }

  return {
    data,
  };
}

/**
 * Server action to invalidate reviews cache manually
 * Call this function when you need to force refresh the reviews data
 */
export async function invalidateReviewsCache(): Promise<void> {
  'use server';

  const { revalidateTag } = await import('next/cache');
  revalidateTag('reviews');
  console.log('Reviews cache invalidated');
}
