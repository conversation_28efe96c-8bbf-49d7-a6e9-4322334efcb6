import { STRAPI_BASE_URL, STRAPI_TOKEN } from '@/config/app';
import axios from 'axios';
import { RoomImagesApiResponse } from './types';
import { unstable_cache } from 'next/cache';

export const buildRoomImagesApiUrl = (): string => {
  return `${STRAPI_BASE_URL}/api/room-type-images?populate=*&pagination[pageSize]=1000`;
};

const getFullImageUrl = (url?: string) => {
  if (!url) return '';

  return `${STRAPI_BASE_URL}${url}`;
};

export const getImagesByOutlet = (
  groupImagesByType: RoomImagesApiResponse['groupImagesByType'],
  outlet: string
) => {
  if (!groupImagesByType) return [];

  const imagesByOutlet = groupImagesByType[String(outlet)?.toLowerCase()];

  if (!imagesByOutlet) return [];

  return Object.values(imagesByOutlet).flatMap((roomType) => roomType.images);
};

/**
 * Custom fetcher function for room images API with Strapi authentication
 */
export const roomImagesFetcher = async (): Promise<RoomImagesApiResponse> => {
  const url = buildRoomImagesApiUrl();

  const result = await axios
    .get(url, {
      headers: {
        Authorization: `Bearer ${STRAPI_TOKEN}`,
      },
    })
    .then((response) => {
      const images = (response.data.data ||
        []) as RoomImagesApiResponse['data'];
      const groupByRoomType = images.reduce<
        RoomImagesApiResponse['groupImagesByType']
      >((grp, image) => {
        if (image.attributes?.title) {
          const [outlet, roomType] = image.attributes.title.split('-');
          if (outlet && roomType) {
            const normalizedRoomType = roomType.replace(/\d+$/, '').trim();
            if (!grp[outlet]) {
              grp[outlet] = {};
            }

            if (!grp[outlet][normalizedRoomType]) {
              grp[outlet][normalizedRoomType] = {
                outlet,
                roomType: normalizedRoomType,
                images: [],
              };
            }

            grp[outlet][normalizedRoomType].images.push({
              name: image.attributes.title,
              url: `${STRAPI_BASE_URL}${image.attributes.image.data.attributes.url}`,
              formats: {
                large: getFullImageUrl(
                  image.attributes?.image?.data?.attributes?.formats?.large?.url
                ),
                medium: getFullImageUrl(
                  image.attributes?.image?.data?.attributes?.formats?.medium
                    ?.url
                ),
                small: getFullImageUrl(
                  image.attributes?.image?.data?.attributes?.formats?.small?.url
                ),
                thumbnail: getFullImageUrl(
                  image.attributes?.image?.data?.attributes?.formats?.thumbnail
                    ?.url
                ),
              },
              beds: image.attributes.beds,
              ranking: image.attributes.ranking,
              imageData: image,
            });
          }
        }

        return grp;
      }, {});

      return {
        ...(response.data || {}),
        groupImagesByType: groupByRoomType,
      };
    })
    .catch((error) => {
      console.error('error get room images', error);

      return {
        data: [],
        meta: { pagination: { page: 1, pageCount: 1, pageSize: 1, total: 0 } },
        groupImagesByType: {},
      };
    });

  return result;
};

/**
 * Cached version of the room images fetcher with 10-day cache duration
 */
const getCachedRoomImages = unstable_cache(
  async () => {
    console.log('Fetching room images from API (cache miss)');
    return await roomImagesFetcher();
  },
  ['room-images'], // Cache key base
  {
    revalidate: 1 * 24 * 60 * 60, // 10 days in seconds
    tags: ['room-images'], // Cache tags for manual invalidation
  }
);

/**
 * Server action to get room images with caching
 *
 * This server action fetches room images with a 1-day cache duration.
 * It uses Next.js's unstable_cache for automatic cache management.
 */
export async function getRoomImages(): Promise<RoomImagesApiResponse> {
  try {
    console.log('Attempting to get room images from cache or API');
    const roomImages = await getCachedRoomImages();
    console.log(`Successfully fetched room images`);
    return roomImages;
  } catch (error) {
    console.error('Error in getRoomImages server action:', error);
    // Return the empty structure on any error
    return await roomImagesFetcher();
  }
}
