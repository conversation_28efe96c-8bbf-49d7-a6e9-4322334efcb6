
export interface Metadata {
    pagination: {
      page: number;
      pageCount: number;
      pageSize: number;
      total: number;
    };
  }
  // TypeScript interfaces for room images API response
  export interface StrapiImageFormat {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    name: string;
    path: string | null;
    size: number;
    width: number;
    height: number;
    sizeInBytes: number;
  }
  
  export interface RoomTypeImageAttributes {
    beds: any;
    title: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    ranking: number;
    image: {
      data: {
        id: number;
        attributes: {
          alternativeText?: string;
          caption?: string;
          createdAt: string;
          updatedAt: string;
          url: string;
          width: number;
          height: number;
          size: number;
          hash: string;
          mime: string;
          name: string;
          ext: string;
          formats: {
            large: StrapiImageFormat;
            medium: StrapiImageFormat;
            small: StrapiImageFormat;
            thumbnail: StrapiImageFormat;
          };
        };
      };
    };
  }
  
  export interface RoomTypeImageData {
    id: number;
    attributes: RoomTypeImageAttributes;
  }
  
  export interface TransformImage {
    name: string;
    url: string;
    beds: any;
    ranking: number;
    imageData: RoomTypeImageData;
    formats: {
      large: string;
      medium: string;
      small: string;
      thumbnail: string;
    };
  }
  
  export interface RoomImagesApiResponse {
    groupImagesByType: {
      [k: string]: {
        [k: string]: {
          outlet: string;
          roomType: string;
          images: TransformImage[];
        };
      };
    };
    data: RoomTypeImageData[];
    meta: Metadata;
  }
  
  // Error type
  export interface RoomImagesError {
    message: string;
    status?: number;
  }
  