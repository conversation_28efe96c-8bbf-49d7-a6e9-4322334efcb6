'use client';

import { IBookingInformation } from '@/models/Booking';
import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

type BookingProviderType = {
  bookingData: IBookingInformation | null;
  setBookingData: React.Dispatch<
    React.SetStateAction<IBookingInformation | null>
  >;
  getBookingData: () => IBookingInformation | null;
};

const BookingContext = createContext<BookingProviderType | undefined>(
  undefined
);

// Custom hook to use the context
export const useBookingData = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBookingData must be used within a MyProvider');
  }
  return context;
};

// Create a provider
export const BookingProvider = ({
  children,
}: {
  children?: React.ReactNode;
}) => {
  const [bookingData, setBookingData] = useState<IBookingInformation | null>(
    null
  );

  const getBookingData = useCallback(() => {
    try {
      const savedData = localStorage.getItem('bookingData');
      return savedData ? JSON.parse(savedData) : null;
    } catch (error) {
      return null;
    }
  }, []);

  useEffect(() => {
    const data = getBookingData();
    setBookingData(data);
  }, [getBookingData]);

  useEffect(() => {
    if (bookingData !== null) {
      localStorage.setItem('bookingData', JSON.stringify(bookingData));
    }
  }, [bookingData]);

  return (
    <BookingContext.Provider value={{ bookingData, setBookingData, getBookingData }}>
      {children}
    </BookingContext.Provider>
  );
};
