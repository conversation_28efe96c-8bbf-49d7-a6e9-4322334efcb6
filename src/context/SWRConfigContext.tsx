// app/providers.tsx
'use client';

import { SWRConfig } from 'swr';
import { ReactNode } from 'react';

export default function SWRProviders({ children }: { children: ReactNode }) {
  return (
    <SWRConfig
      value={{
        fetcher: (url: string) => fetch(url).then((res) => res.json()),
        dedupingInterval: 2000, // avoid repeated calls within 2s
        revalidateOnFocus: true, // refresh data when window is focused
      }}
    >
      {children}
    </SWRConfig>
  );
}
