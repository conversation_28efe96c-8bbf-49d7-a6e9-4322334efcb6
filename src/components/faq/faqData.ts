import IconArrow from "@/assets/icons/general/btn-icon-arrow-left.svg";

import MemberIcon from "./images/icon-faq-member.svg";
import GeneralIcon from "./images/icon-faq-general.svg";
import LocationIcon from "./images/icon-faq-location.svg";
import BookingIcon from "./images/icon-faq-booking.svg";
import CheckInOutIcon from "./images/icon-faq-checkin-out.svg";
// import ParkingIcon from "./images/icon-faq-parking.svg";
import BillingIcon from "./images/icon-faq-billing.svg";
// import ContactlessIcon from "./images/icon-faq-.svg";
import BagsIcon from "./images/icon-faq-luggage.svg";
import FacilitiesIcon from "./images/icon-faq-facilities.svg";
import SpecialRequestIcon from "./images/icon-faq-special-requests.svg";
import { Stack } from "@mui/material";

import {
  Airside1,
  Airside10,
  Airside11,
  Airside12,
  Airside13,
  Airside14,
  Airside15,
  <PERSON>side16,
  <PERSON>side2,
  <PERSON>side3,
  <PERSON>side4,
  <PERSON>side5,
  <PERSON>side6,
  Airside7,
  Airside8,
  Airside9,
  Billing1,
  Billing2,
  Billing3,
  Billing4,
  Billing5,
  Billing6,
  Booking1,
  Booking2,
  Booking3,
  Booking4,
  Booking5,
  Booking6,
  Booking7,
  Booking8,
  Booking9,
  General1,
  General2,
  General3,
  Landside1,
  Landside10,
  Landside11,
  Landside12,
  Landside13,
  Landside14,
  Landside15,
  Landside16,
  Landside17,
  Landside18,
  Landside19,
  Landside2,
  Landside3,
  Landside4,
  Landside5,
  Landside6,
  Landside7,
  Landside8,
  Landside9,
  MAX1,
  MAX10,
  MAX11,
  MAX12,
  MAX13,
  MAX14,
  MAX15,
  MAX16,
  MAX2,
  MAX3,
  MAX4,
  MAX5,
  MAX6,
  MAX7,
  MAX8,
  MAX9,
  SleepLounge1,
  SleepLounge10,
  SleepLounge11,
  SleepLounge12,
  SleepLounge13,
  SleepLounge14,
  SleepLounge2,
  SleepLounge3,
  SleepLounge4,
  SleepLounge5,
  SleepLounge6,
  SleepLounge7,
  SleepLounge8,
  SleepLounge9,
} from "./faqContentsData";

export const FAQs = [
  {
    title: "General",
    description: "Announcements, General Hotel Informations, FAQs",
    icon: GeneralIcon,
    qna: [General1, General2, General3],
  },
  {
    title: "Landside",
    description: "Landside enquiries",
    icon: LocationIcon,
    qna: [
      Landside1,
      Landside2,
      Landside3,
      Landside4,
      Landside5,
      Landside6,
      Landside7,
      Landside8,
      Landside9,
      Landside10,
      Landside11,
      Landside12,
      Landside13,
      Landside14,
      Landside15,
      Landside16,
      Landside17,
      Landside18,
      Landside19,
    ],
  },
  {
    title: "Airside",
    description: "Airside enquiries",
    icon: LocationIcon,
    qna: [
      Airside1,
      Airside2,
      Airside3,
      Airside4,
      Airside5,
      Airside6,
      Airside7,
      Airside8,
      Airside9,
      Airside10,
      Airside11,
      Airside12,
      Airside13,
      Airside14,
      Airside15,
      Airside16,
    ],
  },
  {
    title: "Sleep Lounge",
    description: "Sleep Lounge enquiries",
    icon: LocationIcon,
    qna: [
      SleepLounge1,
      SleepLounge2,
      SleepLounge3,
      SleepLounge4,
      SleepLounge5,
      SleepLounge6,
      SleepLounge7,
      SleepLounge8,
      SleepLounge9,
      SleepLounge10,
      SleepLounge11,
      SleepLounge12,
      SleepLounge13,
      SleepLounge14,
    ],
  },
  {
    title: "MAX",
    description: "Max enquiries",
    icon: LocationIcon,
    qna: [
      MAX1,
      MAX2,
      MAX3,
      MAX4,
      MAX5,
      MAX6,
      MAX7,
      MAX8,
      MAX9,
      MAX10,
      MAX11,
      MAX12,
      MAX13,
      MAX14,
      MAX15,
      MAX16,
    ],
  },
  {
    title: "Booking and Reservations",
    description: "Booking and Reservation related enquiries",
    icon: BookingIcon,
    qna: [
      Booking1,
      Booking2,
      Booking3,
      Booking4,
      Booking5,
      Booking6,
      Booking7,
      Booking8,
      Booking9,
    ],
  },
  {
    title: "Billing and Charges",
    description: "Billing and charges enquiries",
    icon: BillingIcon,
    qna: [Billing1, Billing2, Billing3, Billing4, Billing5, Billing6],
  },
  // {
  //   title: "Locations",
  //   description: "Locations and how to get to our hotels",
  //   icon: LocationIcon,
  //   qna: [],
  // },
  // {
  //   title: "Check-in and Check-out",
  //   description:
  //     "Check-in & Check-out, late check-out, early check-in, late arrival related enquiries",
  //   icon: CheckInOutIcon,
  //   qna: [],
  // },
  // {
  //   title: "Bags, Luggage and Parcels",
  //   description: "Bag drops and luggage storage, parcels and deliveries",
  //   icon: BagsIcon,
  //   qna: [],
  // },
  // {
  //   title: "Facilities",
  //   description: "Hotels facilities & amenities, WI-FI, pet, child policies",
  //   icon: FacilitiesIcon,
  //   qna: [],
  // },
  // {
  //   title: "Special Request",
  //   description: "Special requests, room types and special occasions",
  //   icon: SpecialRequestIcon,

  //   qna: [],
  // },
];
