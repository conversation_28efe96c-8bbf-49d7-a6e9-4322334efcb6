import React from 'react';

const NewOpening = ({ style }: { style?: React.CSSProperties }) => {
  return (
    <svg
      width='132'
      height='132'
      viewBox='0 0 132 132'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      style={style}
    >
      <path
        d='M0.399902 8.40039V0.400391L8.3999 8.40039H0.399902Z'
        fill='#000A3C'
        stroke='#0F0E0E'
        strokeWidth='0.8'
        strokeLinejoin='round'
      />
      <path
        d='M123.6 131.6V123.6L131.6 131.6H123.6Z'
        fill='#000A3C'
        stroke='#0F0E0E'
        strokeWidth='0.8'
        strokeLinejoin='round'
      />
      <path
        d='M131.6 90.1318L41.8685 0.400391H0.399902L131.6 131.6V90.1318Z'
        fill='#011589'
        stroke='#0F0E0E'
        strokeWidth='0.8'
        strokeLinejoin='round'
      />
      <path
        d='M37.4396 16.8422L38.3433 13.9784L38.3051 13.9402L31.5084 20.7369L30.1338 19.3623L39.0179 10.4782L40.6216 12.0819L38.6106 19.8969L37.7069 22.7607L37.7451 22.7989L44.5418 16.0021L45.9164 17.3768L37.0323 26.2609L35.4286 24.6571L37.4396 16.8422ZM38.9653 28.1938L47.8493 19.3097L53.5387 24.9991L52.2659 26.2719L48.0148 22.0208L45.5456 24.49L49.4022 28.3465L48.1294 29.6193L44.2728 25.7628L41.6763 28.3593L45.9274 32.6104L44.6546 33.8832L38.9653 28.1938ZM47.5664 36.795L54.3504 25.8108L55.8141 27.2745L52.4412 32.4548L50.112 36.0313L50.1502 36.0695L53.8159 33.8294L59.1489 30.6092L60.7526 32.2129L57.5197 37.5332L55.2796 41.1734L55.3178 41.2116L58.907 38.9206L64.1127 35.5731L65.5255 36.9859L54.4523 43.6808L52.8358 42.0644L56.2978 36.3368L58.3343 32.9766L58.3088 32.9512L54.9359 34.9749L49.1829 38.4114L47.5664 36.795ZM63.8214 53.3554C63.2444 52.7784 62.8159 52.1547 62.5359 51.4844C62.2728 50.8141 62.1625 50.1183 62.205 49.397C62.2643 48.6758 62.485 47.9376 62.8668 47.1824C63.2656 46.4272 63.8384 45.6762 64.5851 44.9295C65.3318 44.1828 66.0785 43.6143 66.8252 43.224C67.5889 42.8337 68.3271 42.613 69.0398 42.5621C69.7696 42.5112 70.4654 42.6215 71.1272 42.8931C71.806 43.1646 72.434 43.5889 73.011 44.1659C73.588 44.7429 74.008 45.3665 74.271 46.0369C74.551 46.7072 74.6613 47.403 74.6019 48.1242C74.5595 48.8455 74.3389 49.5837 73.9401 50.3389C73.5583 51.0941 72.994 51.845 72.2473 52.5917C71.5006 53.3384 70.7454 53.907 69.9817 54.2973C69.235 54.6876 68.4968 54.9082 67.7671 54.9591C67.0543 55.01 66.3585 54.8997 65.6797 54.6282C65.0178 54.3567 64.3984 53.9324 63.8214 53.3554ZM65.1069 52.0699C65.4463 52.4093 65.8154 52.6596 66.2142 52.8208C66.6215 52.9905 67.0331 53.0627 67.4489 53.0372C67.8816 53.0118 68.3144 52.893 68.7471 52.6808C69.1799 52.4687 69.5999 52.159 70.0072 51.7517L71.4072 50.3516C71.8145 49.9443 72.1242 49.5243 72.3364 49.0916C72.5485 48.6588 72.6631 48.2303 72.68 47.806C72.714 47.3818 72.6461 46.966 72.4764 46.5587C72.3152 46.1599 72.0648 45.7908 71.7254 45.4514C71.3775 45.1035 71.0042 44.8489 70.6054 44.6877C70.2066 44.5265 69.7908 44.4586 69.358 44.4841C68.9423 44.5095 68.518 44.6283 68.0853 44.8404C67.6525 45.0526 67.2325 45.3623 66.8252 45.7696L65.4251 47.1696C65.0178 47.5769 64.7081 47.997 64.496 48.4297C64.2838 48.8625 64.1608 49.291 64.1269 49.7152C64.1099 50.1395 64.182 50.551 64.3432 50.9498C64.5045 51.3486 64.759 51.722 65.1069 52.0699ZM69.3442 58.5728L78.2283 49.6887L82.0467 53.5071C82.8698 54.3301 83.2643 55.2084 83.2304 56.1417C83.1964 57.0751 82.7722 57.9491 81.9576 58.7637C81.143 59.5783 80.269 60.0025 79.3356 60.0365C78.4023 60.0704 77.524 59.6759 76.701 58.8528L74.3208 56.4727L70.7825 60.011L69.3442 58.5728ZM75.5809 55.2126L77.8719 57.5036C78.2368 57.8685 78.6144 58.0594 79.0047 58.0764C79.4035 58.0849 79.7811 57.9109 80.1375 57.5545L80.7484 56.9436C81.1048 56.5872 81.2745 56.2139 81.2576 55.8235C81.2491 55.4247 81.0624 55.0429 80.6975 54.678L78.4065 52.387L75.5809 55.2126ZM77.0695 66.298L85.9536 57.4139L91.6429 63.1033L90.3701 64.3761L86.119 60.125L83.6498 62.5942L87.5064 66.4508L86.2336 67.7235L82.377 63.867L79.7805 66.4635L84.0316 70.7146L82.7589 71.9874L77.0695 66.298ZM91.6655 71.0681L92.5692 68.2043L92.531 68.1661L85.7343 74.9629L84.3597 73.5882L93.2438 64.7041L94.8475 66.3079L92.8365 74.1228L91.9328 76.9866L91.971 77.0248L98.7677 70.2281L100.142 71.6027L91.2582 80.4868L89.6545 78.8831L91.6655 71.0681ZM92.8093 82.0379L93.9803 80.8669L95.1895 82.0761L101.732 75.5339L100.522 74.3248L101.693 73.1538L105.563 77.0231L104.392 78.1941L103.17 76.9722L96.6277 83.5143L97.8496 84.7362L96.6786 85.9072L92.8093 82.0379ZM105.537 84.9401L106.441 82.0763L106.403 82.0381L99.6062 88.8348L98.2316 87.4602L107.116 78.5761L108.719 80.1798L106.708 87.9948L105.805 90.8585L105.843 90.8967L112.64 84.1L114.014 85.4746L105.13 94.3587L103.526 92.755L105.537 84.9401ZM114.127 100.657L114.076 100.606C113.55 100.946 112.935 101.052 112.231 100.925C111.543 100.797 110.86 100.394 110.181 99.7155C109.655 99.1894 109.265 98.6039 109.01 97.959C108.764 97.3057 108.667 96.6226 108.718 95.9098C108.786 95.1971 109.01 94.4631 109.392 93.7079C109.791 92.9527 110.364 92.2018 111.111 91.4551C111.849 90.7168 112.596 90.1568 113.351 89.775C114.114 89.3846 114.861 89.164 115.591 89.1131C116.321 89.0622 117.021 89.1768 117.691 89.4568C118.37 89.7283 119.002 90.1568 119.587 90.7423C120.385 91.5399 120.881 92.3927 121.077 93.3006C121.28 94.2 121.221 95.1165 120.898 96.0498L119.04 95.5662C119.286 94.9807 119.358 94.3825 119.256 93.7715C119.163 93.1521 118.845 92.5709 118.302 92.0278C117.598 91.3235 116.804 90.9799 115.922 90.9969C115.048 91.0223 114.199 91.4466 113.376 92.2696L111.925 93.7206C111.102 94.5437 110.678 95.3922 110.652 96.2662C110.635 97.1487 110.979 97.9421 111.683 98.6463C111.963 98.9263 112.265 99.1597 112.587 99.3464C112.918 99.5246 113.245 99.6391 113.567 99.69C113.906 99.7409 114.233 99.7197 114.547 99.6264C114.878 99.533 115.184 99.3464 115.464 99.0664L116.151 98.379L114.318 96.5462L115.553 95.3116L118.773 98.5318L114.038 103.267L112.778 102.007L114.127 100.657Z'
        fill='white'
      />
    </svg>
  );
};

export default NewOpening;
