

.easeOutAnimation {
    color: var(--highlight-color);
    -webkit-transition: color 0.2s ease-out;
        -moz-transition: color 0.2s ease-out;
        -o-transition: color 0.2s ease-out;
        -ms-transition: color 0.2s ease-out;
        transition: color 0.2s ease-out;
}

.easeInAnimation {
    color: var(--default-color);
    -webkit-transition: color 0.2s ease-in;
    -moz-transition: color 0.2s ease-in;
    -o-transition: color 0.2s ease-in;
    -ms-transition: color 0.2s ease-in;
    transition: color 0.2s ease-in;
}