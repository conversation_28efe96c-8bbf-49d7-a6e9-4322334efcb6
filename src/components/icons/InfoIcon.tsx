import React from 'react';

const InfoIcon = () => {
  return (
    <svg
      width='18'
      height='19'
      viewBox='0 0 18 19'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_7250_2185)'>
        <path
          d='M9 18.5C13.9706 18.5 18 14.4706 18 9.5C18 4.52944 13.9706 0.5 9 0.5C4.02944 0.5 0 4.52944 0 9.5C0 14.4706 4.02944 18.5 9 18.5Z'
          fill='white'
        />
        <path
          d='M10.493 11.4667C10.6403 11.5777 10.7875 11.6888 10.9662 11.824C10.3667 12.7518 9.82267 13.703 8.61721 13.9122C7.51234 14.1037 6.86856 13.4181 7.16068 12.3277C7.38439 11.4908 7.6773 10.6724 7.94044 9.84679C7.94849 9.82184 7.96459 9.7985 7.97263 9.77356C8.24704 8.90125 8.15691 8.77088 7.25965 8.74433C7.20735 8.74272 7.15504 8.73387 7.0625 8.72421C7.11481 8.50855 7.16309 8.30898 7.22425 8.05469H10.431C10.3594 8.30495 10.2878 8.5697 10.2081 8.83204C9.85245 10.0069 9.48871 11.1794 9.14349 12.3583C9.10165 12.5015 9.15556 12.6738 9.16522 12.8323C9.31409 12.7615 9.50481 12.7277 9.60298 12.6134C9.90797 12.2609 10.1776 11.8787 10.4922 11.4667H10.493Z'
          fill='#13C1A8'
        />
        <path
          d='M9.71607 7.21617C9.12783 7.21375 8.64097 6.7269 8.64258 6.14187C8.64419 5.56248 9.14231 5.05792 9.71768 5.0547C10.3003 5.05148 10.7799 5.54317 10.7783 6.14187C10.7775 6.73978 10.3043 7.21778 9.71607 7.21617Z'
          fill='#13C1A8'
        />
      </g>
      <defs>
        <clipPath id='clip0_7250_2185'>
          <rect
            width='18'
            height='18'
            fill='white'
            transform='translate(0 0.5)'
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default InfoIcon;
