import React from 'react';

const RoomSelectionIcon = () => {
  return (
    <svg
      width='18'
      height='16'
      viewBox='0 0 18 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M16.25 3.75V13.6895L14.6895 15.25H3.31055L1.75 13.6895V3.75H16.25Z'
        stroke='#4D5CAC'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <mask id='path-2-inside-1_7071_84447' fill='white'>
        <path d='M17 4V2L15 0H3L1 2V4' />
      </mask>
      <path
        d='M17 4.75C17.4142 4.75 17.75 4.41421 17.75 4C17.75 3.58579 17.4142 3.25 17 3.25V4.75ZM1 3.25C0.585786 3.25 0.25 3.58579 0.25 4C0.25 4.41421 0.585786 4.75 1 4.75V3.25ZM17 4V3.25H1V4V4.75H17V4ZM15.5 4C15.5 4.82843 16.1716 5.5 17 5.5C17.8284 5.5 18.5 4.82843 18.5 4H15.5ZM17 2H18.5C18.5 1.60218 18.342 1.22064 18.0607 0.93934L17 2ZM15 0L16.0607 -1.06066C15.7794 -1.34196 15.3978 -1.5 15 -1.5V0ZM3 0V-1.5C2.60218 -1.5 2.22064 -1.34196 1.93934 -1.06066L3 0ZM1 2L-0.0606601 0.93934C-0.341965 1.22064 -0.5 1.60218 -0.5 2H1ZM-0.5 4C-0.5 4.82843 0.171573 5.5 1 5.5C1.82843 5.5 2.5 4.82843 2.5 4H-0.5ZM17 4H18.5V2H17H15.5V4H17ZM17 2L18.0607 0.93934L16.0607 -1.06066L15 0L13.9393 1.06066L15.9393 3.06066L17 2ZM15 0V-1.5H3V0V1.5H15V0ZM3 0L1.93934 -1.06066L-0.0606601 0.93934L1 2L2.06066 3.06066L4.06066 1.06066L3 0ZM1 2H-0.5V4H1H2.5V2H1Z'
        fill='#4D5CAC'
        mask='url(#path-2-inside-1_7071_84447)'
      />
      <path
        d='M13 8C13.2652 8 13.5196 7.89464 13.7071 7.70711C13.8946 7.51957 14 7.26522 14 7C14 6.73478 13.8946 6.48043 13.7071 6.29289C13.5196 6.10536 13.2652 6 13 6C12.7348 6 12.4804 6.10536 12.2929 6.29289C12.1054 6.48043 12 6.73478 12 7C12 7.26522 12.1054 7.51957 12.2929 7.70711C12.4804 7.89464 12.7348 8 13 8Z'
        fill='#4D5CAC'
      />
    </svg>
  );
};

export default RoomSelectionIcon;
