import React from 'react';

const ParagraphIcon = () => {
  return (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clip-path='url(#clip0_7071_84464)'>
        <path d='M4.5 11H11.5' stroke='currentColor' />
        <path d='M4.5 5H11.5' stroke='currentColor' />
        <path d='M4.5 8H11.5' stroke='currentColor' />
        <rect x='1.5' y='0.5' width='13' height='15' stroke='currentColor' />
      </g>
      <defs>
        <clipPath id='clip0_7071_84464'>
          <rect width='16' height='15.9998' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ParagraphIcon;
