import SleepLoungeDisplay1 from "./images/<EMAIL>";
import SleepLoungeDisplay2 from "./images/<EMAIL>";
import SleepLoungeDisplay3 from "./images/<EMAIL>";
import HotelIntro from "../global/hotel/HotelIntro";

const breadCrumbsContent = [
  { name: "Home", url: "/" },
  { name: "Location / KLIA Terminal 1", url: "/klia-1" },
  { name: "Sleep Lounge", url: "" },
];

const title =
  "Sleep-only lounge in Terminal 1 for a quiet nap or quick recharge.";

const images = [SleepLoungeDisplay1, SleepLoungeDisplay2, SleepLoungeDisplay3];

const SleepLoungeIntro = () => {
  return (
    <HotelIntro
      breadCrumbsContent={breadCrumbsContent}
      images={images}
      textContent={title}
      buttonUrl={"/booking"}
    />
  );
};

export default SleepLoungeIntro;
