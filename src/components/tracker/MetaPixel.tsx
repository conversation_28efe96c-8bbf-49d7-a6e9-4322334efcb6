import Script from 'next/script';

const MetaPixel = ({ pixelId = '' }) => {
  if (!pixelId) return null;
  return (
    <>
      {/* Meta Pixel Script using Next.js Script component */}
      <Script
        id='meta-pixel'
        src='https://connect.facebook.net/en_US/fbevents.js'
        strategy='afterInteractive'
        onLoad={() => {
          // Initialize Meta Pixel after script loads
          if (typeof window !== 'undefined' && window.fbq) {
            window.fbq('init', pixelId);
            window.fbq('track', 'PageView');
          }
        }}
      />

      <Script
        id='meta-pixel-init'
        strategy='afterInteractive'
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];}(window,document,'script');
            fbq('init', '${pixelId}');
            fbq('track', 'PageView');
          `,
        }}
      />

      {/* Fallback noscript image */}
      <noscript>
        <img
          height='1'
          width='1'
          style={{ display: 'none' }}
          src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
          alt=''
        />
      </noscript>
    </>
  );
};

export default MetaPixel;
