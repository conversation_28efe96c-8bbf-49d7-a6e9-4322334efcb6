'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Stack,
  IconButton,
  Paper,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';

const csrImages = [
  'https://images.unsplash.com/photo-1593113598332-cd288d649433?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1559027615-cd4628902d4a?auto=format&fit=crop&w=800&q=80',
];

const DotButton = styled(IconButton)(({ theme }) => ({
  width: 12,
  height: 12,
  borderRadius: '50%',
  transition: 'all 0.2s',
  '&.active': {
    backgroundColor: '#223a8a',
  },
  '&:not(.active)': {
    backgroundColor: theme.palette.grey[300],
  },
}));

const CSRCampaignSection = () => {
  const [imgIdx, setImgIdx] = useState(0);

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: 'grey.50',
        py: { xs: 8, md: 10 },
        px: 2,
      }}
    >
      <Container maxWidth='xl'>
        {/* Mobile Layout */}
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 600,
                mb: 1,
                color: '#0F0E0E',
                letterSpacing: '0.1em',
                display: 'block',
                fontSize: '12px',
              }}
            >
              CSR CAMPAIGN
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: '32px',
                fontWeight: 600,
                mb: 4,
                color: '#0F0E0E',
              }}
            >
              Our Initiative to Give Back
            </Typography>
          </Box>

          {/* Image */}
          <Box sx={{ mb: 6 }}>
            <Paper
              elevation={8}
              sx={{
                borderRadius: 2.5,
                overflow: 'hidden',
                width: '100%',
                height: 256,
                boxShadow: 4,
              }}
            >
              <img
                src={csrImages[imgIdx]}
                alt={`CSR ${imgIdx + 1}`}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
              />
            </Paper>
            {/* Dots */}
            <Box
              sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 2 }}
            >
              {csrImages.map((_, i) => (
                <DotButton
                  key={i}
                  className={i === imgIdx ? 'active' : ''}
                  onClick={() => setImgIdx(i)}
                  aria-label={`Go to image ${i + 1}`}
                />
              ))}
            </Box>
          </Box>

          {/* Content */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography sx={{ fontSize: '1.125rem', mb: 2, color: 'grey.900' }}>
              Our idea is simple.
            </Typography>
            <Typography
              sx={{
                fontSize: '28px',
                fontWeight: 500,
                mb: 3,
                lineHeight: 1.2,
                color: '#0F0E0E',
              }}
            >
              <Box component='span' sx={{ color: '#4BDCC7' }}>
                RM1
              </Box>{' '}
              is donated to{' '}
              <Box
                component='span'
                sx={{ textDecoration: 'underline', fontWeight: 'bold' }}
              >
                Kechara Soup Kitchen
              </Box>{' '}
              to feed the homeless from each check-in at CapsuleTransit.
            </Typography>
            <Box sx={{ mb: 4 }}>
              <Typography
                sx={{
                  color: '#0E9884',
                  fontSize: '16px',
                  mb: 1.5,
                  fontWeight: 600,
                }}
              >
                And as of today, we have donated
              </Typography>
              <Box
                sx={{
                  bgcolor: '#055146',
                  color: '#21FEDD',
                  fontSize: '31px',
                  fontWeight: 500,
                  px: 3,
                  borderRadius: '8px',
                  display: 'inline-block',
                  maxHeight: '54px',
                }}
              >
                RM 115,198
              </Box>
            </Box>
            <Divider
              sx={{
                margin: '30px 0',
                height: '1px',
                borderStyle: 'solid',
                borderColor: '#D8DDE2',
              }}
            />
            <Typography
              sx={{
                color: '#0F0E0E',
                mb: 4,
                fontSize: '16px',
                lineHeight: 1.6,
              }}
            >
              Join us on this journey to support others while you simply get
              rested at KLIA!
            </Typography>
            <Stack spacing={2}>
              <Link href='/book-your-stay' passHref>
                <Button
                  variant='outlined'
                  component='a'
                  href='/book-your-stay'
                  sx={{
                    borderColor: '#011589',
                    color: '#011589',
                    fontWeight: 600,
                    px: 4,
                    borderRadius: '4px',
                    maxHeight: '40px',
                    height: '100%',
                    '&:hover': {
                      bgcolor: '#011589',
                      color: 'white',
                    },
                  }}
                >
                  BOOK NOW
                </Button>
              </Link>
              <Link href='/csr' passHref>
                <Button
                  sx={{
                    color: '#011589',
                    textDecoration: 'underline',
                    fontWeight: 400,
                    fontSize: '15px',
                    backgroundColor: 'transparent !important',
                    '&:hover': {
                      textDecoration: 'none',
                    },
                  }}
                >
                  Read Our CSR
                </Button>
              </Link>
            </Stack>
          </Box>
        </Box>

        {/* Desktop Layout */}
        <Box
          sx={{
            display: {
              xs: 'none',
              md: 'block',
              maxWidth: '1080px',
              margin: '0 auto',
            },
          }}
        >
          <Box sx={{ mb: 4 }}>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 'bold',
                mb: 1,
                color: 'grey.600',
                letterSpacing: '0.1em',
                display: 'block',
                textAlign: 'center',
              }}
            >
              CSR CAMPAIGN
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: '40px',
                fontWeight: 600,
                mb: 4,
                color: '#0F0E0E',
                lineHeight: 1.2,
                textAlign: 'center',
              }}
            >
              Our Initiative to Give Back
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: '60% 40%',
              alignItems: 'center',
              gap: '20px',
              margin: '0 auto',
            }}
          >
            {/* Left: Content */}
            <Box sx={{ flex: 1 }}>
              <Box sx={{ mb: 4 }}>
                <Typography
                  sx={{ fontSize: '20px', mb: '8px', color: '#0F0E0E' }}
                >
                  Our idea is simple.
                </Typography>
                <Typography
                  sx={{
                    fontSize: '32px',
                    fontWeight: 500,
                    mb: 3,
                    lineHeight: '37px',
                    color: '#0F0E0E',
                  }}
                >
                  <Box
                    component='span'
                    sx={{ color: '#4BDCC7', fontWeight: 600 }}
                  >
                    RM1
                  </Box>{' '}
                  is donated to{' '}
                  <Box
                    component='span'
                    sx={{ textDecoration: 'underline', fontWeight: 600 }}
                  >
                    Kechara Soup Kitchen
                  </Box>
                  <br />
                  to feed the homeless from each check-in at CapsuleTransit.
                </Typography>
                <Box
                  sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}
                >
                  <Typography
                    sx={{
                      color: '#0E9884',
                      fontSize: '16px',
                      fontWeight: 600,
                    }}
                  >
                    And as of today,
                    <br /> we have donated
                  </Typography>
                  <Box
                    sx={{
                      bgcolor: '#055146',
                      color: '#21FEDD',
                      fontSize: '31px',
                      fontWeight: 500,
                      px: 2,
                      borderRadius: '8px',
                      maxHeight: '54px',
                    }}
                  >
                    RM 115,198
                  </Box>
                </Box>
                <Divider
                  sx={{
                    borderColor: '#D8DDE2',
                    height: '1px',
                    borderStyle: 'solid',
                    margin: '40px 0',
                  }}
                />
                <Typography
                  sx={{
                    color: '#0F0E0E',
                    mb: 4,
                    fontSize: '16px',
                    lineHeight: 1.6,
                  }}
                >
                  Join us on this journey to support others while you simply get
                  rested at KLIA!
                </Typography>
                <Stack direction='row' spacing={3}>
                  <Link href='/book-your-stay' passHref>
                    <Button
                      variant='outlined'
                      sx={{
                        borderColor: '#011589',
                        color: '#011589',
                        fontWeight: 600,
                        px: 2,
                        py: 1.5,
                        maxHeight: '40px',
                        borderRadius: '4px',
                        '&:hover': {
                          bgcolor: '#011589',
                          color: 'white',
                        },
                      }}
                    >
                      BOOK NOW
                    </Button>
                  </Link>
                  <Link href='/csr' passHref>
                    <Button
                      sx={{
                        color: '#011589',
                        textDecoration: 'underline',
                        fontWeight: 500,
                        fontSize: '15px',
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'transparent !important',
                        '&:hover': {
                          textDecoration: 'none',
                        },
                      }}
                    >
                      Read Our CSR
                    </Button>
                  </Link>
                </Stack>
              </Box>
            </Box>

            {/* Right: Image */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              <Box sx={{ width: '100%', maxWidth: 480 }}>
                <Paper
                  elevation={12}
                  sx={{
                    borderRadius: 2.5,
                    overflow: 'hidden',
                    width: '100%',
                    height: { lg: 384 },
                    boxShadow: 6,
                  }}
                >
                  <img
                    src={csrImages[imgIdx]}
                    alt={`CSR ${imgIdx + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </Paper>
                {/* Dots */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    gap: 1,
                    mt: 3,
                  }}
                >
                  {csrImages.map((_, i) => (
                    <DotButton
                      key={i}
                      className={i === imgIdx ? 'active' : ''}
                      onClick={() => setImgIdx(i)}
                      aria-label={`Go to image ${i + 1}`}
                    />
                  ))}
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default CSRCampaignSection;
