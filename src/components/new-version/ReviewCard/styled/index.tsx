import { Box, Card, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components
export const StyledReviewCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2.5),
  boxShadow: 'none',
  padding: '30px',
  width: '100%',
  height: '100%',
  margin: '0 auto',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  overflow: 'hidden',
  boxSizing: 'border-box',
  backgroundColor: '#FFFFFF',
}));

// Enhanced skeleton card with smooth animations
export const SkeletonCard = styled(StyledReviewCard)(({ theme }) => ({
  '& .MuiSkeleton-root': {
    backgroundColor: 'rgba(0, 0, 0, 0.06)',
    '&::after': {
      background: `linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)`,
    },
  },
  animation: 'fadeIn 0.6s ease-in-out',
  '@keyframes fadeIn': {
    from: {
      opacity: 0,
      transform: 'translateY(20px)',
    },
    to: {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
}));
