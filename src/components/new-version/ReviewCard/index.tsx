'use client';

import React from 'react';
import { Box, Typography, CardContent } from '@mui/material';
import { StyledReviewCard } from './styled';
import { countryCodeToFlag } from '@/utils/getCountryFlag';
import { ReviewData } from '@/actions/getReviews/types';

const ReviewCard = ({
  review,
  platformUrl,
  reviewPageUrl,
  platform,
}: {
  review: ReviewData;
  platformUrl?: string;
  reviewPageUrl?: string;
  platform?: string;
}) => {
  return (
    <StyledReviewCard>
      <CardContent sx={{ p: 0, position: 'relative', height: '100%' }}>
        <Box
          sx={{
            display: 'flex',
            // alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: 0.5,
            }}
          >
            <Typography
              variant='h4'
              sx={{
                fontWeight: 'bold',
                color: '#0F0E0E',
                fontSize: '1.5rem',
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: 1.2,
                marginBottom: 0.5,
              }}
            >
              {review.reviewerInfo?.reviewerName || 'Anonymous'}
            </Typography>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              <Typography
                variant='h5'
                sx={{
                  fontSize: '14px',
                  lineHeight: '0',
                  paddingTop: '3px',
                }}
              >
                {review.reviewerInfo?.countryName &&
                  countryCodeToFlag(review.reviewerInfo?.countryName)}
              </Typography>
              <Typography
                variant='body1'
                sx={{
                  color: '#3B3F47',
                  fontSize: '14px',
                }}
              >
                {review.reviewerInfo?.countryName}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              bgcolor: '#011589',
              color: 'white',
              px: 2,
              py: 1,
              borderRadius: 1.5,
              fontWeight: 'bold',
              fontSize: '14px',
              height: 'fit-content',
            }}
          >
            {review.ratingText}
          </Box>
        </Box>

        {/* Review Title */}
        <Typography
          variant='h6'
          sx={{
            fontWeight: 600,
            color: '#1A1A1A',
            mb: 2,
            fontSize: '1rem',
            lineHeight: 1.4,
          }}
        >
          {review.reviewTitle}
        </Typography>

        {/* Review Content */}
        <Typography
          variant='body2'
          sx={{
            color: '#3B3F47',
            fontSize: '16px',
            mb: 2,
            flexGrow: 1,
            display: '-webkit-box',
            WebkitLineClamp: { xs: 2, sm: 4 }, // 2 lines on mobile, 4 lines on larger screens
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            lineHeight: 1.4,
            maxHeight: { xs: '2.8rem', sm: '5.6rem' }, // 2 lines * 1.4 line-height on mobile, 4 lines on larger screens
          }}
        >
          {review.content}
        </Typography>

        {/* Read More Link */}
        {reviewPageUrl && (
          <Typography
            component='a'
            href={reviewPageUrl}
            target='_blank'
            rel='noopener noreferrer'
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              color: '#011589',
              textDecoration: 'underline',
              fontWeight: 500,
              fontSize: '15px',
              '&:hover': {
                textDecoration: 'none',
              },
            }}
          >
            Read more {platform ? `at ${platform}` : ''}
          </Typography>
        )}
      </CardContent>
    </StyledReviewCard>
  );
};

export default ReviewCard;

export const UnloadedReviewCard = () => {
  return (
    <StyledReviewCard>
      <CardContent sx={{ p: 3, textAlign: 'center' }}>
        <Typography
          variant='h6'
          sx={{
            color: '#87888B',
            mb: 2,
            fontWeight: 500,
          }}
        >
          Unable to load reviews
        </Typography>
        <Typography
          variant='body2'
          sx={{
            color: '#87888B',
            mb: 2,
          }}
        >
          Please try again later
        </Typography>
      </CardContent>
    </StyledReviewCard>
  );
};
