import React from 'react';
import { Box, CardContent, Skeleton } from '@mui/material';
import { SkeletonCard } from '../styled';

export const ReviewCardSkeleton = ({ index }: { index?: number }) => (
  <SkeletonCard
    sx={{
      animationDelay: `${(index || 0) * 150}ms`,
    }}
  >
    <CardContent sx={{ p: 0, position: 'relative' }}>
      {/* Platform Logo Skeleton */}
      <Box
        sx={{
          position: 'absolute',
          top: 2,
          right: 2,
        }}
      >
        <Skeleton
          variant='rectangular'
          width={32}
          height={32}
          sx={{ borderRadius: 1 }}
          animation='wave'
        />
      </Box>

      {/* Reviewer Info Skeleton */}
      <Box sx={{ mb: 2 }}>
        <Skeleton
          variant='text'
          width='60%'
          height={28}
          animation='wave'
          sx={{ mb: 0.5 }}
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Skeleton
            variant='circular'
            width={20}
            height={20}
            animation='wave'
          />
          <Skeleton variant='text' width='40%' height={20} animation='wave' />
        </Box>
      </Box>

      {/* Review Title Skeleton */}
      <Skeleton
        variant='text'
        width='85%'
        height={24}
        animation='wave'
        sx={{ mb: 2 }}
      />

      {/* Review Content Skeleton */}
      <Box sx={{ mb: 3 }}>
        <Skeleton
          variant='text'
          width='100%'
          height={20}
          animation='wave'
          sx={{ mb: 0.5 }}
        />
        <Skeleton
          variant='text'
          width='90%'
          height={20}
          animation='wave'
          sx={{ mb: 0.5 }}
        />
        <Skeleton variant='text' width='75%' height={20} animation='wave' />
      </Box>

      {/* Read More Link Skeleton */}
      <Skeleton variant='text' width='50%' height={20} animation='wave' />
    </CardContent>
  </SkeletonCard>
);
