'use client';

import React, { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import {
  Box,
  Typography,
  Card,
  Button,
  Grid,
  Collapse,
  Container,
  Stack,
  Divider,
  useTheme,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import PublicAreaModal from '../PublicAreaModal';
import RestrictedAreaModal from '../RestrictedAreaModal';
import CheckAvailabilityModal from '../CheckAvailabilityModal';
import Image from 'next/image';

import NewOpening from '../../badge/NewOpening';
import InfoIcon from '../../icons/InfoIcon';
import { Outlet } from '@/actions/getOutlets/types';

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '8px',
  // boxShadow: theme.shadows[4],
  border: `1px solid #4D5CAC`,
  overflow: 'hidden',
  position: 'relative',
}));

const NewOpeningBadge = styled(Box)(({ theme }) => ({
  position: 'absolute',
  right: '-7px',
  top: '-22px',
  zIndex: 20,
}));

const OutletsSection = ({ outlets = [] }: { outlets: Outlet[] }) => {
  const theme = useTheme();
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [isPublicAreaModalOpen, setIsPublicAreaModalOpen] = useState(false);
  const [isRestrictedAreaModalOpen, setIsRestrictedAreaModalOpen] =
    useState(false);
  const [isCheckAvailabilityModalOpen, setIsCheckAvailabilityModalOpen] =
    useState(false);

  const toggleCard = (cardIndex: number) => {
    setExpandedCard(expandedCard === cardIndex ? null : cardIndex);
  };

  const handleOpenPublicAreaModal = () => {
    setIsPublicAreaModalOpen(true);
  };

  const handleClosePublicAreaModal = () => {
    setIsPublicAreaModalOpen(false);
  };

  const handleOpenRestrictedAreaModal = () => {
    setIsRestrictedAreaModalOpen(true);
  };

  const handleCloseRestrictedAreaModal = () => {
    setIsRestrictedAreaModalOpen(false);
  };

  const handleOpenCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(true);
  };

  const handleCloseCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(false);
  };

  const [selectedOutlet, setSelectedOutlet] = useState<any>(null);

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: '#f5f7fa',
        py: { xs: 10, lg: 20 },
        px: 2,
      }}
    >
      <Container maxWidth='xl'>
        <Typography
          variant='h2'
          sx={{
            fontSize: { xs: '1.5rem', md: '1.875rem' },
            fontWeight: 'bold',
            textAlign: 'center',
            mb: 5,
            textDecoration: 'underline',
          }}
        >
          Our Outlets and Location
        </Typography>

        <Stack spacing={4}>
          {outlets.map((outlet, index) => (
            <Box sx={{ position: 'relative' }} key={index}>
              {outlet.isNewOpening && (
                <NewOpeningBadge>
                  <NewOpening style={{ width: '100px' }} />
                </NewOpeningBadge>
              )}
              <StyledCard>
                {/* Desktop Layout */}
                <Box
                  sx={{
                    display: { xs: 'none', md: 'flex', maxHeight: '360px' },
                  }}
                >
                  {/* Image Carousel */}
                  <Box
                    sx={{
                      position: 'relative',
                      width: '33.333%',
                      maxWidth: '360px',
                      height: '360px',
                      maxHeight: '360px',
                      '& .swiper-button-next, & .swiper-button-prev': {
                        top: '56%',
                        transform: 'translateY(-50%)',
                        zIndex: 10,
                        backgroundColor: 'rgba(15, 14, 14, 0.5)',
                        width: '32px',
                        height: '48px',
                        '&:hover': {
                          backgroundColor: 'rgba(15, 14, 14, 0.8)',
                        },
                        boxShadow: theme.shadows[4],
                        transition: 'all 0.2s',
                        borderRadius: '4px',
                        '&::after': {
                          fontSize: '18px',
                          color: 'white',
                          fontWeight: 'bold',
                        },
                      },
                      '& .swiper-button-prev': {
                        left: '12px',
                      },
                      '& .swiper-button-next': {
                        right: '12px',
                      },
                    }}
                  >
                    <Swiper
                      modules={[Navigation]}
                      navigation={true}
                      style={{
                        height: '100%',
                        maxHeight: '360px',
                        maxWidth: '360px',
                        width: '100%',
                      }}
                      className={`outlets-swiper-${index}`}
                    >
                      {outlet.images.map((image, imageIndex) => (
                        <SwiperSlide
                          key={imageIndex}
                          style={{
                            height: '100%',
                            width: '100%',
                            display: 'flex',
                          }}
                        >
                          <img
                            src={image.formats?.medium || image.url}
                            alt={image.name || 'CapsuleTransit '}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              display: 'block',
                            }}
                          />
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </Box>

                  {/* Content */}
                  <Box sx={{ flex: 1, p: 3 }}>
                    <Typography
                      variant='h3'
                      sx={{
                        fontSize: '1.5rem',
                        fontWeight: 'bold',
                        mb: 1,
                        color: '#3B3F47',
                      }}
                    >
                      {outlet.title}
                    </Typography>
                    <Typography
                      sx={{ color: 'grey.600', mb: 2, lineHeight: 1.6 }}
                    >
                      {outlet.description}
                    </Typography>

                    <Stack direction='row' spacing={1} sx={{ mb: 1.5 }}>
                      <Button
                        variant='contained'
                        size='small'
                        sx={{
                          bgcolor:
                            outlet.location === 'Terminal 1'
                              ? '#FFF1AC'
                              : '#D9F2EF',
                          color: '#0F0E0E',
                          fontWeight: 600,
                          textTransform: 'none',
                          borderRadius: '4px',
                          px: 2,
                          py: 0.5,
                          '&:hover': {
                            bgcolor:
                              outlet.location === 'Terminal 1'
                                ? '#FFF1AC'
                                : '#D9F2EF',
                          },
                        }}
                      >
                        {outlet.location}
                      </Button>
                      <Button
                        variant='contained'
                        size='small'
                        onClick={
                          outlet.isPublicArea
                            ? handleOpenPublicAreaModal
                            : handleOpenRestrictedAreaModal
                        }
                        sx={{
                          bgcolor: outlet.isPublicArea ? '#D9F2EF' : '#FEDCF1',
                          color: '#0F0E0E',
                          fontWeight: 600,
                          textTransform: 'none',
                          borderRadius: '4px',
                          px: 2,
                          py: 0.5,
                          '&:hover': {
                            bgcolor: outlet.isPublicArea
                              ? '#D9F2EF'
                              : '#FEDCF1',
                          },
                        }}
                      >
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          {outlet.isPublicArea && 'Public Area'}
                          {outlet.isRestrictedArea && 'Restricted Area'}
                          <InfoIcon />
                        </Box>
                      </Button>
                    </Stack>

                    <Grid container spacing={0} sx={{ mt: 2 }}>
                      {/* Left Part - Room Types and Prices */}
                      <Grid item xs={12} md={5}>
                        <Typography
                          variant='caption'
                          sx={{
                            fontWeight: 600,
                            color: '#989CA4',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em',
                            mb: 1,
                            display: 'block',
                            fontSize: '12px',
                          }}
                        >
                          ROOM TYPE
                        </Typography>
                        <Stack spacing={1}>
                          {outlet.roomTypes.map((roomType, index) => (
                            <Box
                              key={index}
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'flex-start',
                              }}
                            >
                              <Box sx={{ flex: 1 }}>
                                <Typography
                                  variant='body2'
                                  sx={{
                                    fontWeight: 600,
                                    color: '#3B3F47',
                                    fontSize: '16px',
                                  }}
                                >
                                  {roomType.name}
                                </Typography>
                                <Typography
                                  variant='caption'
                                  sx={{ color: '#989CA4', fontSize: '12px' }}
                                >
                                  {roomType.beds}
                                </Typography>
                              </Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 400,
                                  color: '#3B3F47',
                                  ml: 2,
                                  fontSize: '16px',
                                }}
                              >
                                {roomType.priceFrom}
                              </Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Grid>

                      {/* Divider */}
                      <Grid item xs={12} md={1}>
                        <Box
                          sx={{
                            width: '1px',
                            height: '100%',
                            backgroundColor: 'grey.300',
                            mx: 'auto',
                            display: { xs: 'none', md: 'block' },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        {/* Right Part - Facilities */}
                        <Typography
                          variant='caption'
                          sx={{
                            fontWeight: 600,
                            color: '#989CA4',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em',
                            mb: 1,
                            display: 'block',
                            fontSize: '12px',
                          }}
                        >
                          FACILITIES
                        </Typography>
                        <Box
                          sx={{
                            display: 'grid',
                            gridTemplateColumns: {
                              xs: '1fr',
                              sm: '1fr 1fr',
                              lg: '1fr 1fr 1fr',
                            },
                            gap: '12px',
                            alignItems: 'start',
                          }}
                        >
                          {outlet.facilities.map((facility, index) => (
                            <Box
                              key={index}
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '2px',
                              }}
                            >
                              <Box
                                sx={{
                                  width: 18,
                                  height: 18,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexShrink: 0,
                                  color: '#5C68B5',
                                }}
                              >
                                <Image
                                  src={facility.icon}
                                  alt={facility.name}
                                  width={18}
                                  height={18}
                                />
                              </Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: '#333',
                                  fontSize: { xs: '13px', md: '14px' },
                                  lineHeight: 1.4,
                                }}
                              >
                                {facility.name}
                              </Typography>
                            </Box>
                          ))}
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>

                  {/* Action Buttons */}
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      bgcolor: 'grey.50',
                      px: 3,
                      py: 4,
                      minWidth: 160,
                      borderLeft: 1,
                      borderColor: 'grey.200',
                      backgroundColor: '#DADCEC',
                    }}
                  >
                    <Button
                      variant='outlined'
                      onClick={() => {
                        setSelectedOutlet(outlet);
                        handleOpenCheckAvailabilityModal();
                      }}
                      sx={{
                        borderColor: '#011589',
                        fontWeight: 'bold',
                        mb: 1.5,
                        width: '100%',
                        backgroundColor: '#fff',
                        color: '#011589',
                        borderRadius: '4px',
                        '&:hover': {
                          bgcolor: '#011589',
                          color: 'white',
                        },
                      }}
                    >
                      BOOK NOW
                    </Button>
                    <Button
                      component='a'
                      href='#'
                      sx={{
                        color: '#011589',
                        textDecoration: 'underline',
                        fontWeight: 500,
                        backgroundColor: 'transparent !important',
                        '&:hover': {
                          textDecoration: 'none',
                        },
                      }}
                    >
                      View Outlet
                    </Button>
                  </Box>
                </Box>

                {/* Mobile Layout */}
                <Box sx={{ display: { xs: 'block', md: 'none' } }}>
                  {/* Image Carousel */}
                  <Box
                    sx={{
                      position: 'relative',
                      height: 280,
                      maxHeight: 320,
                      '& .swiper-button-next, & .swiper-button-prev': {
                        top: '56%',
                        transform: 'translateY(-50%)',
                        zIndex: 10,
                        backgroundColor: 'rgba(15, 14, 14, 0.5)',
                        width: '32px',
                        height: '48px',
                        '&:hover': {
                          backgroundColor: 'rgba(15, 14, 14, 0.8)',
                        },
                        boxShadow: theme.shadows[4],
                        transition: 'all 0.2s',
                        borderRadius: '4px',
                        '&::after': {
                          fontSize: '18px',
                          color: 'white',
                          fontWeight: 'bold',
                        },
                      },
                      '& .swiper-button-prev': {
                        left: '12px',
                      },
                      '& .swiper-button-next': {
                        right: '12px',
                      },
                    }}
                  >
                    <Swiper
                      modules={[Navigation]}
                      navigation={true}
                      style={{ height: '100%' }}
                      className={`outlets-swiper-mobile-${index}`}
                    >
                      {outlet.images.map((image, imageIndex) => (
                        <SwiperSlide key={imageIndex}>
                          <img
                            src={image.formats?.small || image.url}
                            alt={image.name || 'CapsuleTransit '}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                        </SwiperSlide>
                      ))}
                    </Swiper>
                    {/* Pagination Dots */}
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 16,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        display: 'flex',
                        gap: 1,
                      }}
                    >
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          bgcolor: 'white',
                          borderRadius: '50%',
                          opacity: 0.5,
                        }}
                      />
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          bgcolor: 'white',
                          borderRadius: '50%',
                        }}
                      />
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          bgcolor: 'white',
                          borderRadius: '50%',
                          opacity: 0.5,
                        }}
                      />
                    </Box>
                  </Box>

                  {/* Content */}
                  <Box sx={{ p: 2 }}>
                    <Typography
                      variant='h4'
                      sx={{
                        fontSize: '1.25rem',
                        fontWeight: 'bold',
                        mb: 1,
                        color: '#3B3F47',
                      }}
                    >
                      {outlet.title}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'grey.600',
                        mb: 2,
                        fontSize: '0.875rem',
                        lineHeight: 1.6,
                      }}
                    >
                      {outlet.description}
                    </Typography>
                    <Stack direction='row' spacing={1} sx={{ mb: 1.5 }}>
                      <Button
                        variant='contained'
                        size='small'
                        sx={{
                          bgcolor: '#e8f5e8',
                          color: '#223a8a',
                          fontWeight: 600,
                          textTransform: 'none',
                          borderRadius: '4px',
                          px: 2,
                          py: 0.5,
                          '&:hover': {
                            bgcolor: '#d4e8d4',
                          },
                        }}
                      >
                        Terminal 2
                      </Button>
                      <Button
                        variant='contained'
                        size='small'
                        onClick={handleOpenPublicAreaModal}
                        sx={{
                          bgcolor: '#e8f5e8',
                          color: '#223a8a',
                          fontWeight: 600,
                          textTransform: 'none',
                          borderRadius: '4px',
                          px: 2,
                          py: 0.5,
                          '&:hover': {
                            bgcolor: '#d4e8d4',
                          },
                        }}
                      >
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          Public Area
                          <InfoIcon />
                        </Box>
                      </Button>
                    </Stack>
                    {/* Room Types - Always Visible */}
                    <Box sx={{ mb: 2, mt: 4 }}>
                      <Typography
                        variant='caption'
                        sx={{
                          fontWeight: 600,
                          color: '#989CA4',
                          textTransform: 'uppercase',
                          letterSpacing: '0.05em',
                          display: 'block',
                          mb: 1.5,
                          fontSize: '12px',
                        }}
                      >
                        ROOM TYPE
                      </Typography>
                      <Stack spacing={2}>
                        {outlet.roomTypes.map((roomType, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                            }}
                          >
                            <Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: '#3B3F47',
                                  fontSize: '16px',
                                }}
                              >
                                {roomType.name}
                              </Typography>
                              <Typography
                                variant='caption'
                                sx={{ color: '#989CA4', fontSize: '12px' }}
                              >
                                {roomType.beds}
                              </Typography>
                            </Box>
                            <Typography
                              variant='body2'
                              sx={{
                                fontWeight: 400,
                                color: '#3B3F47',
                                fontSize: '16px',
                              }}
                            >
                              {roomType.priceFrom}
                            </Typography>
                          </Box>
                        ))}
                      </Stack>
                    </Box>

                    {/* Expandable Content */}
                    <Collapse in={expandedCard === 0}>
                      <Divider sx={{ my: 2 }} />
                      <Box>
                        <Typography
                          variant='caption'
                          sx={{
                            fontWeight: 600,
                            color: '#989CA4',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em',
                            mb: 1,
                            display: 'block',
                            fontSize: '12px',
                          }}
                        >
                          FACILITIES
                        </Typography>
                        <Box
                          sx={{
                            display: 'grid',
                            gridTemplateColumns: {
                              xs: '1fr',
                              sm: '1fr 1fr',
                              lg: '1fr 1fr 1fr',
                            },
                            gap: '12px',
                            alignItems: 'start',
                          }}
                        >
                          {outlet.facilities.map((facility, index) => (
                            <Box
                              key={index}
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                              }}
                            >
                              <Box
                                sx={{
                                  width: 18,
                                  height: 18,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexShrink: 0,
                                  color: '#5C68B5',
                                }}
                              >
                                <Image
                                  src={facility.icon}
                                  alt={facility.name}
                                  width={18}
                                  height={18}
                                />
                              </Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: '#333',
                                  fontSize: { xs: '13px', md: '14px' },
                                  lineHeight: 1.4,
                                }}
                              >
                                {facility.name}
                              </Typography>
                            </Box>
                          ))}
                        </Box>
                      </Box>
                    </Collapse>

                    {/* Expand/Collapse Button */}
                    <Button
                      onClick={() => toggleCard(0)}
                      sx={{
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        py: 0,
                        color: '#011589',
                        fontWeight: 500,
                        fontSize: '0.875rem',
                      }}
                      endIcon={
                        <KeyboardArrowDownIcon
                          sx={{
                            fontSize: '40px !important',
                            transition: 'transform 0.2s',
                            transform:
                              expandedCard === 0
                                ? 'rotate(180deg)'
                                : 'rotate(0deg)',
                          }}
                        />
                      }
                    ></Button>

                    {/* Action Buttons */}
                    <Box
                      bgcolor={'#DADCEC'}
                      style={{ backgroundColor: '#DADCEC' }}
                    >
                      <Stack
                        spacing={1}
                        sx={{
                          mt: 2,
                          padding: '20px 10px',
                          textAlign: 'center',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Button
                          variant='outlined'
                          onClick={() => {
                            setSelectedOutlet(outlet);
                            handleOpenCheckAvailabilityModal();
                          }}
                          sx={{
                            borderColor: '#011589',
                            fontWeight: 'bold',
                            width: '100%',
                            backgroundColor: '#fff',
                            color: '#011589',
                            borderRadius: '4px',
                            '&:hover': {
                              bgcolor: '#011589',
                              color: 'white',
                            },
                          }}
                        >
                          BOOK NOW
                        </Button>
                        <Button
                          component='a'
                          href='#'
                          sx={{
                            color: '#011589',
                            textDecoration: 'underline',
                            fontWeight: 500,
                            textAlign: 'center',
                            width: 'fit-content',
                            margin: '0 auto',
                            display: 'block',
                            backgroundColor: 'transparent !important',
                            '&:hover': {
                              textDecoration: 'none',
                              backgroundColor: 'transparent !important',
                            },
                          }}
                        >
                          View Outlet
                        </Button>
                      </Stack>
                    </Box>
                  </Box>
                </Box>
              </StyledCard>
            </Box>
          ))}
        </Stack>
      </Container>

      {/* Public Area Modal */}
      <PublicAreaModal
        open={isPublicAreaModalOpen}
        onClose={handleClosePublicAreaModal}
        onOpenRestrictedAreaModal={() => {
          handleClosePublicAreaModal();
          handleOpenRestrictedAreaModal();
        }}
      />
      {/* Restricted Area Modal */}
      <RestrictedAreaModal
        open={isRestrictedAreaModalOpen}
        onClose={handleCloseRestrictedAreaModal}
        onOpenPublicAreaModal={() => {
          handleCloseRestrictedAreaModal();
          handleOpenPublicAreaModal();
        }}
      />
      {/* Check Availability Modal */}
      <CheckAvailabilityModal
        open={isCheckAvailabilityModalOpen}
        onClose={handleCloseCheckAvailabilityModal}
        outlet={selectedOutlet}
      />
    </Box>
  );
};

export default OutletsSection;
