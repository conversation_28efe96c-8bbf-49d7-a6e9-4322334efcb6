import React from 'react';
import Image from 'next/image';
import {
  Box,
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Divider,
  Link,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import publicArea from '@/assets/images/public-area.svg';
import { DesktopMapArea, MobileMapArea } from './MapArea';

interface PublicAreaModalProps {
  open: boolean;
  onClose: () => void;
  onOpenRestrictedAreaModal: () => void;
}

const PublicAreaModal: React.FC<PublicAreaModalProps> = ({
  open,
  onClose,
  onOpenRestrictedAreaModal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery('(max-width:768px)');

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='lg'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          maxWidth: '1200px',
          width: '100%',
          margin: '16px',
          maxHeight: '90vh',
          overflow: 'hidden',
        },
      }}
    >
      <DialogContent sx={{ padding: 0, position: 'relative' }}>
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: '16px',
            top: '16px',
            color: '#666',
            zIndex: 1,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
        <Box
          sx={{ 
            display: 'flex', 
            flexDirection: isMobile ? 'column-reverse' : 'row',
            maxHeight: 'calc(90vh - 32px)',
            overflow: 'auto'
          }}
        >
          {/* Left Side - Map Image and Link */}
          <Box
            sx={{
              flex: 1,
              padding: '32px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: isMobile ? 'auto' : '500px',
            }}
          >
            {/* Map Image */}
            <Box
              sx={{
                width: '100%',
                maxWidth: '600px',
                minHeight: '300px',
                position: 'relative',
                marginBottom: '16px',
                backgroundColor: 'transparent',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {isMobile ? <MobileMapArea /> : <DesktopMapArea />}
            </Box>

            {/* Link below map */}
            <Link
              onClick={onOpenRestrictedAreaModal}
              sx={{
                color: '#223a8a',
                textDecoration: 'none',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              ← What is the Restricted Area?
            </Link>
          </Box>

          {/* Right Side - Content */}
          <Box
            sx={{
              flex: 1,
              padding: '32px',
              backgroundColor: 'white',
              position: 'relative',
              minHeight: isMobile ? 'auto' : '500px',
            }}
          >
            {/* Title */}
            <Typography
              variant='h4'
              sx={{
                color: '#223a8a',
                fontSize: '1.75rem',
                fontWeight: 700,
                marginBottom: '16px',
                lineHeight: 1.2,
              }}
            >
              <Typography
                variant='body1'
                sx={{
                  color: '#1A1A1A',
                  fontSize: isMobile ? '24px' : '32px',
                  lineHeight: 1,
                  fontWeight: 500,
                }}
              >
                What is the{' '}
              </Typography>
              <Typography
                style={{ 
                  color: '#011589', 
                  fontSize: isMobile ? '24px' : '32px', 
                  fontWeight: 500 
                }}
              >
                Public Area?
              </Typography>
            </Typography>

            {/* Divider */}
            <Divider
              sx={{
                marginBottom: '24px',
                borderColor: '#E0E0E0',
                borderWidth: '1px',
              }}
            />

            {/* Description */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <Typography
                variant='body1'
                sx={{
                  color: '#333',
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                The public area is located{' '}
                <strong>before immigration and security checkpoints</strong>,
                and is open to everyone — including travelers, visitors, and the
                general public.
              </Typography>

              <Typography
                variant='body1'
                sx={{
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                <strong>No boarding pass is required</strong> to enter.
              </Typography>

              <Typography
                variant='body1'
                sx={{
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                Transiting passengers who wish to access outlets in the public
                area (like MAX or Landside) must exit the restricted zone, clear
                immigration, and later go through security again to board their
                next flight.
              </Typography>
            </Box>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PublicAreaModal;
