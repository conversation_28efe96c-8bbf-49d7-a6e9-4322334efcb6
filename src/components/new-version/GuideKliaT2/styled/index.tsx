import { Box, Typography, FormControlLabel, styled } from '@mui/material';

// Styled components
export const GuideContainer = styled(Box)(({ theme }) => ({
  padding: '40px 0',
  maxWidth: '1200px',
  margin: '0 auto',
  fontFamily: "'IBM Plex Sans', sans-serif",
  [theme.breakpoints.down('md')]: {
    padding: '60px 12px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 12px',
  },
}));

export const Title = styled(Typography)(({ theme }) => ({
  fontSize: '2.5rem',
  fontWeight: 600,
  color: '#1A1A1a',
  marginBottom: theme.spacing(2),
  [theme.breakpoints.down('md')]: {
    fontSize: '2rem',
    marginBottom: theme.spacing(1.5),
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.75rem',
    marginBottom: theme.spacing(1),
  },
}));

export const Instructions = styled(Typography)(({ theme }) => ({
  fontSize: '1.1rem',
  color: '#1A1A1a',
  marginBottom: theme.spacing(3),
  lineHeight: 1.6,
  [theme.breakpoints.down('md')]: {
    fontSize: '1rem',
    marginBottom: theme.spacing(2),
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.95rem',
    marginBottom: theme.spacing(1.5),
    lineHeight: 1.5,
  },
}));

export const LeftSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: '#FFFFFF',
  height: 'fit-content',
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(2.5),
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

export const RightSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(1.5),
  },
}));

export const RadioOption = styled(FormControlLabel)<{ bgcolor: string }>(
  ({ bgcolor, theme }) => ({
    margin: theme.spacing(0.5, 0),
    padding: theme.spacing(1.5, 2),
    borderRadius: '12px',
    backgroundColor: bgcolor,
    border: 'none',
    transition: 'all 0.3s ease',
    width: '100%',
    cursor: 'pointer',
    '&:hover': {
      opacity: 0.9,
      transform: 'translateY(-1px)',
    },
    '& .MuiFormControlLabel-label': {
      color: '#1A1A1a',
      fontWeight: 500,
      fontSize: '1rem',
      [theme.breakpoints.down('sm')]: {
        fontSize: '0.9rem',
      },
    },
    '& .MuiRadio-root': {
      color: '#FFFFFF',
      '&.Mui-checked': {
        color: '#FFFFFF',
      },
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.5),
      },
    },
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(1, 1.5),
      margin: theme.spacing(0.25, 0),
    },
  })
);
