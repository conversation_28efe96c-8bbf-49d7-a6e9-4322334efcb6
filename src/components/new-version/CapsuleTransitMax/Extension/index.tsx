'use client'

import React from 'react';
import { Box, Typography } from '@mui/material';
import LoungeAccessIcon from '@/assets/icons/icons/icon-lounge-access.svg';
import EquippedGymIcon from '@/assets/icons/icons/icon-equipped-gym.svg';
import BathFacilitiesIcon from '@/assets/icons/icons/icon-bath-facilities.svg';
import MeetingRoomIcon from '@/assets/icons/icons/icon-meeting-room.svg';
import Image from 'next/image';

interface AmenityItem {
  icon: React.ReactNode;
  label: string;
}

const Extension: React.FC = () => {

  const amenities: AmenityItem[] = [
    {
      icon: (
        <Image
          src={LoungeAccessIcon}
          alt='Lounge Access'
          width={80}
          height={80}
        />
      ),
      label: 'Lounge Access',
    },
    {
      icon: (
        <Image
          src={EquippedGymIcon}
          alt='Equipped Gym'
          width={80}
          height={80}
        />
      ),
      label: 'Fully-equipped Gym',
    },
    {
      icon: (
        <Image
          src={BathFacilitiesIcon}
          alt='Bath Facilities'
          width={80}
          height={80}
        />
      ),
      label: 'Steam Bath Facilities',
    },
    {
      icon: (
        <Image
          src={MeetingRoomIcon}
          alt='Meeting Room'
          width={80}
          height={80}
        />
      ),
      label: 'Meeting Room',
    },
  ];

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px',
        backgroundColor: '#0F1168', // Dark blue background
        padding: { xs: 2, md: 4 },
        width: '100%',
      }}
    >
      {/* Desktop Layout */}
      <Box
        sx={{
          display: { xs: 'none', md: 'flex' },
          alignItems: 'center',
          justifyContent: 'center',
          maxWidth: '1200px',
          width: '100%',
        }}
      >
        {amenities.map((amenity, index) => (
          <React.Fragment key={index}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                flex: 1,
                padding: 3,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 1,
                }}
              >
                {amenity.icon}
              </Box>
              <Typography
                variant='body2'
                sx={{
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 500,
                  fontSize: '1rem',
                  lineHeight: 1.2,
                }}
              >
                {amenity.label}
              </Typography>
            </Box>

            {/* Desktop Divider - only show between items, not after the last one */}
            {index < amenities.length - 1 && (
              <Box
                sx={{
                  width: '1px',
                  height: '80px',
                  backgroundColor: 'white',
                  opacity: 0.3,
                }}
              />
            )}
          </React.Fragment>
        ))}
      </Box>

      {/* Mobile Layout - 2x2 Grid with Plus Sign Dividers */}
      <Box
        sx={{
          display: { xs: 'grid', md: 'none' },
          gridTemplateColumns: '1fr 1fr',
          gridTemplateRows: '1fr 1fr',
          gap: 0,
          maxWidth: '600px',
          width: '100%',
          position: 'relative',
        }}
      >
        {/* Grid Items */}
        {amenities.map((amenity, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 2,
              position: 'relative',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 1,
              }}
            >
              {amenity.icon}
            </Box>
            <Typography
              variant='body2'
              sx={{
                color: 'white',
                textAlign: 'center',
                fontWeight: 500,
                fontSize: '0.875rem',
                lineHeight: 1.2,
              }}
            >
              {amenity.label}
            </Typography>
          </Box>
        ))}

        {/* Mobile Plus Sign Dividers */}
        {/* Vertical divider spanning full height */}
        <Box
          sx={{
            position: 'absolute',
            top: '0',
            left: '50%',
            width: '2px',
            height: '100%',
            backgroundColor: 'white',
            opacity: 0.3,
            transform: 'translateX(-50%)',
            zIndex: 1,
          }}
        />

        {/* Horizontal divider spanning full width */}
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '0',
            width: '100%',
            height: '2px',
            backgroundColor: 'white',
            opacity: 0.3,
            transform: 'translateY(-50%)',
            zIndex: 1,
          }}
        />
      </Box>
    </Box>
  );
};

export default Extension;
