'use client'

import Image from 'next/image';
import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import HeroBackground from '@/assets/images/bw_capsule_transit-max.png';
import MaxWord from '@/assets/icons/icons/max-word.svg';

const HeroSection = () => {
  const isHandheldDevice = useMediaQuery('(max-width:1050px)');

  return !isHandheldDevice ? <DesktopView /> : <HandheldView />;
};

const DesktopView = () => {
  return (
    <Box
      width={'100%'}
      sx={{ 
        position: 'relative', 
        height: 'calc(100vh - 76px)', // Account for navbar height
        minHeight: '500px', // Ensure minimum height
        maxHeight: 'calc(100vh - 76px)' 
      }}
    >
      <Image
        src={HeroBackground}
        alt='Capsule Transit MAX Hero'
        layout='fill'
        objectFit='cover'
        objectPosition='center'
        priority
      />
      {/* Dark overlay to emphasize text */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1,
        }}
      />
      <Box
        display={'flex'}
        height={'100%'}
        flexDirection={'column'}
        justifyContent={'center'}
        alignItems={'center'}
        width={'100%'}
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 2,
        }}
      >
        <Stack alignItems={'center'} spacing={2}>
          <Box>
            <Image src={MaxWord} alt='Max Word' width={336} height={160} />
          </Box>
          <Typography
            variant='h5'
            fontSize='25px'
            fontWeight={500}
            color='#FFFFFF'
            textAlign='center'
            sx={{
              textShadow: '1px 1px 3px rgba(0,0,0,0.6)',
              maxWidth: '468px',
              lineHeight: 1.2,
            }}
          >
            Experience the premium accommodation at KL International Airport
          </Typography>
        </Stack>
      </Box>
    </Box>
  );
};

const HandheldView = () => {
  const isSmallerDevice = useMediaQuery('(max-width:500px)');
  const isVerySmallDevice = useMediaQuery('(max-width:370px)');

  return (
    <Box
      width={'100%'}
      sx={{ 
        position: 'relative', 
        height: isSmallerDevice 
          ? 'calc(100vh - 76px)' // Full viewport minus navbar for small devices
          : 'calc(100vh - 100px)', // Slightly more space for larger mobile devices
        minHeight: isSmallerDevice ? '400px' : '450px', // Responsive minimum height
        maxHeight: 'calc(100vh - 80px)' // Ensure it doesn't exceed viewport
      }}
    >
      <Image
        src={HeroBackground}
        alt='Capsule Transit MAX Hero'
        layout='fill'
        objectFit='cover'
        objectPosition='center'
        priority
      />
      {/* Dark overlay to emphasize text */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1,
        }}
      />
      <Box
        display={'flex'}
        height={'100%'}
        flexDirection={'column'}
        justifyContent={'center'}
        alignItems={'center'}
        width={'100%'}
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          paddingX: 3,
          zIndex: 2,
          margin: '0 auto',
        }}
      >
        <Stack alignItems={'center'} spacing={isSmallerDevice ? 1.5 : 2}>
          <Box>
            <Image 
              src={MaxWord} 
              alt='Max Word' 
              width={isSmallerDevice ? 300 : 354} 
              height={isSmallerDevice ? 85 : 100} 
            />
          </Box>

          <Typography
            variant='h6'
            fontSize={isSmallerDevice ? '18px' : '20px'}
            fontWeight={600}
            color='#FFFFFF'
            textAlign='center'
            sx={{
              fontFamily: 'IBM Plex Sans, sans-serif',
              fontStyle: 'normal',
              lineHeight: '120%',
              letterSpacing: '-0.02em',
              textShadow: '1px 1px 3px rgba(0,0,0,0.6)',
              maxWidth: isSmallerDevice ? '280px' : '330px',
            }}
          >
            Experience the premium accommodation at KL International Airport
          </Typography>
        </Stack>
      </Box>
    </Box>
  );
};

export default HeroSection;
