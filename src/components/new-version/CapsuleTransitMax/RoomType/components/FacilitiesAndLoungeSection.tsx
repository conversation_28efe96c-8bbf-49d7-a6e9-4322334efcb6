'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { Check, ExpandMore } from '@mui/icons-material';
import Image from 'next/image';

export const FacilitiesAndAmenitiesAccordion = ({
  room,
  expanded,
  setExpanded,
}: {
  room: { roomDetails?: { facilities?: any[] }; [k: string]: any };
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
}) => {
  return (
    <>
      {/* Facilities and Amenities - Accordion */}
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        sx={{
          boxShadow: 'none',
          // border: '1px solid #E5E7EB',
          borderRadius: '4px',
          '&:before': {
            display: 'none',
          },
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMore />}
          sx={{
            backgroundColor: '#F2F3FD',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: '#F3F4F6',
            },
          }}
        >
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              textTransform: 'uppercase',
              fontSize: '12px',
              color: '#989CA4',
            }}
          >
            Facilities and Amenities
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 2 }}>
          <Grid container spacing={2}>
            {(room.roomDetails?.facilities || []).map((facility, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Typography
                  variant='body2'
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    color: 'text.primary',
                    fontSize: '0.875rem',
                  }}
                >
                  {facility.icon ? (
                    <Image
                      src={facility.icon as any}
                      alt={facility.name}
                      width={20}
                      height={20}
                      style={{
                        filter:
                          'brightness(0) saturate(100%) invert(6%) sepia(87%) saturate(4451%) hue-rotate(240deg) brightness(98%) contrast(101%)',
                      }}
                    />
                  ) : (
                    <Box
                      style={{
                        width: 20,
                        height: 20,
                        backgroundColor: '#EDF0F3',
                      }}
                    />
                  )}

                  {facility.name}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
    </>
  );
};

export const LoungePackageAccordion = ({
  room,
  expanded,
  setExpanded,
}: {
  room: { roomDetails?: { loungePackages?: string[] }; [k: string]: any };
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
}) => {
  return (
    <>
      {/* Lounge Package - Accordion */}
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        sx={{
          boxShadow: 'none',
          // border: '1px solid #E5E7EB',
          borderRadius: '4px',
          '&:before': {
            display: 'none',
          },
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMore />}
          sx={{
            backgroundColor: '#F2F3FD',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: '#F3F4F6',
            },
          }}
        >
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              textTransform: 'uppercase',
              fontSize: '12px',
              color: '#989CA4',
            }}
          >
            Lounge Package Included When You Book a Room
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 2 }}>
          <Grid container spacing={2}>
            {(room.roomDetails?.loungePackages || []).map((item, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Typography
                  variant='body2'
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    color: 'text.secondary',
                  }}
                >
                  <Check
                    sx={{
                      fontSize: '1rem',
                      color: '#808AC4',
                    }}
                  />
                  {item}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
    </>
  );
};
