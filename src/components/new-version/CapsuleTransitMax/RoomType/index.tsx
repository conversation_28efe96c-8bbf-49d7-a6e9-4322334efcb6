'use client';

import React, { useMemo, useState, useRef } from 'react';
import {
  Box,
  Typography,
  Grid,
  CardMedia,
  Button,
  Stack,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useMediaQuery,
} from '@mui/material';
import { Check, ExpandMore } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Thumbs, FreeMode } from 'swiper/modules';
import type { Swiper as SwiperType } from 'swiper';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';
import 'swiper/css/free-mode';

import theme from '@/assets/theme/theme';
import IconAccessTime from '@/assets/icons/icons/icon-access-time.svg';
import Image from 'next/image';
import { Outlet } from '@/actions/getOutlets/types';
import { ROOM_OPTIONS } from '@/constant/outlet';
import ModalRoomPhotos from '@/components/new-version/ModalRoomPhotos';
import CheckAvailabilityModal from '@/components/new-version/CheckAvailabilityModal';
import {
  FacilitiesAndAmenitiesAccordion,
  LoungePackageAccordion,
} from './components/FacilitiesAndLoungeSection';

interface RoomTypeProps {
  selectedOutlet?: Outlet;
}

const RoomType: React.FC<RoomTypeProps> = ({ selectedOutlet }) => {
  const roomOptions = useMemo(() => {
    return Object.keys(selectedOutlet?.imagesByRoomType || {})
      .map((roomType) => {
        const outlet = selectedOutlet?.imagesByRoomType?.[roomType]?.outlet;
        const roomTypeKey = `${outlet}_${roomType}`;
        const roomDetails = ROOM_OPTIONS[roomTypeKey];

        return {
          id: roomType,
          title: roomType
            .split(' ')
            .map(
              (word) =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            )
            .join(' '),
          images: selectedOutlet?.imagesByRoomType?.[roomType]?.images || [],
          roomDetails,
        };
      })
      .filter((r) => !!r.roomDetails);
  }, [selectedOutlet]);

  const muiTheme = useTheme();
  const [facilitiesExpanded, setFacilitiesExpanded] = useState(false);
  const [loungeExpanded, setLoungeExpanded] = useState(false);
  const [thumbsSwipers, setThumbsSwipers] = useState<{
    [key: string]: SwiperType | null;
  }>({});

  // Check if mobile view
  const isMobile = useMediaQuery('(max-width:768px)');

  // Helper function to set thumb swiper for specific room
  const setThumbSwiperForRoom = (roomId: string, swiper: SwiperType | null) => {
    setThumbsSwipers((prev) => ({
      ...prev,
      [roomId]: swiper,
    }));
  };

  const [isOpenRoomPhotosModal, setIsOpenRoomPhotosModal] = useState(false);
  const [currentRoom, setCurrentRoom] = useState<any>(null);
  const [isCheckAvailabilityModalOpen, setIsCheckAvailabilityModalOpen] =
    useState(false);

  const handleAllPhotosClick = (room: any) => {
    setCurrentRoom(room);
    setIsOpenRoomPhotosModal(true);
  };

  const handleOpenCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(true);
  };

  const handleCloseCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(false);
  };
  return (
    <Box sx={{ marginBottom: { xs: '40px', md: '60px' } }}>
      <ModalRoomPhotos
        isOpen={isOpenRoomPhotosModal}
        handleClose={() => setIsOpenRoomPhotosModal(false)}
        images={currentRoom?.images || []}
      />
      <CheckAvailabilityModal
        open={isCheckAvailabilityModalOpen}
        onClose={handleCloseCheckAvailabilityModal}
        outlet={selectedOutlet}
      />
      {roomOptions.map((room, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            py: { xs: 6, md: 8 }, // Responsive padding
            px: { xs: 2, md: 2 },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' }, // Stack vertically on mobile
              gap: { xs: 3, md: 4 },
              minHeight: '600px',
              margin: '0 auto',
              width: '100%',
              justifyContent: 'center',
            }}
          >
            {/* Left Section - Image Gallery */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: { xs: '100%', md: '658px' },
                width: '100%',
                order: { xs: 1, md: 1 }, // First on mobile
              }}
            >
              {/* Room Title */}
              <Typography
                variant='h3'
                sx={{
                  fontWeight: 500,
                  color: '#011589',
                  mb: 2,
                  fontSize: { xs: '32px', md: '40px' }, // Smaller on mobile
                }}
              >
                {room.title}
              </Typography>

              {/* Main Image Slider with Swiper */}
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: { xs: '300px', md: '400px' },
                  '& .swiper': {
                    width: '100%',
                    height: '100%',
                    borderRadius: 1,
                  },
                  '& .swiper-slide': {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                  '& .swiper-button-next, & .swiper-button-prev': {
                    backgroundColor: 'rgba(15, 14, 14, 0.5)',
                    width: '32px',
                    height: '48px',
                    boxShadow: theme.shadows[4],
                    transition: 'all 0.2s',
                    borderRadius: '4px',
                    '&:hover': {
                      backgroundColor: 'rgba(15, 14, 14, 0.8)',
                    },
                    '&::after': {
                      fontSize: '18px',
                      color: 'white',
                      fontWeight: 'bold',
                    },
                    '&:after': {
                      fontSize: '20px',
                      fontWeight: 'bold',
                    },
                  },
                  '& .swiper-button-next': {
                    right: '8px',
                  },
                  '& .swiper-button-prev': {
                    left: '8px',
                  },
                }}
              >
                {room.images.length > 0 ? (
                  <Swiper
                    spaceBetween={10}
                    navigation={room.images.length > 1}
                    thumbs={{
                      swiper:
                        thumbsSwipers[room.id] &&
                        !thumbsSwipers[room.id]?.destroyed
                          ? thumbsSwipers[room.id]
                          : null,
                    }}
                    modules={[FreeMode, Navigation, Thumbs]}
                    className='main-swiper'
                  >
                    {room.images.map((image, index) => (
                      <SwiperSlide key={index}>
                        <CardMedia
                          component='img'
                          image={image.url}
                          alt={image.name}
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            borderRadius: 1,
                          }}
                        />
                      </SwiperSlide>
                    ))}
                  </Swiper>
                ) : (
                  <Box
                    sx={{
                      width: '100%',
                      height: '100%',
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 1,
                    }}
                  >
                    <Typography color='text.secondary'>
                      No image available
                    </Typography>
                  </Box>
                )}
              </Box>

              {/* Thumbnail Images Swiper - Only show when there are multiple images */}
              {room.images.length > 1 && (
                <Box
                  sx={{
                    '& .swiper': {
                      width: '100%',
                      // height: '70px',
                      padding: '8px 4px',
                    },
                    '& .swiper-slide': {
                      width: 'auto',
                      opacity: 0.8,
                      transition: 'opacity 0.3s ease',
                      '&:hover': {
                        opacity: 1,
                      },
                    },
                    '& .swiper-slide-thumb-active': {
                      opacity: '1 !important',
                    },
                  }}
                >
                  <Swiper
                    onSwiper={(swiper) =>
                      setThumbSwiperForRoom(room.id, swiper)
                    }
                    spaceBetween={8}
                    slidesPerView={'auto'}
                    freeMode={true}
                    watchSlidesProgress={true}
                    modules={[FreeMode, Navigation, Thumbs]}
                    className='thumbs-swiper'
                  >
                    {room.images.map((image, index) => (
                      <SwiperSlide key={index} style={{ width: 'auto' }}>
                        <Box
                          className='thumbnail-box'
                          sx={(theme) => ({
                            width: '75px',
                            height: '60px',
                            borderRadius: '4px',
                            overflow: 'hidden',
                            border: '1px solid',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            backgroundColor: '#f5f5f5',
                            transform: 'scale(1)',
                            // Active state styles
                            '.swiper-slide-thumb-active &': {
                              borderColor: '#011589',
                              transform: 'scale(1.02)',
                            },
                            // Hover state styles
                            '.swiper-slide:hover &': {
                              borderColor: '#011589',
                              transform: 'scale(1.05)',
                            },
                            // Active + Hover state
                            '.swiper-slide-thumb-active:hover &': {
                              transform: 'scale(1.08)',
                            },
                          })}
                        >
                          <CardMedia
                            component='img'
                            image={image.url}
                            alt={image.name}
                            sx={{
                              width: '75px',
                              height: '60px',
                              objectFit: 'cover',
                              transition: 'transform 0.3s ease',
                            }}
                          />
                        </Box>
                      </SwiperSlide>
                    ))}
                  </Swiper>
                </Box>
              )}

              {/* All Room Photos Button - Only show when there are images */}
              {room.images.length > 0 && (
                <Button
                  onClick={() => handleAllPhotosClick(room)}
                  sx={{
                    alignSelf: 'flex-start',
                    color: '#011589',
                    fontWeight: 500,
                    fontSize: { xs: '14px', md: '15px' },
                    textDecoration: 'underline',
                    '&:hover': {
                      textDecoration: 'none',
                    },
                  }}
                >
                  All room photos
                </Button>
              )}

              {!isMobile && (
                <>
                  <FacilitiesAndAmenitiesAccordion
                    room={room}
                    expanded={facilitiesExpanded}
                    setExpanded={setFacilitiesExpanded}
                  />
                  <LoungePackageAccordion
                    room={room}
                    expanded={loungeExpanded}
                    setExpanded={setLoungeExpanded}
                  />
                </>
              )}
            </Box>

            {/* Right Section - Room Details */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 3,
                maxWidth: { xs: '100%', md: '658px' },
                width: '100%',
                paddingTop: { xs: 0, md: '70px' }, // No top padding on mobile
                order: { xs: 2, md: 2 }, // Second on mobile
              }}
            >
              {/* Mobile Layout: Room Description and Specifications side by side */}
              {isMobile ? (
                <Box>
                  {/* First Section: Room Description | Divider | Room Specifications */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: 2,
                      mb: 3,
                    }}
                  >
                    {/* Room Description */}
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant='h4'
                        sx={{
                          fontWeight: 500,
                          color: '#3B3F47',
                          lineHeight: 1.1,
                          fontSize: { xs: '16px', md: '28px' },
                        }}
                      >
                        {room.roomDetails?.description}
                      </Typography>
                    </Box>

                    {/* Divider */}
                    <Divider
                      orientation='vertical'
                      flexItem
                      sx={{ borderColor: '#D8DDE2' }}
                    />

                    {/* Room Specifications */}
                    <Box sx={{ flex: 1 }}>
                      <Stack spacing={1}>
                        <Typography variant='body1' color='text.secondary'>
                          <span
                            style={{
                              color: '#989CA4',
                              fontWeight: 600,
                              fontSize: '12px',
                            }}
                          >
                            ROOM FOR:
                          </span>{' '}
                          <span
                            style={{
                              color: '#3B3F47',
                              fontWeight: 500,
                              fontSize: '16px',
                            }}
                          >
                            {room.roomDetails?.capacity}
                          </span>
                        </Typography>
                        <Typography variant='body1' color='text.secondary'>
                          <span
                            style={{
                              color: '#989CA4',
                              fontWeight: 600,
                              fontSize: '12px',
                            }}
                          >
                            BED TYPE:
                          </span>{' '}
                          <span
                            style={{
                              color: '#3B3F47',
                              fontWeight: 500,
                              fontSize: '16px',
                            }}
                          >
                            {room.roomDetails?.bedType}
                          </span>
                        </Typography>
                        <Typography variant='body1' color='text.secondary'>
                          <span
                            style={{
                              color: '#989CA4',
                              fontWeight: 600,
                              fontSize: '12px',
                            }}
                          >
                            ROOM SIZE:
                          </span>{' '}
                          <span
                            style={{
                              color: '#3B3F47',
                              fontWeight: 500,
                              fontSize: '16px',
                            }}
                          >
                            {room.roomDetails?.roomSize}
                          </span>
                        </Typography>
                      </Stack>
                    </Box>
                  </Box>
                </Box>
              ) : (
                /* Desktop Layout: Original stacked layout */
                <>
                  {/* Room Description */}
                  <Typography
                    variant='h4'
                    sx={{
                      fontWeight: 500,
                      color: '#3B3F47',
                      lineHeight: 1.1,
                      fontSize: { xs: '16px', md: '28px' },
                    }}
                  >
                    {room.roomDetails?.description}
                  </Typography>

                  {/* Room Specifications */}
                  <Stack spacing={1}>
                    <Typography variant='body1' sx={{ lineHeight: 1.2 }}>
                      <span
                        style={{
                          color: '#989CA4',
                          fontWeight: 600,
                          fontSize: '12px',
                        }}
                      >
                        ROOM FOR:
                      </span>{' '}
                      <span
                        style={{
                          color: '#3B3F47',
                          fontWeight: 500,
                          fontSize: '16px',
                        }}
                      >
                        {room.roomDetails?.capacity}
                      </span>
                    </Typography>
                    <Typography variant='body1' sx={{ lineHeight: 1.2 }}>
                      <span
                        style={{
                          color: '#989CA4',
                          fontWeight: 600,
                          fontSize: '12px',
                        }}
                      >
                        BED TYPE:
                      </span>{' '}
                      <span
                        style={{
                          color: '#3B3F47',
                          fontWeight: 500,
                          fontSize: '16px',
                        }}
                      >
                        {room.roomDetails?.bedType}
                      </span>
                    </Typography>
                    <Typography variant='body1' sx={{ lineHeight: 1.2 }}>
                      <span
                        style={{
                          color: '#989CA4',
                          fontWeight: 600,
                          fontSize: '12px',
                        }}
                      >
                        ROOM SIZE:
                      </span>{' '}
                      <span
                        style={{
                          color: '#3B3F47',
                          fontWeight: 500,
                          fontSize: '16px',
                        }}
                      >
                        {room.roomDetails?.roomSize}
                      </span>
                    </Typography>
                  </Stack>

                  <Divider sx={{ borderColor: '#D8DDE2', my: 2 }} />
                </>
              )}

              {isMobile && (
                <>
                  <FacilitiesAndAmenitiesAccordion
                    room={room}
                    expanded={facilitiesExpanded}
                    setExpanded={setFacilitiesExpanded}
                  />
                  <LoungePackageAccordion
                    room={room}
                    expanded={loungeExpanded}
                    setExpanded={setLoungeExpanded}
                  />
                </>
              )}

              {/* Pricing by Hours */}
              <Box
                sx={{
                  // marginTop: '20px',
                  backgroundColor: '#F2F3FD',
                  borderRadius: '8px',
                  padding: { xs: '20px', md: '28px' },
                }}
              >
                <Typography
                  variant='h6'
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    textTransform: 'uppercase',
                    fontSize: '12px',
                    color: '#989CA4',
                  }}
                >
                  Book by the Hours
                </Typography>
                <Box
                  sx={{
                    p: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                  }}
                >
                  {(room.roomDetails?.prices || []).map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        py: 0.5,
                      }}
                    >
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        <Image
                          src={IconAccessTime}
                          alt='Access Time'
                          width={14}
                          height={14}
                        />
                        <Typography
                          variant='body2'
                          sx={{
                            color: '#3B3F47',
                            fontWeight: 500,
                            fontSize: { xs: '18px', md: '20px' },
                            fontStyle: 'normal',
                            lineHeight: '10px',
                          }}
                        >
                          {item.hours} Hours
                        </Typography>
                      </Box>
                      <Typography
                        variant='body1'
                        sx={{
                          color: '#011589',
                          fontWeight: 500,
                          fontSize: { xs: '20px', md: '24px' },
                          fontStyle: 'normal',
                        }}
                      >
                        RM{item.price}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Book Button */}
              <Button
                variant='contained'
                onClick={handleOpenCheckAvailabilityModal}
                sx={{
                  // mt: 'auto',
                  bgcolor: '#011589',
                  color: 'white',
                  py: { xs: 1.5, md: 1 },
                  px: 4,
                  fontSize: { xs: '16px', md: '14px' },
                  fontWeight: 600,
                  maxWidth: { xs: '100%', md: '200px' },
                  width: '100%',
                  borderRadius: '6px',
                  '&:hover': {
                    bgcolor: '#011589',
                  },
                }}
              >
                BOOK
              </Button>
            </Box>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default RoomType;
