// Import images for the carousel
import MaxDisplay1 from '@/assets/images/max_k8.png';
import MaxDisplay2 from '@/assets/images/max_k2.png';
import MaxDisplay3 from '@/assets/images/max_k3.png';
import DeluxeRoom from '@/assets/images/max_k4.png';
import SuiteRoom from '@/assets/images/max_k5.png';
import Lounge1 from '@/assets/images/max_k6.png';

export interface SlideData {
  id: number;
  image: any;
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  primaryButtonText: string;
  secondaryButtonText: string;
}

export const PROPER_PLACES: SlideData[] = [
  {
    id: 1,
    image: MaxDisplay1,
    title:
      'CapsuleTransit MAX offers a proper place to breathe before or after takeoff.',
    subtitle: 'Launched in September 2024',
    description:
      "What you'll get when you book a room or getting a Lounge Access Pass:",
    features: [
      'Access to our lounge',
      'A plant-based meal',
      'Free flow buffet nibbles and drinks',
      'Your drink of choice (cocktails / kombucha / craft beer)',
      'The meeting room',
      'Steam bath facilities',
      'The fully-equipped gym',
    ],
    primaryButtonText: 'BOOK A ROOM AT MAX',
    secondaryButtonText: 'GET LOUNGE ACCESS PASS',
  },
  {
    id: 2,
    image: MaxDisplay2,
    title: 'Premium accommodation with world-class amenities',
    subtitle: 'Experience luxury at its finest',
    description: 'Indulge in our premium services and facilities:',
    features: [
      '24/7 concierge service',
      'Complimentary high-speed WiFi',
      'Premium bedding and linens',
      'In-room dining service',
      'Fitness center access',
      'Business center facilities',
      'Airport shuttle service',
    ],
    primaryButtonText: 'EXPLORE ROOM TYPES',
    secondaryButtonText: 'VIEW AMENITIES',
  },
  {
    id: 3,
    image: MaxDisplay3,
    title: 'Your perfect transit companion at KLIA',
    subtitle: 'Conveniently located at the airport',
    description: 'Why choose CapsuleTransit MAX for your journey:',
    features: [
      'Minutes away from departure gates',
      'Flexible check-in and check-out',
      'Secure luggage storage',
      'Quiet and comfortable environment',
      'Professional staff assistance',
      'Competitive pricing',
      'Easy online booking',
    ],
    primaryButtonText: 'CHECK AVAILABILITY',
    secondaryButtonText: 'VIEW LOCATION',
  },
  {
    id: 4,
    image: DeluxeRoom,
    title: 'Deluxe rooms designed for your comfort',
    subtitle: 'Spacious and modern accommodations',
    description: 'Our deluxe rooms feature:',
    features: [
      'Premium queen-size bed',
      'En-suite bathroom',
      'Work desk and chair',
      'Smart TV with streaming',
      'Individual climate control',
      'Soundproof walls',
      'Complimentary toiletries',
    ],
    primaryButtonText: 'BOOK DELUXE ROOM',
    secondaryButtonText: 'VIEW ROOM DETAILS',
  },
  {
    id: 5,
    image: SuiteRoom,
    title: 'Luxury suite experience',
    subtitle: 'The ultimate in airport accommodation',
    description: 'Our luxury suites include:',
    features: [
      'Separate living area',
      'King-size bed',
      'Premium bathroom amenities',
      'Mini bar and coffee station',
      'Lounge access included',
      'Priority check-in service',
      'Complimentary breakfast',
    ],
    primaryButtonText: 'BOOK SUITE',
    secondaryButtonText: 'SUITE AMENITIES',
  },
  {
    id: 6,
    image: Lounge1,
    title: 'Exclusive lounge access',
    subtitle: 'Relax and refresh in style',
    description: 'Lounge access includes:',
    features: [
      'Comfortable seating areas',
      'Complimentary refreshments',
      'Business facilities',
      'Shower rooms',
      'WiFi and charging stations',
      'Flight information display',
      'Professional staff service',
    ],
    primaryButtonText: 'GET LOUNGE ACCESS',
    secondaryButtonText: 'VIEW LOUNGE FEATURES',
  },
];
