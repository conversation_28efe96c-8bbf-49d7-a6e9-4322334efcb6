import React, { useState } from 'react';
import Image from 'next/image';
import { useF<PERSON>, Controller } from 'react-hook-form';
import {
  Box,
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Divider,
  FormControl,
  Select,
  MenuItem,
  Button,
  Stack,
  useMediaQuery,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { SlideData } from '../constant';
import {
  calculateCheckoutTime,
} from '@/components/new-version/HeroSection/utils';
import { ArrowDropDownIcon, DatePicker } from '@mui/x-date-pickers';
import { getCompatibleTimes } from '@/components/booking/constant/TimeSelection';
import { DURATIONS } from '@/components/new-version/HeroSection/constant';
import { useRouter } from 'next/navigation';
import CheckoutTime from '@/components/new-version/CheckoutTime';
import { Outlet } from '@/actions/getOutlets/types';
import ImageSide from '@/assets/images/max_k7.png';

interface RoomBookingModalProps {
  open: boolean;
  onClose: () => void;
  slideData: SlideData;
  selectedOutlet?: Outlet;
}

interface FormData {
  date: Date | null;
  checkInTime: string;
  stayDuration: string;
}

const RoomBookingModal: React.FC<RoomBookingModalProps> = ({
  open,
  onClose,
  slideData,
  selectedOutlet,
}) => {

  const router = useRouter();
  // Get current date and time in Malaysia timezone (UTC+8)
  const getCurrentMalaysiaDateTime = () => {
    const now = new Date();
    const malaysiaTimeOffset = 8 * 60; // Malaysia is UTC+8
    const localOffset = now.getTimezoneOffset(); // Get the local offset in minutes
    return new Date(now.getTime() + (malaysiaTimeOffset + localOffset) * 60000);
  };

  const currentDateTime = getCurrentMalaysiaDateTime();
  // Set minDate to start of current day in Malaysia timezone
  const currentDateOnly = new Date(currentDateTime);
  currentDateOnly.setHours(0, 0, 0, 0);

  const [openDatePicker, setOpenDatePicker] = useState(false);
  const isMobile = useMediaQuery('(max-width:768px)');

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      date: null,
      checkInTime: '',
      stayDuration: '',
    },
    mode: 'onChange',
  });

  const watchedDate = watch('date');
  const watchedCheckInTime = watch('checkInTime');

  const checkoutTime = calculateCheckoutTime({
    date: watchedDate,
    checkinTime: watchedCheckInTime,
    duration: Number(watch('stayDuration')),
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const onSubmit = (data: FormData) => {
    try {
      console.log('Checking availability with:', data);

      // Convert date to Unix timestamp (seconds since epoch)
      if (!data.date) {
        return;
      }

      setIsSubmitting(true);
      const checkInDatetime = Math.floor(data.date.getTime() / 1000);

      // Redirect to the booking page with query parameters
      const queryParams = new URLSearchParams({
        lotId: selectedOutlet?.lotId?.toString() || '',
        date: data.date.toISOString(),
        checkInTime: data.checkInTime,
        duration: data.stayDuration.toString(),
        checkInDatetime: checkInDatetime.toString(),
      });

      router.push(`/new-booking?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error checking availability:', error);
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth='lg'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          maxWidth: '900px',
          width: '100%',
          margin: '16px',
        },
      }}
      slotProps={{
        backdrop: {
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.8) !important',
            backdropFilter: 'blur(4px)',
          },
        },
      }}
    >
      <DialogContent sx={{ padding: 0, overflow: 'hidden' }}>
        <Box
          sx={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row' }}
        >
          {/* Left Side - Image */}
          <Box
            sx={{
              flex: 1,
              minHeight: isMobile ? '300px' : '500px',
              position: 'relative',
              backgroundColor: '#f5f5f5',
            }}
          >
            <Image
              src={ImageSide}
              alt={'Room Booking'}
              fill
              style={{
                objectFit: 'cover',
              }}
            />
          </Box>

          {/* Right Side - Booking Options */}
          <Box
            sx={{
              flex: 1,
              padding: '32px',
              backgroundColor: 'white',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            {/* Close Button */}
            <IconButton
              onClick={handleClose}
              sx={{
                position: 'absolute',
                right: '16px',
                top: '16px',
                color: '#001689',
                backgroundColor: 'rgba(0, 22, 137, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 22, 137, 0.2)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>

            {/* Content */}
            <Box sx={{ marginTop: '20px' }}>
              {/* Subtitle */}
              <Typography
                variant='body2'
                sx={{
                  color: '#666',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  marginBottom: '8px',
                }}
              >
                Room Booking
              </Typography>

              {/* Main Title */}
              <Typography
                variant='h4'
                sx={{
                  color: '#001689',
                  fontSize: '1.5rem',
                  fontWeight: 700,
                  marginBottom: '16px',
                  lineHeight: 1.2,
                }}
              >
                CapsuleTransit MAX
              </Typography>

              {/* Divider */}
              <Divider sx={{ marginBottom: '24px', borderColor: '#E0E0E0' }} />

              {/* Form */}
              <Box component='form' onSubmit={handleSubmit(onSubmit)}>
                <Stack spacing={3}>
                  {/* Date Selection */}
                  <Box>
                    <Typography
                      variant='caption'
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: 'text.secondary',
                        display: 'block',
                      }}
                    >
                      DATE
                    </Typography>
                    <FormControl fullWidth error={!!errors.date}>
                      <Controller
                        name='date'
                        control={control}
                        rules={{
                          required: 'Date is required',
                          validate: (value) => {
                            if (!value) return 'Date is required';
                            if (value < currentDateOnly)
                              return 'Date cannot be in the past';
                            return true;
                          },
                        }}
                        render={({ field }) => (
                          <DatePicker
                            open={openDatePicker}
                            onClose={() => setOpenDatePicker(false)}
                            slotProps={{
                              textField: {
                                onClick: () => setOpenDatePicker(true),
                                error: !!errors.date,
                                helperText: errors.date?.message,
                                placeholder: 'Select date',
                              },
                            }}
                            value={field.value}
                            onChange={(newValue) => {
                              field.onChange(newValue);
                            }}
                            format='dd/MM/yyyy'
                            slots={{
                              openPickerIcon: ArrowDropDownIcon,
                            }}
                            minDate={currentDateOnly}
                            sx={{
                              fontWeight: 600,
                              maxHeight: '42px',
                              '& .MuiInputBase-root': {
                                maxHeight: '42px',
                              },
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Box>

                  {/* Check-in Time Selection */}
                  <Box>
                    <Typography
                      variant='caption'
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: 'text.secondary',
                        display: 'block',
                      }}
                    >
                      CHECK-IN TIME
                    </Typography>
                    <FormControl fullWidth error={!!errors.checkInTime}>
                      <Controller
                        name='checkInTime'
                        control={control}
                        rules={{
                          required: 'Check-in time is required',
                        }}
                        render={({ field }) => (
                          <Select
                            {...field}
                            disabled={!watchedDate}
                            value={field.value}
                            onChange={field.onChange}
                            displayEmpty
                            MenuProps={{
                              PaperProps: {
                                sx: {
                                  maxHeight: 200,
                                  zIndex: 9999,
                                },
                              },
                            }}
                            sx={{
                              '& .MuiSelect-select': {
                                py: { xs: 1.25, md: 1.5 },
                                px: { xs: 1.5, md: 2 },
                                fontSize: { xs: '0.875rem', md: '1rem' },
                                color: !watchedDate
                                  ? 'text.disabled'
                                  : 'text.primary',
                                backgroundColor: !watchedDate
                                  ? 'grey.100'
                                  : 'white',
                              },
                              '& .MuiOutlinedInput-root': {
                                bgcolor: !watchedDate ? 'grey.100' : 'white',
                                '&:hover': {
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: !watchedDate
                                      ? 'grey.300'
                                      : 'primary.main',
                                  },
                                },
                                '&.Mui-focused': {
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: 'primary.main',
                                    borderWidth: '2px',
                                  },
                                },
                              },
                              maxHeight: '42px',
                              '& .MuiSelect-icon': {
                                color: !watchedDate
                                  ? 'text.disabled'
                                  : 'text.primary',
                              },
                            }}
                          >
                            <MenuItem value='' disabled>
                              {!watchedDate
                                ? 'Select date first'
                                : 'Select check-in time'}
                            </MenuItem>
                            {watchedDate &&
                              getCompatibleTimes(watchedDate).map((time) => (
                                <MenuItem key={time} value={time}>
                                  {time}
                                </MenuItem>
                              ))}
                          </Select>
                        )}
                      />
                      {errors.checkInTime && (
                        <Typography
                          sx={{
                            fontSize: '12px',
                            color: 'error.main',
                            mt: 0.5,
                          }}
                        >
                          {errors.checkInTime.message}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>

                  {/* Stay Duration Selection */}
                  <Box>
                    <Typography
                      variant='caption'
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: 'text.secondary',
                        display: 'block',
                      }}
                    >
                      STAY DURATION
                    </Typography>
                    <FormControl fullWidth error={!!errors.stayDuration}>
                      <Controller
                        name='stayDuration'
                        control={control}
                        rules={{
                          required: 'Stay duration is required',
                        }}
                        render={({ field }) => (
                          <Select
                            {...field}
                            disabled={!watchedDate || !watchedCheckInTime}
                            value={field.value}
                            onChange={field.onChange}
                            displayEmpty
                            MenuProps={{
                              PaperProps: {
                                sx: {
                                  maxHeight: 200,
                                  zIndex: 9999,
                                },
                              },
                            }}
                            sx={{
                              '& .MuiSelect-select': {
                                py: { xs: 1.25, md: 1.5 },
                                px: { xs: 1.5, md: 2 },
                                fontSize: { xs: '0.875rem', md: '1rem' },
                                color:
                                  !watchedDate || !watchedCheckInTime
                                    ? 'text.disabled'
                                    : 'text.primary',
                                backgroundColor:
                                  !watchedDate || !watchedCheckInTime
                                    ? 'grey.100'
                                    : 'white',
                              },
                              '& .MuiOutlinedInput-root': {
                                bgcolor:
                                  !watchedDate || !watchedCheckInTime
                                    ? 'grey.100'
                                    : 'white',
                                '&:hover': {
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor:
                                      !watchedDate || !watchedCheckInTime
                                        ? 'grey.300'
                                        : 'primary.main',
                                  },
                                },
                                '&.Mui-focused': {
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: 'primary.main',
                                    borderWidth: '2px',
                                  },
                                },
                              },
                              maxHeight: '42px',
                              '& .MuiSelect-icon': {
                                color:
                                  !watchedDate || !watchedCheckInTime
                                    ? 'text.disabled'
                                    : 'text.primary',
                              },
                            }}
                          >
                            <MenuItem value='' disabled>
                              {!watchedDate || !watchedCheckInTime
                                ? 'Please select date and check-in time first'
                                : 'Select stay duration'}
                            </MenuItem>
                            {DURATIONS.map((time) => (
                              <MenuItem key={time.value} value={time.value}>
                                {time.label}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                      {errors.stayDuration && (
                        <Typography
                          sx={{
                            fontSize: '12px',
                            color: 'error.main',
                            mt: 0.5,
                          }}
                        >
                          {errors.stayDuration.message}
                        </Typography>
                      )}
                      {(!watchedDate || !watchedCheckInTime) && (
                        <Typography
                          sx={{
                            fontSize: '14px',
                            marginTop: '4px',
                            color: 'text.secondary',
                          }}
                        >
                          Please select date and check-in time first
                        </Typography>
                      )}
                      {watchedDate &&
                        watchedCheckInTime &&
                        watch('stayDuration') &&
                        checkoutTime && (
                          <CheckoutTime checkoutTime={checkoutTime} />
                        )}
                    </FormControl>
                  </Box>
                </Stack>

                {/* Check Availability Button */}
                <Button
                  variant='contained'
                  type='submit'
                  fullWidth
                  disabled={!isValid || isSubmitting}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    fontWeight: 'bold',
                    px: { xs: 3, md: 4 },
                    py: { xs: 1.25, md: 1.5 },
                    borderRadius: 2,
                    boxShadow: 2,
                    marginTop: '32px',
                    fontSize: '14px',
                    whiteSpace: 'nowrap',
                    maxHeight: '42px',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                      transform: 'translateY(-1px)',
                      boxShadow: 4,
                    },
                    '&:disabled': {
                      bgcolor: 'grey.300',
                      color: 'text.disabled',
                    },
                  }}
                >
                  {isSubmitting ? 'CHECKING...' : 'CHECK AVAILABILITY'}
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default RoomBookingModal;
