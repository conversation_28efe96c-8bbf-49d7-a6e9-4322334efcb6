'use client'

import React, { useState } from 'react';
import Image from 'next/image';
import {
  Box,
  Stack,
  Typography,
  Button,
  IconButton,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { SlideData, PROPER_PLACES } from './constant';
import RoomBookingModal from './components/RoomBookingModal';
import { Outlet } from '@/actions/getOutlets/types';
import ModalRoomPhotos from '../../ModalRoomPhotos';

const ProperPlace = ({ selectedOutlet }: { selectedOutlet?: Outlet }) => {
  const theme = useTheme();
  const isHandheldDevice = useMediaQuery('(max-width:1050px)');
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalPhotosOpen, setIsModalPhotosOpen] = useState(false);

  const handleNext = () => {
    setCurrentSlide((prev) => (prev + 1) % PROPER_PLACES.length);
  };

  const handlePrev = () => {
    setCurrentSlide((prev) => (prev - 1 + PROPER_PLACES.length) % PROPER_PLACES.length);
  };

  const handleSlideClick = (index: number) => {
    setCurrentSlide(index);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleOpenModalPhotos = () => {
    setIsModalPhotosOpen(true);
  };

  const handleCloseModalPhotos = () => {
    setIsModalPhotosOpen(false);
  };

  const currentSlideData = PROPER_PLACES[currentSlide];

  return (
    <>
      {!isHandheldDevice ? (
        <DesktopView
          currentSlideData={currentSlideData}
          currentSlide={currentSlide}
          totalSlides={PROPER_PLACES.length}
          onNext={handleNext}
          onPrev={handlePrev}
          onSlideClick={handleSlideClick}
          onOpenModal={handleOpenModal}
          selectedOutlet={selectedOutlet}
          onOpenPhotos={handleOpenModalPhotos}
        />
      ) : (
        <HandheldView
          currentSlideData={currentSlideData}
          currentSlide={currentSlide}
          totalSlides={PROPER_PLACES.length}
          onNext={handleNext}
          onPrev={handlePrev}
          onSlideClick={handleSlideClick}
          onOpenModal={handleOpenModal}
          selectedOutlet={selectedOutlet}
          onOpenPhotos={handleOpenModalPhotos}
        />
      )}
      
      <RoomBookingModal
        open={isModalOpen}
        onClose={handleCloseModal}
        slideData={currentSlideData}
        selectedOutlet={selectedOutlet}
      />
      <ModalRoomPhotos
        handleClose={handleCloseModalPhotos}
        images={selectedOutlet?.images || []}
        isOpen={isModalPhotosOpen}
      />
    </>
  );
};

interface ViewProps {
  currentSlideData: SlideData;
  currentSlide: number;
  totalSlides: number;
  onNext: () => void;
  onPrev: () => void;
  onSlideClick: (index: number) => void;
  onOpenModal: () => void;
  selectedOutlet?: Outlet;  
  onOpenPhotos?: () => void;
}

const DesktopView: React.FC<ViewProps> = ({
  currentSlideData,
  currentSlide,
  totalSlides,
  onNext,
  onPrev,
  onSlideClick,
  onOpenModal,
  selectedOutlet,
  onOpenPhotos,
}) => {
  const theme = useTheme();

  return (
    <Box
      width='100%'
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 'calc(100vh - 80px)', // Account for navbar height
        backgroundColor: '#fff',
        padding: '60px 16px',
      }}
    >
      {/* Container with max width and gap */}
      <Box
        sx={{
          display: 'flex',
          maxWidth: '1332px', // 658px * 2 + 16px gap
          width: '100%',
          gap: '16px',
        }}
      >
        {/* Left Section - Image Carousel */}
        <Box
          sx={{
            flex: 1,
            maxWidth: '658px',
            position: 'relative',
            height: 'calc(100vh - 120px)', // Subtract navbar height and padding
            minHeight: '500px', // Ensure minimum height
            overflow: 'hidden',
            borderRadius: '8px',
          }}
        >
          {/* Main Image */}
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <Image
              src={currentSlideData.image}
              alt={currentSlideData.title}
              layout='fill'
              objectFit='cover'
              objectPosition='center'
              priority
            />

            {/* Navigation Arrows */}
            <IconButton
              onClick={onPrev}
              sx={{
                position: 'absolute',
                left: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                },
                zIndex: 2,
              }}
            >
              <ChevronLeftIcon />
            </IconButton>

            <IconButton
              onClick={onNext}
              sx={{
                position: 'absolute',
                right: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                },
                zIndex: 2,
              }}
            >
              <ChevronRightIcon />
            </IconButton>

            {/* + Photos Button */}
            <Button
              variant='contained'
              onClick={onOpenPhotos}
              sx={{
                position: 'absolute',
                bottom: 20,
                left: 20,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                borderRadius: 0,
                padding: '8px 16px',
                fontSize: '0.875rem',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                },
                zIndex: 2,
              }}
            >
              + photos
            </Button>

            {/* Pagination Dots */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 40,
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: 1,
                zIndex: 2,
              }}
            >
              {Array.from({ length: totalSlides }, (_, index) => (
                <Box
                  key={index}
                  onClick={() => onSlideClick(index)}
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor:
                      index === currentSlide
                        ? 'white'
                        : 'rgba(255, 255, 255, 0.5)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      backgroundColor:
                        index === currentSlide
                          ? 'white'
                          : 'rgba(255, 255, 255, 0.7)',
                    },
                  }}
                />
              ))}
            </Box>
          </Box>
        </Box>

        {/* Right Section - Content */}
        <Box
          sx={{
            flex: 1,
            maxWidth: '658px',
            padding: '48px 16px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            backgroundColor: '#fff',
          }}
        >
          <Stack spacing={4} sx={{ maxWidth: 500 }}>
            {/* Subtitle */}
            <Typography
              variant='body2'
              sx={{ fontSize: '20px', fontWeight: 500, color: '#0F0E0E' }}
            >
              {currentSlideData.subtitle}
            </Typography>

            {/* Main Title */}
            <Typography
              variant='h2'
              sx={{
                fontSize: '40px',
                fontWeight: 500,
                color: '#011589',
                lineHeight: 1.2,
              }}
            >
              {currentSlideData.title}
            </Typography>

            {/* Divider Line */}
            <Box
              sx={{
                width: '100%',
                height: '1px',
                backgroundColor: '#E2E6E0',
                marginY: 2,
              }}
            />

            {/* Features Section Title */}
            <Typography
              variant='h3'
              sx={{
                fontSize: '1.25rem',
                fontWeight: 600,
                color: '#1A1A1A',
                marginBottom: 2,
              }}
            >
              {currentSlideData.description}
            </Typography>

            {/* Features Paragraph */}
            <Typography
              variant='body1'
              sx={{
                fontSize: '0.875rem',
                color: '#1A1A1A',
                lineHeight: 1.6,
                marginBottom: 3,
              }}
            >
              {currentSlideData.features.join('. ')}
            </Typography>

            {/* Action Buttons */}
            <Stack spacing={2} sx={{ marginTop: 4 }}>
              <Button
                variant='contained'
                onClick={onOpenModal}
                sx={{
                  backgroundColor: '#001689',
                  color: 'white',
                  padding: '12px 24px',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  borderRadius: '4px',
                  '&:hover': {
                    backgroundColor: '#001689',
                  },
                }}
              >
                {'BOOK A ROOM AT MAX'}
              </Button>

              <Button
                variant='outlined'
                sx={{
                  borderColor: '#001689',
                  color: '#001689',
                  padding: '12px 24px',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  borderRadius: '4px',
                  '&:hover': {
                    backgroundColor: '#001689',
                    color: 'white',
                  },
                }}
              >
                {'GET LOUNGE ACCESS PASS'}
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
};

const HandheldView: React.FC<ViewProps> = ({
  currentSlideData,
  currentSlide,
  totalSlides,
  onNext,
  onPrev,
  onSlideClick,
  onOpenModal,
  onOpenPhotos,
}) => {
  const theme = useTheme();
  const isSmallerDevice = useMediaQuery('(max-width:500px)');

  return (
    <Box
      width='100%'
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 'calc(100vh - 80px)', // Account for navbar height
        backgroundColor: '#fff',
        padding: isSmallerDevice ? '20px 16px' : '40px 16px', // Reduce padding on smaller devices
      }}
    >
      {/* Container with max width */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          maxWidth: '658px',
          width: '100%',
          alignItems: 'center',
        }}
      >
        {/* Header Section - Subtitle and Title */}
        <Box
          sx={{
            textAlign: 'center',
            marginBottom: isSmallerDevice ? '24px' : '32px', // Responsive margin
            width: '100%',
          }}
        >
          {/* Subtitle */}
          <Typography
            variant='body2'
            sx={{ 
              fontSize: '16px', 
              fontWeight: 500, 
              color: '#0F0E0E',
              marginBottom: '12px'
            }}
          >
            {currentSlideData.subtitle}
          </Typography>

          {/* Main Title */}
          <Typography
            variant='h3'
            sx={{
              fontSize: isSmallerDevice ? '28px' : '32px', // Responsive font size
              fontWeight: 500,
              color: '#011589',
              lineHeight: 1.2,
            }}
          >
            {currentSlideData.title}
          </Typography>
        </Box>

        {/* Image Section */}
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            height: isSmallerDevice 
              ? 'calc(35vh - 20px)' // Smaller height for very small devices
              : 'calc(40vh - 40px)', // Standard height for larger mobile devices
            minHeight: isSmallerDevice ? '200px' : '250px', // Responsive minimum height
            overflow: 'hidden',
            borderRadius: '8px',
            marginBottom: isSmallerDevice ? '24px' : '32px', // Responsive margin
          }}
        >
          <Image
            src={currentSlideData.image}
            alt={currentSlideData.title}
            layout='fill'
            objectFit='cover'
            objectPosition='center'
            priority
          />

          {/* Navigation Arrows */}
          <IconButton
            onClick={onPrev}
            sx={{
              position: 'absolute',
              left: 10,
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
              },
              zIndex: 2,
            }}
          >
            <ChevronLeftIcon />
          </IconButton>

          <IconButton
            onClick={onNext}
            sx={{
              position: 'absolute',
              right: 10,
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
              },
              zIndex: 2,
            }}
          >
            <ChevronRightIcon />
          </IconButton>

          {/* + Photos Button */}
          <Button
            variant='contained'
            onClick={onOpenPhotos}
            sx={{
              position: 'absolute',
              bottom: 20,
              left: 10,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              borderRadius: 0,
              padding: '6px 12px',
              fontSize: '0.75rem',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
              },
              zIndex: 2,
            }}
          >
            + photos
          </Button>

          {/* Pagination Dots */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 20,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 0.5,
              zIndex: 2,
            }}
          >
            {Array.from({ length: totalSlides }, (_, index) => (
              <Box
                key={index}
                onClick={() => onSlideClick(index)}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor:
                    index === currentSlide
                      ? 'white'
                      : 'rgba(255, 255, 255, 0.5)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor:
                      index === currentSlide
                        ? 'white'
                        : 'rgba(255, 255, 255, 0.7)',
                  },
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Content Section - Description and Features */}
        <Box
          sx={{
            textAlign: 'center',
            width: '100%',
            padding: isSmallerDevice ? '0 16px' : '0 24px',
          }}
        >
          <Stack spacing={3} sx={{ alignItems: 'center' }}>
            {/* Features Section Title */}
            <Typography
              variant='h4'
              sx={{
                fontSize: '16px',
                fontWeight: 600,
                color: '#1A1A1A',
                marginBottom: 1,
              }}
            >
              {currentSlideData.description}
            </Typography>

            {/* Features Paragraph */}
            <Typography
              variant='body2'
              sx={{
                fontSize: '16px',
                color: '#1A1A1A',
                lineHeight: 1.5,
                marginBottom: 2,
              }}
            >
              {currentSlideData.features.join('. ')}
            </Typography>

            {/* Action Buttons */}
            <Stack spacing={isSmallerDevice ? 1 : 1.5} sx={{ marginTop: 2 }}>
              <Button
                variant='contained'
                onClick={onOpenModal}
                sx={{
                  backgroundColor: '#001689',
                  color: 'white',
                  padding: isSmallerDevice ? '12px 16px' : '10px 20px', // Responsive padding
                  fontSize: isSmallerDevice ? '0.8rem' : '0.75rem', // Slightly larger text on small devices
                  fontWeight: 600,
                  borderRadius: '4px',
                  width: isSmallerDevice ? '100%' : 'auto', // Full width on very small devices
                  '&:hover': {
                    backgroundColor: '#001689',
                  },
                }}
              >
                {'BOOK A ROOM AT MAX'}
              </Button>

              <Button
                variant='outlined'
                sx={{
                  borderColor: '#001689',
                  color: '#001689',
                  padding: isSmallerDevice ? '12px 16px' : '10px 20px', // Responsive padding
                  fontSize: isSmallerDevice ? '0.8rem' : '0.75rem', // Slightly larger text on small devices
                  fontWeight: 600,
                  borderRadius: '4px',
                  width: isSmallerDevice ? '100%' : 'auto', // Full width on very small devices
                  '&:hover': {
                    backgroundColor: '#001689',
                    color: 'white',
                  },
                }}
              >
                {'GET LOUNGE ACCESS PASS'}
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
};

export default ProperPlace;
