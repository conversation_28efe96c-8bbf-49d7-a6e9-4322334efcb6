import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode, Mousewheel } from 'swiper/modules';

import Agoda from '../../awards/agoda';
import Traveloka from '../../awards/traveloka';
import LEEDSilver from '../../awards/leed-silver';
import NQACert from '../../awards/nqa-cert';

const AwardsSection = () => {
  const isMobile = useMediaQuery('(max-width:1050px)');

  const awards = [
    {
      component: <Agoda />,
      key: 'agoda',
    },
    {
      component: <Traveloka />,
      key: 'traveloka',
    },
    {
      component: <LEEDSilver />,
      key: 'leed-silver',
    },
    {
      component: <NQACert />,
      key: 'nqa-cert',
    },
  ];

  return (
    <Box
      sx={{
        // padding: isMobile ? '24px 16px' : '48px 32px',
        maxWidth: '100%',
      }}
    >
      <Box
        sx={{
          '& .swiper-wrapper': {
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          },
        }}
      >
        <Swiper
          modules={[FreeMode, Mousewheel]}
          spaceBetween={isMobile ? 16 : 32}
          slidesPerView='auto'
          freeMode={{
            enabled: true,
            momentum: true,
            momentumRatio: 0.8,
            momentumVelocityRatio: 0.8,
          }}
          mousewheel={{
            forceToAxis: true,
            sensitivity: 1,
          }}
          style={{
            padding: '0',
          }}
        >
          {awards.map((award) => (
            <SwiperSlide
              key={award.key}
              style={{
                display: 'flex',
                maxWidth: isMobile ? '168px' : '230px',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  maxWidth: { xs: '168px', sm: '230px' },
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                {award.component}
              </Box>
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    </Box>
  );
};

export default AwardsSection;
