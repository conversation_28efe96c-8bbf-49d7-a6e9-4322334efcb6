import React from 'react';
import { Box, Grid, Typography, useMediaQuery, useTheme } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode, Mousewheel } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/free-mode';
import DividerIcon from '../../icons/DividerIcon';

const StatsSection = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const stats = [
    {
      element: (
        <span style={{ display: 'flex', flexDirection: 'column' }}>
          <span>Over</span>
          <span>600,000</span>
          <span>happy guest</span>
          <span>welcomed</span>
        </span>
      ),
      text: 'Over 600,000 happy guest welcomed',
    },
    {
      element: (
        <span style={{ display: 'flex', flexDirection: 'column' }}>
          <span>Over</span>
          <span>10 years</span>
          <span>in hospitality</span>
        </span>
      ),
      text: 'Over 10 years in hospitality',
    },
    {
      element: (
        <span style={{ display: 'flex', flexDirection: 'column' }}>
          <span>364 beds </span>
          <span>with more</span>
          <span>coming</span>
        </span>
      ),
      text: '364 beds with more coming',
    },
    {
      element: (
        <span style={{ display: 'flex', flexDirection: 'column' }}>
          <span>Open 24/7, 354</span>
          <span>All day,</span>
          <span>every day</span>
        </span>
      ),
      text: 'Open 24/7, 354 All day, every day',
    },
  ];

  return (
    <Box
      sx={{
        width: '100%',
        mb: { xs: 4, md: 6 },
      }}
    >
      {isMobile ? (
        <Box
          sx={{
            '& .swiper-wrapper': {
              '&::-webkit-scrollbar': {
                display: 'none',
              },
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            },
          }}
        >
          <Swiper
            modules={[FreeMode, Mousewheel]}
            spaceBetween={16}
            slidesPerView='auto'
            freeMode={{
              enabled: true,
              momentum: true,
              momentumRatio: 0.8,
              momentumVelocityRatio: 0.8,
            }}
            mousewheel={{
              forceToAxis: true,
              sensitivity: 1,
            }}
            style={{
              padding: '0 16px',
            }}
          >
            {stats.map((stat, index) => (
              <React.Fragment key={index}>
                <SwiperSlide
                  style={{
                    width: '100%',
                    maxWidth: '168px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '200px',
                      px: 2,
                      minWidth: '140px',
                      maxWidth: '180px',
                    }}
                  >
                    <Typography
                      variant='h6'
                      sx={{
                        color: 'primary.main',
                        fontSize: '1.125rem',
                        fontWeight: 600,
                        textAlign: 'center',
                        lineHeight: 'tight',
                      }}
                    >
                      {stat.element}
                    </Typography>
                  </Box>
                </SwiperSlide>
                {index < stats.length - 1 && (
                  <SwiperSlide
                    style={{
                      width: 'auto',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '200px',
                        paddingLeft: '0 !important'
                      }}
                    >
                      <DividerIcon />
                    </Box>
                  </SwiperSlide>
                )}
              </React.Fragment>
            ))}
          </Swiper>
        </Box>
      ) : (
        <Grid
          container
          spacing={3}
          sx={{
            justifyContent: 'center',
            flexWrap: 'nowrap',
          }}
        >
          {stats.map((stat, index) => (
            <React.Fragment key={index}>
              <Grid
                item
                xs={6}
                sm={3}
                sx={{
                  padding: 0,
                  paddingLeft: '0px !important',
                  paddingTop: '0px !important',
                  display: 'grid',
                  justifyContent: 'center',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    minWidth: { xs: '140px' },
                    maxWidth: { xs: '180px' },
                  }}
                >
                  <Typography
                    variant='h6'
                    sx={{
                      color: 'primary.main',
                      fontSize: { xs: '1.125rem', sm: '1.25rem' },
                      fontWeight: 600,
                      textAlign: 'center',
                      lineHeight: 'tight',
                    }}
                  >
                    {stat.element}
                  </Typography>
                </Box>
              </Grid>
              {index < stats.length - 1 && (
                <Grid
                  item
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: '0 !important',
                  }}
                >
                  <DividerIcon />
                </Grid>
              )}
            </React.Fragment>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default StatsSection;
