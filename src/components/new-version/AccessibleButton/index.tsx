import InfoIcon from '@/components/icons/InfoIcon';
import { Box, Button } from '@mui/material';
import React, { useState } from 'react';
import PublicAreaModal from '../PublicAreaModal';
import RestrictedAreaModal from '../RestrictedAreaModal';

const AccessibleButton = ({ isPublicArea }: { isPublicArea?: boolean }) => {
  const [isPublicAreaModalOpen, setIsPublicAreaModalOpen] = useState(false);
  const [isRestrictedAreaModalOpen, setIsRestrictedAreaModalOpen] =
    useState(false);

  const handleOpenPublicAreaModal = () => {
    setIsPublicAreaModalOpen(true);
  };

  const handleClosePublicAreaModal = () => {
    setIsPublicAreaModalOpen(false);
  };

  const handleOpenRestrictedAreaModal = () => {
    setIsRestrictedAreaModalOpen(true);
  };

  const handleCloseRestrictedAreaModal = () => {
    setIsRestrictedAreaModalOpen(false);
  };

  return (
    <>
      <Button
        variant='contained'
        size='small'
        onClick={
          isPublicArea
            ? handleOpenPublicAreaModal
            : handleOpenRestrictedAreaModal
        }
        sx={{
          bgcolor: isPublicArea ? '#D9F2EF' : '#FEDCF1',
          color: '#0F0E0E',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: '4px',
          fontSize: '14px',
          px: 2,
          py: 0.5,
          '&:hover': {
            bgcolor: isPublicArea ? '#D9F2EF' : '#FEDCF1',
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isPublicArea && 'Public Area'}
          {!isPublicArea && 'Restricted Area'}
          <InfoIcon />
        </Box>
      </Button>
      {/* Public Area Modal */}
      <PublicAreaModal
        open={isPublicAreaModalOpen}
        onClose={handleClosePublicAreaModal}
        onOpenRestrictedAreaModal={() => {
          handleClosePublicAreaModal();
          handleOpenRestrictedAreaModal();
        }}
      />
      {/* Restricted Area Modal */}
      <RestrictedAreaModal
        open={isRestrictedAreaModalOpen}
        onClose={handleCloseRestrictedAreaModal}
        onOpenPublicAreaModal={() => {
          handleCloseRestrictedAreaModal();
          handleOpenPublicAreaModal();
        }}
      />
    </>
  );
};

export default AccessibleButton;
