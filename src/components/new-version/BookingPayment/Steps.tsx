'use client';

import React from 'react';
import {
  Box,
  styled,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';

import RoomSelectionIcon from '@/components/icons/RoomSelectionIcon';
import ParagraphIcon from '@/components/icons/ParagraphIcon';
import UserIcon from '@/components/icons/UserIcon';

// Styled components
const StepsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  marginTop: '24px',
  flexDirection: 'column',
  width: '100%',
  [theme.breakpoints.down('md')]: {
    paddingBottom: '40px', // Extra space for the ::after pseudo-element text (increased for multi-line)
  },
  [theme.breakpoints.up('md')]: {
    flexDirection: 'row',
  },
}));

const StepsRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  width: '100%',
  padding: '0 20px',
  [theme.breakpoints.down('sm')]: {
    padding: '0 16px',
  },
}));

const StepItem = styled(Box)<{ active?: boolean; completed?: boolean }>(
  ({ theme, active, completed }) => ({
    display: 'flex',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    color: completed || active ? '#011589' : '#1A1A1A',
    fontSize: '16px',
    gap: '6px',
    fontWeight: 500,
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(0.5),
      flexDirection: 'column',
      alignItems: 'center',
      gap: '4px',
    },
  })
);

const StepIcon = styled(Box)<{ 
  active?: boolean; 
  completed?: boolean;
  stepTitle?: string;
}>(
  ({ theme, active, completed, stepTitle }) => ({
    color: completed || active ? '#011589' : '#1A1A1A',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    flexDirection: 'column',
    
    [theme.breakpoints.down('md')]: {
      '&::after': stepTitle && active ? {
        content: `"${stepTitle}"`,
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        marginTop: '8px',
        fontSize: '16px',
        fontWeight: 500,
        color: completed || active ? '#011589' : '#1A1A1A',
        textAlign: 'center',
        maxWidth: '100px',
        wordWrap: 'break-word',
        lineHeight: '1.2',
      } : {},
    },
  })
);



const Divider = styled(Box)(({ theme }) => ({
  flex: 1,
  height: '2px',
  backgroundColor: '#011589',
  margin: '0 12px',
  [theme.breakpoints.down('md')]: {
    height: '1px',
    margin: '0 8px',
  },
}));

type Step = 'roomSelection' | 'detailsAndPayment' | 'completeBooking' | string;

const Steps = ({
  completedSteps,
  currentStep,
}: {
  completedSteps: Step[];
  currentStep: Step;
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const steps = [
    {
      icon: <RoomSelectionIcon />,
      title: 'Room Selection',
      completed: completedSteps.includes('roomSelection'),
      active: currentStep === 'roomSelection',
    },
    {
      icon: <UserIcon />,
      title: 'Details & Payment',
      completed: completedSteps.includes('detailsAndPayment'),
      active: currentStep === 'detailsAndPayment',
    },
    {
      icon: <ParagraphIcon />,
      title: 'Complete Booking',
      completed: completedSteps.includes('completeBooking'),
      active: currentStep === 'completeBooking',
    },
  ];

  if (isMobile) {
    return (
      <StepsContainer>
        {/* Icons row with titles as ::after pseudo-elements */}
        <StepsRow>
          {steps.map((step, index) => (
            <React.Fragment key={index}>
              <StepIcon 
                active={step.active} 
                completed={step.completed}
                stepTitle={step.active ? step.title : undefined}
              >
                {step.icon}
              </StepIcon>
              {index < steps.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </StepsRow>
      </StepsContainer>
    );
  }

  // Desktop version (original layout)
  return (
    <StepsContainer>
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          <StepItem active={step.active} completed={step.completed}>
            {step.icon}
            {step.title}
          </StepItem>
          {index < steps.length - 1 && (
            <Box
              sx={{
                width: '30px',
                height: '2px',
                backgroundColor: '#011589',
                mx: 1,
              }}
            />
          )}
        </React.Fragment>
      ))}
    </StepsContainer>
  );
};

export default Steps;
