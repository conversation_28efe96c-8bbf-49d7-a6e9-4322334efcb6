'use client';

import React, { useEffect } from 'react';
import { Box, Divider } from '@mui/material';
import Steps from './Steps';
import DetailsAndPayment from './DetailsAndPayment';
import { BookingPaymentProvider, useBookingPaymentContext } from './context';
import { FormPaymentData } from '@/app/new-booking/types/form-payment-data';
import { ICountry } from '@/models/Booking';

interface BookingPaymentProps {
  // Add your props here based on your needs
  selectedRooms?: any[];
  onSubmit?: (
    data: FormPaymentData
  ) => Promise<{ success: boolean; error?: any }>;
  hotelDetail: any;
  taxAndServiceCharge: any;
  checkInInformation: any;
  guestsCountry: ICountry[];
}

// Inner component that uses the context
const BookingPaymentContent: React.FC = () => {
  const { currentStep, completedSteps } = useBookingPaymentContext();

  return (
    <Box
      sx={{
        backgroundColor: '#fff',
        minHeight: '100vh',
        pb: 4,
        maxWidth: '1080px',
        width: '100%',
        mx: 'auto',
      }}
    >
      {/* Steps Header */}
      <Steps completedSteps={completedSteps} currentStep={currentStep} />

      <Divider sx={{ my: 4, borderColor: '#D8DDE2' }} />

      {/* Main Content */}
      {currentStep === 'detailsAndPayment' && <DetailsAndPayment />}
    </Box>
  );
};

// Main component that provides the context
const BookingPayment: React.FC<BookingPaymentProps> = ({
  selectedRooms = [],
  onSubmit,
  hotelDetail,
  taxAndServiceCharge,
  checkInInformation,
  guestsCountry,
}) => {
  // Scroll to top smoothly when component mounts
  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }, []);

  return (
    <BookingPaymentProvider
      selectedRooms={selectedRooms}
      onSubmit={onSubmit}
      hotelDetail={hotelDetail}
      taxAndServiceCharge={taxAndServiceCharge}
      checkInInformation={checkInInformation}
      guestsCountry={guestsCountry}
    >
      <BookingPaymentContent />
    </BookingPaymentProvider>
  );
};

export default BookingPayment;
