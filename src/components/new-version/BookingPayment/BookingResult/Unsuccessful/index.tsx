'use client';

import React from 'react';
import { <PERSON>, Typo<PERSON>, But<PERSON>, Divider } from '@mui/material';
import { useRouter } from 'next/navigation';

// Create a booking failure icon component
const BookingFailureIcon = () => {
  return (
    <svg
      width='150'
      height='90'
      viewBox='0 0 150 90'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clip-path='url(#clip0_7071_84871)'>
        <path
          d='M79.1426 48.3906H94.1876'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M79.1426 21.1348H110.928'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M79.1426 34.7705H110.928'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M79.1426 62.0098H90.1376'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M92.3584 76.1101H65.5234V7.99512H124.548V40.5901'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M116.221 82.5156C126.576 82.5156 134.971 74.121 134.971 63.7656C134.971 53.4103 126.576 45.0156 116.221 45.0156C105.865 45.0156 97.4707 53.4103 97.4707 63.7656C97.4707 74.121 105.865 82.5156 116.221 82.5156Z'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M124.516 55.4707L107.926 72.0607'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M124.516 72.0607L107.926 55.4707'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M54.6003 42.2705H9.19531V75.1205H54.6003V42.2705Z'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M18.1523 42.2705V75.1205'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M45.4805 42.2705V75.1205'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
        <path
          d='M40.9358 29.8799H22.8008V42.2699H40.9358V29.8799Z'
          stroke='#FA25A8'
          strokeWidth='2'
          strokeMiterlimit='10'
        />
      </g>
      <defs>
        <clipPath id='clip0_7071_84871'>
          <rect width='150' height='90' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

interface UnsuccessfulBookingProps {
  onRetry?: () => void;
}

const UnsuccessfulBooking: React.FC<UnsuccessfulBookingProps> = ({
  onRetry,
}) => {
  const router = useRouter();

  const reasons = [
    'the internet connection during the payment process,',
    'insufficient amount in the selected payment method.',
  ];

  const handleMakeBookingAgain = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Navigate to the new booking page
      router.push('/new-booking');
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '80vh',
        padding: { xs: 2, md: 4 },
        backgroundColor: '#ffffff',
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      }}
    >
      {/* Title */}
      <Typography
        variant='h1'
        sx={{
          fontSize: { xs: '1.75rem', md: '2.5rem' },
          fontWeight: 600,
          color: '#1A1A1A',
          textAlign: 'center',
          marginBottom: { xs: 3, md: 4 },
          lineHeight: 1.2,
        }}
      >
        Your booking is unsuccessful.
      </Typography>

      {/* Icon */}
      <Box sx={{ marginBottom: { xs: 3, md: 4 } }}>
        <BookingFailureIcon />
      </Box>

      {/* Description Box */}
      <Box
        sx={{
          width: '100%',
          maxWidth: '500px',
          backgroundColor: '#F2F3FD',
          borderRadius: '4px',
          padding: { xs: 3, md: 4 },
          marginBottom: { xs: 3, md: 4 },
          border: '1px solid #F2F3FD',
        }}
      >
        {/* Box Title */}
        <Typography
          variant='h6'
          sx={{
            fontSize: { xs: '1.1rem', md: '1.25rem' },
            fontWeight: 600,
            color: '#1A1A1A',
            marginBottom: 2,
          }}
        >
          There is error while booking the hotel. It can be:
        </Typography>

        {/* Reasons List */}
        <Box
          component='ul'
          sx={{
            margin: 0,
            paddingLeft: { xs: 2, md: 3 },
            listStyleType: 'disc',
            '& li': {
              fontSize: { xs: '0.95rem', md: '1rem' },
              color: '#1A1A1A',
              lineHeight: 1.6,
              marginBottom: 1,
            },
          }}
        >
          {reasons.map((reason, index) => (
            <li key={index}>{reason}</li>
          ))}
        </Box>

        {/* Divider */}
        <Divider
          sx={{
            marginY: 3,
            borderColor: '#DEE2E6',
            borderWidth: '1px',
          }}
        />

        {/* Bottom Description */}
        <Typography
          sx={{
            fontSize: { xs: '0.95rem', md: '1rem' },
            color: '#1A1A1A',
            textAlign: 'center',
            lineHeight: 1.6,
          }}
        >
          Please make sure you have resolved issues above before making another
          booking.
        </Typography>
      </Box>

      {/* Action Button */}
      <Button
        variant='outlined'
        onClick={handleMakeBookingAgain}
        sx={{
          minWidth: { xs: '260px', md: '280px' },
          height: { xs: '42px' },
          fontSize: { xs: '0.95rem', md: '1rem' },
          fontWeight: 600,
          textTransform: 'uppercase',
          borderColor: '#011589',
          color: '#011589',
          backgroundColor: 'white',
          borderRadius: '4px',
          letterSpacing: '0.5px',
          '&:hover': {
            backgroundColor: '#011589',
            color: 'white',
            borderColor: '#011589',
          },
          '&:focus': {
            outline: 'none',
            boxShadow: '0 0 0 3px rgba(1, 21, 137, 0.1)',
          },
        }}
      >
        Make a Booking Again
      </Button>
    </Box>
  );
};

export default UnsuccessfulBooking;
