'use client';

import React from 'react';
import { Box, Typography, Button, Divider, styled } from '@mui/material';
import axios from 'axios';
import { API_BASE_URL } from '@/config/app';
import { OUTLETS } from '@/constant/outlet';
import {
  calculateCheckoutTime,
  formatCheckoutDisplay,
  formatDateWithWeekday,
  toAmPmTime,
} from '@/utils/time-selection';
import AccessibleButton from '@/components/new-version/AccessibleButton';
import Steps from '@/components/new-version/BookingPayment/Steps';

interface SuccessfulBookingProps {
  bookingData: {
    guestDetail: { [k: string]: any };
    payment: { [k: string]: any };
    roomBookings: { [k: string]: any }[];
    selectedHotel: { [k: string]: any };
    bookingSchedule: { [k: string]: any };
    bookingNo: string;
    bookingId: string;
  };
}

const StyledLabel = styled(Typography)({
  fontSize: '12px',
  fontWeight: 600,
  color: '#989CA4',
  letterSpacing: '0.1em',
  marginBottom: '4px',
});

const StyledValue = styled(Typography)({
  fontSize: '16px',
  fontWeight: 600,
  color: '#3B3F47',
});

const SuccessfulBooking: React.FC<SuccessfulBookingProps> = ({
  bookingData,
}) => {
  const { roomBookings, selectedHotel, bookingSchedule, guestDetail, payment } =
    bookingData;

  const checkInTime = {
    dateInString: bookingSchedule?.date
      ? formatDateWithWeekday(new Date(bookingSchedule?.date))
      : '',
    timeInString: bookingSchedule?.checkInTime
      ? toAmPmTime(bookingSchedule?.checkInTime)
      : '',
  };

  const checkoutTime = (() => {
    const value = calculateCheckoutTime({
      date: bookingSchedule?.date as any,
      checkinTime: bookingSchedule?.checkInTime as string,
      duration: Number(bookingSchedule?.stayDuration),
    });

    if (value) {
      const [dateInString, timeInString] =
        formatCheckoutDisplay(value).split(',');

      return {
        dateInString,
        timeInString,
      };
    }

    return {
      dateInString: '-',
      timeInString: '-',
    };
  })();

  const outletInfo = OUTLETS.find(
    (outlet) => outlet.lotId === Number(selectedHotel?.lotId)
  );

  const handleDownloadFile = (pdfContent: string) => {
    // Convert the decoded content to a Uint8Array
    const pdfData = new Uint8Array(pdfContent.length);
    for (let i = 0; i < pdfContent.length; i++) {
      pdfData[i] = pdfContent.charCodeAt(i);
    }

    const pdfBlob = new Blob([pdfData], { type: 'application/pdf' });

    // Create a download link
    const url = URL.createObjectURL(pdfBlob);

    // Create an anchor element to trigger the download
    const link = document.createElement('a');
    link.href = url;
    link.download = 'Receipt.pdf';

    // Click the link to start the download
    link.click();

    // Clean up by revoking the object URL
    URL.revokeObjectURL(url);
  };

  const handleDownloadReceipt = () => {
    const apiUrl = `${API_BASE_URL}/landing-page/generate-receipt/`;

    const formData = {
      bookingId: bookingData.bookingId,
    };

    axios
      .post(apiUrl, formData)
      .then((res) => {
        const pdfContent = atob(res.data.pdf_base64);
        handleDownloadFile(pdfContent);
      })
      .catch((res) => {
        // Notification.failed(res.response.data.message);
      });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        // padding: { xs: 2, md: 4 },
        backgroundColor: '#ffffff',
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        maxWidth: '1120px',
        margin: '0 auto',
        padding: '20px',
      }}
    >
      <Steps
        currentStep='completeBooking'
        completedSteps={[
          'roomSelection',
          'detailsAndPayment',
          'completeBooking',
        ]}
      />
      {/* Title */}
      <Typography
        variant='h1'
        sx={{
          fontSize: { xs: '28px' },
          fontWeight: 600,
          color: '#3B3F47',
          textAlign: 'center',
          marginBottom: { xs: 6, md: 8 },
          lineHeight: 1.2,
          marginTop: '40px',
        }}
      >
        Your booking is successful!
      </Typography>

      {/* Main Details Box */}
      <Box
        sx={{
          width: '100%',
          border: '1px solid #E5E7EB',
          borderRadius: '8px',
          padding: { xs: 3, md: 4 },
          marginBottom: { xs: 3, md: 4 },
          backgroundColor: '#ffffff',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 0, md: 0 },
            alignItems: 'stretch',
            minHeight: { xs: 'auto', md: '400px' },
          }}
        >
          {/* Left Section */}
          <Box
            sx={{
              flex: 1,
            }}
          >
            {/* Booking Number */}
            <Box
              sx={{ marginBottom: 3, backgroundColor: '#F2F3FD', padding: 2 }}
            >
              <Typography
                variant='overline'
                sx={{
                  fontSize: '12px',
                  fontWeight: 600,
                  color: '#011589',
                  letterSpacing: '0.1em',
                  marginBottom: '4px',
                  display: 'block',
                }}
              >
                BOOKING NUMBER
              </Typography>
              <Typography
                sx={{
                  fontSize: { xs: '20px' },
                  fontWeight: 600,
                  color: '#0F0E0E',
                }}
              >
                {bookingData?.bookingNo}
              </Typography>
            </Box>

            {/* Outlet Name */}
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '20px' },
                fontWeight: 600,
                color: '#0F0E0E',
                marginBottom: 2,
                lineHeight: 1.3,
              }}
            >
              {outletInfo && (
                <>
                  {outletInfo?.title} @ {outletInfo?.terminal}
                </>
              )}
            </Typography>

            {/* Terminal and Area Badges */}
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                marginBottom: 3,
              }}
            >
              <Button
                variant='contained'
                sx={{
                  backgroundColor: '#D9F2EF',
                  color: '#0F0E0E',
                  px: 2,
                  py: 0.5,
                  borderRadius: '4px',
                  fontSize: '14px',
                  fontWeight: 600,
                  '&:hover': {
                    backgroundColor: '#D9F2EF',
                  },
                }}
              >
                {outletInfo?.location}
              </Button>
              <AccessibleButton isPublicArea={outletInfo?.isPublicArea} />
            </Box>

            {/* Location Description */}
            <Typography
              sx={{
                fontSize: '0.875rem',
                color: '#6B7280',
                lineHeight: 1.6,
              }}
            >
              {selectedHotel?.hotelDetailedLocation}
            </Typography>
          </Box>

          <Divider
            orientation='vertical'
            flexItem
            sx={{
              backgroundColor: '#D8DDE2',
              marginX: { xs: 0, md: 3 },
              marginY: { xs: 3, md: 0 },
              height: { md: 'auto', xs: '1px' },
              width: { md: '1px', xs: 'auto' },
            }}
          />

          {/* Right Section */}
          <Box
            sx={{
              flex: 1,
            }}
          >
            {/* You've Booked Section */}
            <StyledLabel>YOU&apos;VE BOOKED</StyledLabel>

            {roomBookings.map((room, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  gap: 2,
                  marginBottom: 3,
                }}
              >
                {/* Room Image */}
                <Box
                  sx={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    backgroundColor: '#F3F4F6',
                    flexShrink: 0,
                  }}
                >
                  <img
                    src={room.room?.images?.[0]?.url}
                    alt='Room'
                    width={80}
                    height={80}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                    onError={(e) => {
                      // Fallback to a placeholder if image fails to load
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </Box>

                {/* Room Info */}
                <Box sx={{ flex: 1 }}>
                  <StyledValue>
                    {room.quantity}x {room.roomName}{' '}
                    {room.zone ? `@ ${room.zone}` : ''}
                  </StyledValue>
                  <Typography
                    sx={{
                      fontSize: '14px',
                      color: '#6B7280',
                    }}
                  >
                    {room.room?.bedType} • {room.room?.capacity}
                  </Typography>
                </Box>
              </Box>
            ))}

            {/* Room Details with Image */}

            {/* Divider */}
            <Divider
              sx={{
                marginY: 3,
                borderColor: '#D8DDE2',
                height: '1px',
                width: '100%',
              }}
            />

            {/* Check-in Information */}
            <Box sx={{ marginBottom: 2 }}>
              <StyledLabel>CHECK-IN</StyledLabel>
              <StyledValue>
                {checkInTime.dateInString}, {checkInTime.timeInString}
              </StyledValue>
            </Box>

            {/* Check-out Information */}
            <Box sx={{ marginBottom: 2 }}>
              <StyledLabel>CHECK-OUT</StyledLabel>
              <StyledValue>
                {checkoutTime.dateInString}, {checkoutTime.timeInString}
              </StyledValue>
            </Box>

            {/* Stay Duration */}
            <Box sx={{ marginBottom: 2 }}>
              <StyledLabel>STAY DURATION</StyledLabel>
              <StyledValue>{bookingSchedule?.stayDuration} Hours</StyledValue>
            </Box>

            {/* Amount Paid */}
            <Box>
              <StyledLabel>AMOUNT PAID</StyledLabel>
              <StyledValue>RM{payment.creditAmount}</StyledValue>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Download Receipt Button */}
      <Box
        sx={{
          width: '100%',
          maxWidth: '1000px',
          display: 'flex',
          justifyContent: 'flex-end',
        }}
      >
        <Button
          variant='contained'
          onClick={handleDownloadReceipt}
          sx={{
            minWidth: { xs: '200px', md: '220px' },
            height: { xs: '48px', md: '52px' },
            fontSize: { xs: '0.95rem', md: '1rem' },
            fontWeight: 600,
            textTransform: 'uppercase',
            backgroundColor: '#011589',
            color: 'white',
            borderRadius: '4px',
            letterSpacing: '0.5px',
            '&:hover': {
              backgroundColor: '#010f6b',
            },
            '&:focus': {
              outline: 'none',
              boxShadow: '0 0 0 3px rgba(1, 21, 137, 0.1)',
            },
          }}
        >
          Download Receipt
        </Button>
      </Box>
    </Box>
  );
};

export default SuccessfulBooking;
