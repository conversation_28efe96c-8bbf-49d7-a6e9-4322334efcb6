'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { IGuestDetail, GuestDetailInitial, ICountry } from '@/models/Booking';
import { FormPaymentData } from '@/app/new-booking/types/form-payment-data';
import { OUTLETS } from '@/constant/outlet';
import {
  calculateCheckoutTime,
  formatCheckoutDisplay,
  formatDateWithWeekday,
  toAmPmTime,
} from '@/utils/time-selection';
import { Outlet } from '@/actions/getOutlets/types';
import { useRouter } from 'next/navigation';

// Validation schema
const guestDetailSchema = yup.object().shape({
  guestDetail: yup.object().shape({
    firstName: yup
      .string()
      .required('First name is required')
      .min(2, 'First name must be at least 2 characters'),
    lastName: yup
      .string()
      .required('Last name is required')
      .min(2, 'Last name must be at least 2 characters'),
    gender: yup
      .string()
      .required('Gender is required')
      .oneOf(['Male', 'Female'], 'Please select a valid gender'),
    nationality: yup.string().required('Nationality is required'),
    identification: yup
      .string()
      .required('Identification type is required')
      .oneOf(
        ['MyKad', 'Passport'],
        'Please select a valid identification type'
      ),
    idNumber: yup
      .string()
      .required('ID number is required')
      .min(6, 'ID number must be at least 6 characters'),
    email: yup
      .string()
      .required('Email is required')
      .email('Please enter a valid email address'),
    phone: yup
      .string()
      .required('Phone number is required')
      .matches(/^\+\d{1,4}\s\d{8,15}$/, 'Please enter a valid phone number'),
  }),
  selectedPaymentMethod: yup.string().optional(),
  acceptedTerms: yup
    .boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
});

// Form data interface
interface IFormData {
  guestDetail: IGuestDetail;
  selectedPaymentMethod: string;
  acceptedTerms: boolean;
  sum: number;
  promotion: string;
  promotionAmount: number;
  creditAmount: number;
  roundings: number;
}

// Types for the context
export interface BookingPaymentContextType {
  // Props passed from parent
  selectedRooms: any[];
  hotelDetail: any;
  taxAndServiceCharge: any;
  onSubmit?: (data: any) => void;
  checkInInformation: { date: Date; [k: string]: any };
  guestsCountry: ICountry[];
  // State for current step
  currentStep: string;
  setCurrentStep: (step: string) => void;
  completedSteps: string[];
  // React Hook Form
  form: UseFormReturn<IFormData, any, IFormData>;

  // Form validation
  isFormValid: boolean;

  // Handlers
  handleFormSubmit: () => void;
  handleStepChange: (step: string) => void;

  // Convenience getters (derived from form state)
  guestDetail: IGuestDetail;
  selectedPaymentMethod: string;
  acceptedTerms: boolean;

  // Convenience setters (shortcuts to form.setValue)
  setGuestDetail: (guestDetail: Partial<IGuestDetail>) => void;
  setSelectedPaymentMethod: (method: string) => void;
  setAcceptedTerms: (accepted: boolean) => void;

  // Outlet info
  outletInfo?: Partial<Outlet>;
  checkInTime: {
    dateInString: string;
    timeInString: string;
  };
  checkoutTime: {
    dateInString: string;
    timeInString: string;
  };
  formSubmittedResult: any;
  isSubmitting: boolean;
}

// Create the context
const BookingPaymentContext = createContext<
  BookingPaymentContextType | undefined
>(undefined);

// Provider props interface
interface BookingPaymentProviderProps {
  children: ReactNode;
  selectedRooms?: any[];
  hotelDetail: any;
  taxAndServiceCharge: any;
  onSubmit?: (
    data: FormPaymentData
  ) => Promise<{ success: boolean; data?: any; error?: any }>;
  checkInInformation: any;
  guestsCountry: ICountry[];
}

// Provider component
export const BookingPaymentProvider: React.FC<BookingPaymentProviderProps> = ({
  children,
  selectedRooms = [],
  hotelDetail,
  taxAndServiceCharge,
  onSubmit,
  checkInInformation,
  guestsCountry,
}) => {
  const router = useRouter();

  // Minimal state management - only for non-form data
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formSubmittedResult, setFormSubmittedResult] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<string>('detailsAndPayment');
  const [completedSteps, setCompletedSteps] = useState<string[]>([
    'roomSelection',
    'detailsAndPayment',
  ]);

  const outletInfo = OUTLETS.find(
    (outlet) => outlet.lotId === hotelDetail?.lotId
  );

  const checkInTime = {
    dateInString: checkInInformation?.date
      ? formatDateWithWeekday(checkInInformation?.date)
      : '',
    timeInString: checkInInformation?.checkInTime
      ? toAmPmTime(checkInInformation?.checkInTime)
      : '',
  };

  const checkoutTime = (() => {
    const value = calculateCheckoutTime({
      date: checkInInformation?.date as any,
      checkinTime: checkInInformation?.checkInTime as string,
      duration: Number(checkInInformation?.stayDuration),
    });

    if (value) {
      const [dateInString, timeInString] =
        formatCheckoutDisplay(value).split(',');

      return {
        dateInString,
        timeInString,
      };
    }

    return {
      dateInString: '-',
      timeInString: '-',
    };
  })();

  // Initialize React Hook Form
  const form = useForm<IFormData>({
    resolver: yupResolver(guestDetailSchema) as any,
    defaultValues: {
      guestDetail: GuestDetailInitial,
      selectedPaymentMethod: '',
      acceptedTerms: false,
      sum: 0,
      promotion: '',
      promotionAmount: 0,
      creditAmount: 0,
      roundings: 0,
    },
    mode: 'onChange', // Validate on change for better UX
  });

  // Watch form values for derived state
  const formValues = form.watch();

  // Computed values from form state
  const isFormValid = React.useMemo(() => {
    return form.formState.isValid && formValues.acceptedTerms;
  }, [form.formState.isValid, formValues.acceptedTerms]);

  // Convenience getters (derived from form state)
  const guestDetail = formValues.guestDetail || GuestDetailInitial;
  const selectedPaymentMethod = formValues.selectedPaymentMethod || '';
  const acceptedTerms = formValues.acceptedTerms || false;

  // Convenience setters (shortcuts to form.setValue)
  const setGuestDetail = React.useCallback(
    (guestDetailUpdate: Partial<IGuestDetail>) => {
      const currentGuestDetail = form.getValues('guestDetail');
      form.setValue(
        'guestDetail',
        { ...currentGuestDetail, ...guestDetailUpdate },
        { shouldValidate: true }
      );
    },
    [form]
  );

  const setSelectedPaymentMethod = React.useCallback(
    (method: string) => {
      form.setValue('selectedPaymentMethod', method, { shouldValidate: true });
    },
    [form]
  );

  const setAcceptedTerms = React.useCallback(
    (accepted: boolean) => {
      form.setValue('acceptedTerms', accepted, { shouldValidate: true });
    },
    [form]
  );

  // Handlers
  const handleFormSubmit = form.handleSubmit(async (data) => {
    setIsSubmitting(true);
    try {
      if (onSubmit) {
        const formPaymentData: FormPaymentData = {
          promotion: data.promotion,
          promotionAmount: data.promotionAmount,
          sum: data.sum,
          creditAmount: data.creditAmount,
          roundings: 0,
          countryCode: data.guestDetail.nationality,
          firstName: data.guestDetail.firstName,
          lastName: data.guestDetail.lastName,
          idType: data.guestDetail.identification,
          idNo: data.guestDetail.idNumber,
          email: data.guestDetail.email,
          phoneNumber: String(data.guestDetail.phone).replace(/\s/g, ''),
          gender: data.guestDetail.gender,
        };

        const result = await onSubmit(formPaymentData);

        console.log('result', result);

        setFormSubmittedResult(result);

        if (result.success) {
          // setCompletedSteps([...completedSteps, 'completeBooking']);
          // setCurrentStep('completeBooking');

          router.push(result.data.checkoutUrl);
        } else {
          // setCurrentStep('error');
        }
      }
    } catch (error) {
    } finally {
      setIsSubmitting(false);
    }
  });

  const handleStepChange = (step: string) => {
    setCurrentStep(step);
  };

  // Context value
  const contextValue: BookingPaymentContextType = {
    // Props
    selectedRooms,
    hotelDetail,
    taxAndServiceCharge,
    onSubmit,
    checkInInformation,
    guestsCountry,
    // State
    currentStep,
    setCurrentStep,
    completedSteps,

    // React Hook Form
    form,

    // Computed
    isFormValid,

    // Handlers
    handleFormSubmit,
    handleStepChange,

    // Convenience getters (derived from form state)
    guestDetail,
    selectedPaymentMethod,
    acceptedTerms,

    // Convenience setters (shortcuts to form.setValue)
    setGuestDetail,
    setSelectedPaymentMethod,
    setAcceptedTerms,
    outletInfo,
    checkInTime,
    checkoutTime,
    formSubmittedResult,
    isSubmitting,
  };

  return (
    <BookingPaymentContext.Provider value={contextValue}>
      {children}
    </BookingPaymentContext.Provider>
  );
};

// Custom hook to use the context
export const useBookingPaymentContext = (): BookingPaymentContextType => {
  const context = useContext(BookingPaymentContext);
  if (context === undefined) {
    throw new Error(
      'useBookingPayment must be used within a BookingPaymentProvider'
    );
  }
  return context;
};

// Utility hook for form field helpers
export const useFormField = (name: keyof IFormData | string) => {
  const { form } = useBookingPaymentContext();
  const fieldState = form.formState.errors;
  const fieldValue = form.watch(name as keyof IFormData);

  return {
    ...form.register(name as keyof IFormData),
    error: name.includes('.')
      ? name.split('.').reduce((obj, key) => obj?.[key], fieldState as any)
      : fieldState[name as keyof IFormData],
    value: fieldValue,
    setValue: (value: any) => form.setValue(name as keyof IFormData, value),
    clearError: () => form.clearErrors(name as keyof IFormData),
  };
};

// Export the context for advanced use cases
export { BookingPaymentContext };
