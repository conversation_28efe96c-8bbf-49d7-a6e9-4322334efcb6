import { Box, styled } from '@mui/material';

export const PaymentMethod = styled(Box)<{ selected?: boolean }>(
  ({ theme, selected }) => ({
    display: 'flex',
    alignItems: 'center',
    padding: '8px',
    color: '#3B3F47',
    border: `1px solid ${selected ? '#011589' : '#D8DDE2'} `,
    borderRadius: '4px',
    cursor: 'pointer',
    '&:hover': {
      borderColor: '#011589 !important',
    },
    position: 'relative',
  })
);
