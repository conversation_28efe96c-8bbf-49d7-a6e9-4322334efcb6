'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';

import Image from 'next/image';

// Import existing assets and components
import { FormSection } from './styled/FormSection';
// import { PaymentMethod } from './styled/PaymentMethod';
import { useBookingPaymentContext } from '../context';
// import fpxImage from '@/assets/images/fpx.png';
// import visaMastercardImage from '@/assets/images/visa-mastercard.png';
// import ewalletImage from '@/assets/images/ewallet.png';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import IPay88Image from '@/assets/images/PaymentTypes.jpeg';

const PaymentDetails = () => {
  const { selectedPaymentMethod, setSelectedPaymentMethod } =
    useBookingPaymentContext();

  const renderCheckIcon = (method: string) => {
    if (selectedPaymentMethod === method) {
      return (
        <CheckCircleIcon
          sx={{
            color: '#13C1A8',
            position: 'absolute',
            right: '4px',
            top: '50%',
            transform: 'translateY(-50%)',
          }}
        />
      );
    }
    return null;
  };

  return (
    <FormSection>
      <Typography variant='h5' fontWeight={600} mb={3}>
        Payment Details
      </Typography>

      {/* <Typography fontWeight={600} mb={2}>
        SELECT YOUR PAYMENT METHOD
      </Typography>

      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
          gap: '16px',
        }}
      >
        <PaymentMethod
          selected={selectedPaymentMethod === 'fpx'}
          onClick={() => setSelectedPaymentMethod('fpx')}
        >
          <Image src={fpxImage} alt='fpx' width={100} height={50} />
          <Typography fontWeight={600}>Online Banking</Typography>
          {renderCheckIcon('fpx')}
        </PaymentMethod>

        <PaymentMethod
          selected={selectedPaymentMethod === 'card'}
          onClick={() => setSelectedPaymentMethod('card')}
        >
          <Image src={visaMastercardImage} alt='fpx' width={100} height={50} />
          <Typography fontWeight={600}>Visa/Mastercard</Typography>
          {renderCheckIcon('card')}
        </PaymentMethod>

        <PaymentMethod
          selected={selectedPaymentMethod === 'ewallet'}
          onClick={() => setSelectedPaymentMethod('ewallet')}
        >
          <Image src={ewalletImage} alt='fpx' width={100} height={50} />
          <Typography fontWeight={600}>e-Wallets</Typography>
          {renderCheckIcon('ewallet')}
        </PaymentMethod>
      </Box> */}
      <Box>
        <Typography fontWeight={700}>Secure Payment with ipay88</Typography>
        <Box display={'flex'} justifyContent={'center'}>
          <Image
            src={IPay88Image}
            alt='ipay88'
            style={{ width: '300px', height: '200px' }}
          />
        </Box>
      </Box>
    </FormSection>
  );
};

export default PaymentDetails;
