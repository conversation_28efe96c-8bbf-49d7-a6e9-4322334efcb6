'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Checkbox,
  FormControlLabel,
  Divider,
} from '@mui/material';

// Import existing assets and components
import GuestInformation from './GuestInformation';
import PaymentDetails from './PaymentDetails';
import PaymentDocument from './PaymentDocument';
import SelectedRooms from './SelectedRooms';
import { FormSection } from './styled/FormSection';
import { useBookingPaymentContext } from '../context';
import Link from 'next/link';

// Import dialog components
import RulesAndRestrictionsDialog from '../../RuleAndRestrictions';
import TermsAndConditionDialog from '../../TermAndCondition';
import PrivacyPolicyDialog from '../../PrivacyPolicy';

const DetailsAndPayment: React.FC = () => {
  const {
    acceptedTerms,
    setAcceptedTerms,
    isFormValid,
    handleFormSubmit,
    isSubmitting,
  } = useBookingPaymentContext();

  // State for dialog visibility
  const [rulesDialogOpen, setRulesDialogOpen] = useState(false);
  const [termsDialogOpen, setTermsDialogOpen] = useState(false);
  const [privacyDialogOpen, setPrivacyDialogOpen] = useState(false);

  // Dialog handlers
  const handleRulesClick = () => setRulesDialogOpen(true);
  const handleTermsClick = () => setTermsDialogOpen(true);
  const handlePrivacyClick = () => setPrivacyDialogOpen(true);

  const handleRulesClose = () => setRulesDialogOpen(false);
  const handleTermsClose = () => setTermsDialogOpen(false);
  const handlePrivacyClose = () => setPrivacyDialogOpen(false);

  return (
    <Box
      sx={{
        width: '100%',
        mx: 'auto',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        gap: '20px',
      }}
    >
      {/* Right Section - 1/3 width - Shows first on mobile */}
      <Box
        sx={{
          width: { xs: '100%', md: '33%' },
          order: { xs: 1, md: 2 },
          backgroundColor: { xs: '#F8FAFC', md: 'unset' },
          padding: '20px',
        }}
      >
        <SelectedRooms />
      </Box>

      {/* Left Section - 2/3 width - Shows second on mobile */}
      <Box
        sx={{
          width: { xs: '100%', md: 'calc(100% - 33% - 20px)' },
          order: { xs: 2, md: 1 },
          padding: '20px',
        }}
      >
        <form>
          {/* Guest Information Section */}
          <GuestInformation />

          <Divider sx={{ my: 4, borderColor: '#D8DDE2' }} />

          {/* Payment Details Section */}
          <PaymentDetails />

          <Divider sx={{ my: 4, borderColor: '#D8DDE2' }} />

          {/* Accordion Sections */}
          <PaymentDocument />

          {/* Important Information Checkbox */}
          <FormSection>
            <Typography
              variant='h6'
              fontWeight={600}
              mb={2}
              fontSize={'16px'}
              color='#3B3F47'
            >
              Important Information about Your Booking
            </Typography>

            <FormControlLabel
              control={
                <Checkbox
                  checked={acceptedTerms}
                  onChange={(e) => setAcceptedTerms(e.target.checked)}
                  sx={{
                    color: '#011589',
                    '&.Mui-checked': {
                      color: '#011589',
                    },
                  }}
                />
              }
              label={
                <Typography fontSize='16px' color='#3B3F47' fontWeight={400}>
                  By checking this box, I acknowledge that I have read and
                  accept the{' '}
                  <Box
                    component={'span'}
                    onClick={handleRulesClick}
                    sx={{
                      p: 0,
                      textDecoration: 'underline',
                      color: '#011589',
                      fontWeight: 400,
                      cursor: 'pointer',
                      '&:hover': {
                        opacity: 0.8,
                      },
                    }}
                  >
                    Rules & Restrictions,
                  </Box>{' '}
                  <Box
                    component={'span'}
                    onClick={handleTermsClick}
                    sx={{
                      p: 0,
                      textDecoration: 'underline',
                      color: '#011589',
                      fontWeight: 400,
                      cursor: 'pointer',
                      '&:hover': {
                        opacity: 0.8,
                      },
                    }}
                  >
                    Terms of Use,{' '}
                  </Box>
                  and{' '}
                  <Box
                    component={'span'}
                    onClick={handlePrivacyClick}
                    sx={{
                      p: 0,
                      textDecoration: 'underline',
                      color: '#011589',
                      fontWeight: 400,
                      cursor: 'pointer',
                      '&:hover': {
                        opacity: 0.8,
                      },
                    }}
                  >
                    Privacy Policy
                  </Box>
                </Typography>
              }
            />
          </FormSection>

          {/* Pay Now Button */}
          <Button
            variant='contained'
            disabled={!isFormValid || isSubmitting}
            onClick={handleFormSubmit}
            sx={{
              width: '100%',
              maxHeight: '42px',
              maxWidth: '304px',
              margin: '0 auto',
              display: 'block',
              fontSize: '14px',
              fontWeight: 600,
              borderRadius: '4px',
              backgroundColor: '#011589',
              color: '#fff',
              '&:hover': {
                backgroundColor: 'white',
                color: '#011589',
              },
              '&:disabled': {
                backgroundColor: '#B2B9DC',
                color: '#fff',
              },
            }}
          >
            {isSubmitting ? 'SUBMITTING...' : 'PAY NOW'}
          </Button>
        </form>
      </Box>

      {/* Dialog Components */}
      <RulesAndRestrictionsDialog
        open={rulesDialogOpen}
        onClose={handleRulesClose}
      />
      
      <TermsAndConditionDialog
        open={termsDialogOpen}
        onClose={handleTermsClose}
      />
      
      <PrivacyPolicyDialog
        open={privacyDialogOpen}
        onClose={handlePrivacyClose}
      />
    </Box>
  );
};

export default DetailsAndPayment;
