import React from 'react';
import { FormSection } from './styled/FormSection';
import { useBookingPaymentContext } from '../context';
import { ICountry, IGuestDetail } from '@/models/Booking';

import {
  Box,
  Typography,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Autocomplete,
  FormHelperText,
} from '@mui/material';
import { countryCodeToFlag } from '@/utils/getCountryFlag';

const genders: string[] = ['Male', 'Female'];
const identifications: string[] = ['MyKad', 'Passport'];

const GuestInformation: React.FC = () => {
  const { guestDetail, setGuestDetail, form, guestsCountry } =
    useBookingPaymentContext();
  const {
    formState: { errors },
  } = form;

  const handleInputChange = React.useCallback(
    (field: keyof IGuestDetail, value: string) => {
      setGuestDetail({
        [field]: value,
      } as Partial<IGuestDetail>);
    },
    [setGuestDetail]
  );

  // Helper function to get nested error messages
  const getFieldError = React.useCallback(
    (fieldPath: string) => {
      return fieldPath
        .split('.')
        .reduce((obj, key) => obj?.[key], errors as any);
    },
    [errors]
  );

  // Helper functions for phone handling
  const getPhonePrefix = React.useMemo(
    () => guestDetail.phone?.split(' ')[0] || '+60',
    [guestDetail.phone]
  );

  const getPhoneNumber = React.useMemo(
    () => guestDetail.phone?.split(' ')[1] || '',
    [guestDetail.phone]
  );

  return (
    <FormSection>
      <Typography
        variant='h5'
        fontWeight={500}
        color='#3B3F47'
        mb={3}
        fontSize={{ xs: '28px', md: '28px' }}
      >
        Guest Information
      </Typography>

      <Grid container spacing={2} sx={{ margin: 0, width: '100%' }}>
        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            FIRST NAME
          </Typography>
          <TextField
            fullWidth
            variant='outlined'
            value={guestDetail.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            error={!!getFieldError('guestDetail.firstName')}
            helperText={getFieldError('guestDetail.firstName')?.message}
            sx={{
              '& .MuiOutlinedInput-root': {
                maxHeight: '42px',
                backgroundColor: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.firstName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.firstName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.firstName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              },
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            LAST NAME
          </Typography>
          <TextField
            fullWidth
            variant='outlined'
            value={guestDetail.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            error={!!getFieldError('guestDetail.lastName')}
            helperText={getFieldError('guestDetail.lastName')?.message}
            sx={{
              '& .MuiOutlinedInput-root': {
                maxHeight: '42px',
                backgroundColor: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.lastName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.lastName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.lastName')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              },
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            GENDER
          </Typography>
          <FormControl fullWidth error={!!getFieldError('guestDetail.gender')}>
            <Select
              value={guestDetail.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
              displayEmpty
              sx={{
                backgroundColor: 'white',
                maxHeight: '42px',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.gender')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.gender')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.gender')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              }}
            >
              {genders.map((gender) => (
                <MenuItem key={gender} value={gender}>
                  {gender}
                </MenuItem>
              ))}
            </Select>
            {getFieldError('guestDetail.gender') && (
              <FormHelperText>
                {getFieldError('guestDetail.gender')?.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            NATIONALITY
          </Typography>
          <FormControl
            fullWidth
            error={!!getFieldError('guestDetail.nationality')}
          >
            <Autocomplete
              options={guestsCountry || []}
              getOptionLabel={(option) => option.countryName}
              value={
                guestsCountry?.find(
                  (c) => c.countryCode === guestDetail.nationality
                ) || null
              }
              onChange={(event, newValue: ICountry | null) => {
                handleInputChange('nationality', newValue?.countryCode || '');
                if (newValue) {
                  handleInputChange('phone', `+${newValue.prefix}`);
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant='outlined'
                  placeholder='Malaysia'
                  error={!!getFieldError('guestDetail.nationality')}
                  sx={{
                    maxHeight: '42px',
                    '& .MuiOutlinedInput-root': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: getFieldError('guestDetail.nationality')
                          ? '#d32f2f !important'
                          : '#989CA4 !important',
                        borderWidth: '1px !important',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: getFieldError('guestDetail.nationality')
                          ? '#d32f2f !important'
                          : '#989CA4 !important',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: getFieldError('guestDetail.nationality')
                          ? '#d32f2f !important'
                          : '#989CA4 !important',
                        borderWidth: '1px !important',
                      },
                      '& .MuiInputBase-input': {
                        padding: '0px 5px !important',
                      },
                    },
                  }}
                />
              )}
              sx={{
                maxHeight: '42px',
                '& .MuiOutlinedInput-root': {
                  maxHeight: '42px',
                  backgroundColor: 'white',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: getFieldError('guestDetail.nationality')
                      ? '#d32f2f !important'
                      : '#989CA4 !important',
                    borderWidth: '1px !important',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: getFieldError('guestDetail.nationality')
                      ? '#d32f2f !important'
                      : '#989CA4 !important',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: getFieldError('guestDetail.nationality')
                      ? '#d32f2f !important'
                      : '#989CA4 !important',
                    borderWidth: '1px !important',
                  },
                },
              }}
            />
            {getFieldError('guestDetail.nationality') && (
              <FormHelperText>
                {getFieldError('guestDetail.nationality')?.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            IDENTIFICATION
          </Typography>
          <FormControl
            fullWidth
            error={!!getFieldError('guestDetail.identification')}
          >
            <Select
              value={guestDetail.identification}
              onChange={(e) =>
                handleInputChange('identification', e.target.value)
              }
              sx={{
                maxHeight: '42px',
                backgroundColor: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.identification')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.idType')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.idType')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              }}
            >
              {identifications.map((id) => (
                <MenuItem key={id} value={id}>
                  {id}
                </MenuItem>
              ))}
            </Select>
            {getFieldError('guestDetail.identification') && (
              <FormHelperText>
                {getFieldError('guestDetail.idType')?.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            YOUR MYKAD NUMBER
          </Typography>
          <TextField
            fullWidth
            variant='outlined'
            value={guestDetail.idNumber}
            onChange={(e) => handleInputChange('idNumber', e.target.value)}
            error={!!getFieldError('guestDetail.idNumber')}
            helperText={getFieldError('guestDetail.idNumber')?.message}
            sx={{
              '& .MuiOutlinedInput-root': {
                maxHeight: '42px',
                backgroundColor: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.idNumber')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.idNumber')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.idNumber')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              },
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            EMAIL
          </Typography>
          <TextField
            fullWidth
            variant='outlined'
            type='email'
            value={guestDetail.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={!!getFieldError('guestDetail.email')}
            helperText={getFieldError('guestDetail.email')?.message}
            sx={{
              '& .MuiOutlinedInput-root': {
                maxHeight: '42px',
                backgroundColor: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.email')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.email')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.email')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
              },
            }}
          />
          <Typography
            variant='caption'
            color='#3B3F47'
            fontSize={'14px'}
            mt={1}
          >
            We&apos;ll send a confirmation email to this address.
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Typography
            fontWeight={600}
            fontSize={{ xs: '12px', md: '16px' }}
            color='#3B3F47'
            mb={1}
          >
            PHONE
          </Typography>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              '&:focus-within': {
                '& .phone-select .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.phone')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                },
                '& .phone-input .MuiOutlinedInput-notchedOutline': {
                  borderColor: getFieldError('guestDetail.phone')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                  borderWidth: '1px !important',
                  borderLeft: '1px solid #989CA4 !important',
                  borderLeftColor: getFieldError('guestDetail.phone')
                    ? '#d32f2f !important'
                    : '#989CA4 !important',
                },
              },
            }}
          >
            <FormControl sx={{ width: '40%' }}>
              <Autocomplete
                className='phone-select'
                options={guestsCountry || []}
                getOptionLabel={(option) =>
                  `${countryCodeToFlag(option.countryCode)} +${option.prefix}`
                }
                clearOnBlur={false}
                disableClearable={true}
                value={
                  guestsCountry?.find(
                    (country) => `+${country.prefix}` === getPhonePrefix
                  ) || undefined
                }
                onChange={(event, newValue: ICountry | null) => {
                  const prefix = newValue ? `+${newValue.prefix}` : '+60';
                  handleInputChange(
                    'phone',
                    `${prefix} ${getPhoneNumber}`.trim()
                  );
                }}
                renderOption={(props, option) => (
                  <Box component='li' {...props}>
                    {countryCodeToFlag(option.countryCode)} +{option.prefix}
                  </Box>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant='outlined'
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        height: '42px',
                        maxHeight: '42px',
                        backgroundColor: 'white',
                        borderTopRightRadius: 'unset !important',
                        borderBottomRightRadius: 'unset !important',
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: getFieldError('guestDetail.phone')
                            ? '#d32f2f !important'
                            : '#989CA4 !important',
                          borderRight: getFieldError('guestDetail.phone')
                            ? '1px solid #d32f2f !important'
                            : '1px solid #989CA4 !important',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: getFieldError('guestDetail.phone')
                            ? '#d32f2f !important'
                            : '#989CA4 !important',
                          borderRight: getFieldError('guestDetail.phone')
                            ? '1px solid #d32f2f !important'
                            : '1px solid #989CA4 !important',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: getFieldError('guestDetail.phone')
                            ? '#d32f2f !important'
                            : '#989CA4 !important',
                          borderRight: getFieldError('guestDetail.phone')
                            ? '1px solid #d32f2f !important'
                            : '1px solid #989CA4 !important',
                          borderWidth: '1px !important',
                        },
                        '& .MuiInputBase-input': {
                          padding: '0px 5px !important',
                        },
                      },
                    }}
                  />
                )}
                sx={{
                  maxHeight: '42px',
                  borderTopRightRadius: 'unset !important',
                  borderBottomRightRadius: 'unset !important',
                  '& .MuiOutlinedInput-root': {
                    borderTopRightRadius: 'unset !important',
                    borderBottomRightRadius: 'unset !important',
                  },
                }}
              />
            </FormControl>
            <TextField
              className='phone-input'
              fullWidth
              variant='outlined'
              placeholder='123456789'
              value={getPhoneNumber}
              error={!!getFieldError('guestDetail.phone')}
              onChange={(e) => {
                handleInputChange(
                  'phone',
                  `${getPhonePrefix} ${e.target.value}`.trim()
                );
              }}
              sx={{
                flex: 1,
                borderLeft: 'none !important',
                '& .MuiOutlinedInput-root': {
                  maxHeight: '42px',
                  backgroundColor: 'white',
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  borderLeft: 'none !important',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: getFieldError('guestDetail.phone')
                      ? '#d32f2f !important'
                      : '#989CA4 !important',
                    borderLeft: 'none !important',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: getFieldError('guestDetail.phone')
                      ? '#d32f2f !important'
                      : '#989CA4 !important',
                    borderLeft: 'none !important',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderLeft: 'none !important',
                  },
                },
              }}
            />
          </Box>
          {getFieldError('guestDetail.phone') && (
            <FormHelperText error sx={{ mt: 0.5 }}>
              {getFieldError('guestDetail.phone')?.message}
            </FormHelperText>
          )}
        </Grid>
      </Grid>
    </FormSection>
  );
};

export default GuestInformation;
