'use client';

import React, { useEffect } from 'react';
import { Box, Typography, Button, Divider } from '@mui/material';
import { SelectedRoomCard } from './styled/SelectedRoomCard';
import { useBookingPaymentContext } from '../context';
import LockIcon from '@/components/icons/LockIcon';
import AccessibleButton from '@/components/new-version/AccessibleButton';

const SelectedRooms = () => {
  const {
    selectedRooms,
    hotelDetail,
    taxAndServiceCharge,
    checkInInformation,
    form,
    checkInTime,
    checkoutTime,
    outletInfo,
  } = useBookingPaymentContext();

  const taxAmountInNumber = taxAndServiceCharge?.taxAmount
    ? parseInt(taxAndServiceCharge?.taxAmount)
    : 0;

  const sum = selectedRooms.reduce((acc, room) => {
    return acc + Number(room?.price) * Number(room.quantity);
  }, 0);

  
  const totalTaxAmount = sum * (taxAmountInNumber / 100);
  
  const creditAmount = sum + totalTaxAmount;


  useEffect(() => {
    form.setValue('sum', sum);
  }, [sum, form]);

  useEffect(() => {
    form.setValue('creditAmount', creditAmount);
  }, [creditAmount, form]);

  return (
    <Box>
      <SelectedRoomCard>
        <Typography
          variant='h6'
          fontWeight={600}
          mb={2}
          color='#0F0E0E'
          fontSize={'20px'}
        >
          {outletInfo && (
            <>
              {outletInfo?.title} <br />@ {outletInfo?.terminal}
            </>
          )}
        </Typography>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Button
            variant='contained'
            sx={{
              backgroundColor: '#D9F2EF',
              color: '#0F0E0E',
              px: 2,
              py: 0.5,
              borderRadius: '4px',
              fontSize: '14px',
              fontWeight: 600,
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: '#D9F2EF',
              },
              '&:focus': {
                backgroundColor: '#D9F2EF',
              },
            }}
          >
            {outletInfo?.location}
          </Button>
          <AccessibleButton isPublicArea={outletInfo?.isPublicArea} />
        </Box>

        <Typography color='#3B3F47' mb={3} fontSize={'14px'}>
          {hotelDetail?.hotelDetailedLocation}
        </Typography>

        <Divider sx={{ my: 3 }} />

        <Typography fontWeight={600} mb={2}>
          YOU SELECTED
        </Typography>

        {/* Selected Room Items */}
        <Box>
          {selectedRooms.map((room, index) => (
            <Box key={index} sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px',
                  backgroundImage: `url(${room.room?.images?.[0]?.url})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  flexShrink: 0,
                }}
              />
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              >
                <Typography
                  fontWeight={600}
                  sx={{ mb: 0.5, fontSize: '16px', color: '#3B3F47' }}
                >
                  {room.quantity}x {room.roomName}{' '}
                  {room.zone ? `@ ${room.zone}` : ''}
                </Typography>
                <Typography fontSize='14px' color='#989CA4'>
                  {room.room?.bedType} • {room.room?.capacity}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Booking Details */}
        <Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: 1,
              mb: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: '4px',
              }}
            >
              <Typography fontSize='12px' fontWeight={600} color='#989CA4'>
                CHECK-IN
              </Typography>
              <Typography color='#3B3F47' fontSize='14px' fontWeight={600}>
                {checkInTime.dateInString}
              </Typography>
              <Typography color='#3B3F47' fontSize='14px' fontWeight={600}>
                {checkInTime.timeInString}
              </Typography>
            </Box>
            <Divider orientation='vertical' flexItem />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: '4px',
              }}
            >
              <Typography fontSize='12px' fontWeight={600} color='#989CA4'>
                CHECK-OUT
              </Typography>
              <Typography color='#3B3F47' fontSize='14px' fontWeight={600}>
                {checkoutTime.dateInString}
              </Typography>
              <Typography color='#3B3F47' fontSize='14px' fontWeight={600}>
                {checkoutTime.timeInString}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Typography fontSize='12px' fontWeight={600} color='#989CA4'>
              STAY DURATION
            </Typography>
            <Typography color='#3B3F47' fontSize='14px' fontWeight={600}>
              {checkInInformation?.stayDuration} Hours
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Price Details */}
        <Box>
          <Typography fontSize='12px' fontWeight={600} color='#989CA4'>
            PRICE DETAILS
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography fontSize='16px' fontWeight={400} color='#3B3F47'>
              Total Room Price
            </Typography>
            <Typography fontSize='16px' fontWeight={400} color='#3B3F47'>
              RM{sum.toFixed(2)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography fontSize='16px' fontWeight={400} color='#3B3F47'>
              Taxes ({taxAmountInNumber}%)
            </Typography>
            <Typography fontSize='16px' fontWeight={400} color='#3B3F47'>
              RM{totalTaxAmount.toFixed(2)}
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 2,
              gap: '4px',
            }}
          >
            <Typography fontSize='16px' fontWeight={400} color='#3B3F47'>
              Have a promo code?
            </Typography>
            <Button
              variant='text'
              size='small'
              sx={{ textDecoration: 'underline', color: '#011589' }}
            >
              Add
            </Button>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography
            variant='h6'
            fontWeight={600}
            fontSize={'20px'}
            color='#3B3F47'
          >
            Price
          </Typography>
          <Typography
            variant='h6'
            fontWeight={600}
            fontSize={'20px'}
            color='#3B3F47'
          >
            RM{creditAmount.toFixed(2)}
          </Typography>
        </Box>
      </SelectedRoomCard>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          color: '#3B3F47',
        }}
      >
        <LockIcon />
        <Typography fontWeight={400} fontSize={'16px'} color='#3B3F47'>
          Secure Transaction
        </Typography>
      </Box>
    </Box>
  );
};

export default SelectedRooms;
