import React from 'react';
import {
  <PERSON>,
  Typography,
  Container,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';

export const CapsuleTransitIcon = () => {
  return (
    <svg
      width='160'
      height='38'
      viewBox='0 0 187 38'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M5.42285 25.9452L16.1319 32.1124L16.121 27.1052L9.75012 23.4434L5.42285 25.9452Z'
        fill='white'
      />
      <path
        d='M27.5136 12.3309L27.1863 12.1418L16.8081 6.1709V11.1745L23.1827 14.8364L27.5136 12.3309Z'
        fill='white'
      />
      <path
        d='M16.0981 6.1709L5.3999 12.36L9.73809 14.8545L16.1017 11.1745L16.0981 6.1709Z'
        fill='white'
      />
      <path
        d='M28.2182 11.92L32.5491 9.41089L16.7964 0.341797L16.8073 5.34907L25.7818 10.5163L28.2182 11.92Z'
        fill='white'
      />
      <path
        d='M5.06603 25.3349L9.39694 22.8294L9.38603 15.4803L5.04785 12.9785L5.05876 19.1567L5.06603 25.3349Z'
        fill='white'
      />
      <path
        d='M16.8554 37.9381L32.5827 28.8217L28.2445 26.3271L16.8481 32.9308L16.8554 37.9381Z'
        fill='white'
      />
      <path
        d='M27.5349 25.9205L23.2003 23.4224L16.8403 27.106L16.844 32.1133L27.5349 25.9205Z'
        fill='white'
      />
      <path
        d='M0.378418 28.869L16.1348 37.938L16.1311 32.9308L4.71296 26.3599L0.378418 28.869Z'
        fill='white'
      />
      <path
        d='M16.0835 0.341797L0.352539 9.45452L4.69436 11.9527L16.0944 5.34907L16.0835 0.341797Z'
        fill='white'
      />
      <path
        d='M4.33455 12.5707L0 10.0762L0.0254544 28.2543L4.36 25.7453L4.33455 12.5707Z'
        fill='white'
      />
      <path
        d='M60.5483 20.919C60.3592 21.4099 59.9955 22.279 59.2537 23.1735C58.1919 24.4062 56.5119 25.4899 54.0683 25.4899C50.0937 25.4899 47.0537 22.5772 47.0537 17.559C47.0537 12.5408 50.3483 9.71533 54.1737 9.71533C58.8501 9.71533 59.9555 13.3081 60.2974 14.3481L57.4901 15.0935C57.4501 14.8172 57.3192 14.1772 56.9374 13.6062C56.6174 13.1372 55.7883 12.2862 54.2355 12.2862C51.7919 12.2862 50.0901 14.3044 50.0901 17.5153C50.0901 20.9372 51.8973 22.8281 54.2573 22.8281C55.661 22.8281 56.5083 22.1481 57.1264 21.3626C57.6137 20.7081 57.8064 20.1517 57.9119 19.8135L60.5483 20.919Z'
        fill='white'
      />
      <path
        d='M86.618 21.9582C87.2543 22.7437 88.2107 23.3182 89.6761 23.3182C91.1416 23.3182 91.7816 22.5946 91.7816 22.0637C91.7816 21.3837 91.018 21.2127 90.6798 21.1509C90.3161 21.0673 88.5743 20.7437 88.1925 20.6418C85.6616 20.0455 85.1961 18.58 85.1961 17.6237C85.1961 15.8164 86.8325 14.1582 89.7452 14.1582C91.3598 14.1582 92.5271 14.6709 93.2507 15.18C93.7852 15.5182 94.1234 15.8818 94.3343 16.1364L92.5271 17.8164C92.2325 17.2418 91.5089 16.2018 89.6361 16.2018C88.4216 16.2018 87.8725 16.7327 87.8725 17.2855C87.8725 18.0491 88.658 18.2637 90.018 18.4964C92.2289 18.9 92.8434 19.0055 93.5234 19.54C94.1816 20.0491 94.6107 20.9 94.6107 21.7909C94.6107 23.4491 93.1234 25.4237 89.8252 25.4237C87.9343 25.4237 86.0652 24.8273 84.7271 23.5291L86.618 21.9582Z'
        fill='white'
      />
      <path
        d='M106.018 14.4743V25.1034H103.469V24.0198C103.022 24.4452 101.978 25.358 100.022 25.358C98.2802 25.358 97.4512 24.6125 97.0693 24.1252C96.3457 23.2089 96.3457 22.2125 96.3457 20.6198V14.4743H98.9202V20.5762C98.9202 21.0016 98.9202 21.3616 99.0075 21.638C99.2802 22.5725 100.048 22.9762 100.982 22.9762C102.048 22.9762 102.706 22.4016 103.044 21.8489C103.426 21.1471 103.448 20.4671 103.469 18.9798V14.4707H106.018V14.4743Z'
        fill='white'
      />
      <path d='M111.072 10.0967V25.104H108.501V10.0967H111.072Z' fill='white' />
      <path
        d='M137.491 14.474V15.9831C137.814 15.4303 138.512 14.1758 140.894 14.2412V16.8558C140.342 16.8776 139.385 16.8958 138.6 17.4921C137.516 18.3212 137.494 19.4049 137.494 21.1685V25.0994H135.029V14.4703H137.494L137.491 14.474Z'
        fill='white'
      />
      <path
        d='M153.639 14.4743H156.21V15.7288C156.337 15.5361 156.596 15.1979 157.039 14.8998C157.679 14.4343 158.657 14.1543 159.483 14.1543C160.697 14.1543 161.737 14.707 162.268 15.4307C162.926 16.3252 162.926 17.4707 162.926 18.8525V25.0998H160.396V19.6816C160.396 18.8925 160.396 18.3616 160.312 18.067C160.079 17.1325 159.399 16.6416 158.57 16.6416C157.741 16.6416 156.977 17.1325 156.614 17.8307C156.21 18.6161 156.21 19.6998 156.21 20.4888V25.0998H153.639V14.4707V14.4743Z'
        fill='white'
      />
      <path
        d='M71.4005 23.3798V17.747C71.4005 16.5979 71.3786 15.6016 70.1241 14.8343C69.1896 14.2816 67.9532 14.1543 66.935 14.1543C66.255 14.1543 63.7241 14.1543 62.7059 16.0052C62.3641 16.6234 62.3241 17.0707 62.2768 17.5979L64.895 17.747C64.895 17.4488 64.8732 17.1543 65.1496 16.8125C65.4041 16.4707 65.9786 16.1325 67.0405 16.1325C68.8077 16.1325 68.8477 17.2161 68.8477 17.747V20.9798C68.8477 21.2961 68.8477 21.6161 68.6986 21.9325C68.4223 22.5688 67.5096 23.1688 66.2114 23.1688C65.1932 23.1688 64.4914 22.7434 64.4914 21.9143C64.4914 20.787 65.7023 20.5761 66.6186 20.4452C67.3496 20.3252 67.8514 20.2016 68.2477 20.0634V17.9325C67.7023 18.0888 67.1968 18.1979 65.5532 18.5325C64.5314 18.7216 62.895 19.0634 62.1314 20.5943C61.9168 21.0198 61.7896 21.507 61.7896 22.0416C61.7896 23.8707 63.2332 25.3579 65.6368 25.3579C67.1023 25.3579 68.2296 24.7398 68.7786 24.2743C68.7786 24.4234 68.8223 24.8488 68.8441 25.1034H71.8623C71.3932 24.467 71.3932 24.1907 71.3932 23.3834L71.4005 23.3798Z'
        fill='white'
      />
      <path
        d='M76.2203 20.3622C76.1985 20.1622 76.1876 19.9476 76.1912 19.7258C76.1912 19.5694 76.2058 19.4167 76.2203 19.2749V15.4458C76.2203 15.4458 76.2167 15.4531 76.213 15.4567V14.4785H73.6821V28.5731H76.213V24.2349C76.213 24.2349 76.2203 24.2349 76.2203 24.2422V20.3658V20.3622Z'
        fill='white'
      />
      <path
        d='M79.1258 14.1543C78.1221 14.1543 77.4167 14.4743 76.9258 14.8198V17.4416C77.4276 16.8525 78.1076 16.5979 78.7403 16.5979C79.504 16.5979 81.3549 17.1107 81.3549 19.7652C81.3549 22.1652 80.0167 23.0161 78.784 23.0161C78.0676 23.0161 77.4021 22.7325 76.9258 22.1725V24.7834C77.4567 25.1252 78.1731 25.4197 79.1258 25.4197C81.9731 25.4197 83.9512 23.0634 83.9512 19.8961C83.9512 16.9616 82.2494 14.1543 79.1258 14.1543Z'
        fill='white'
      />
      <path d='M134.639 10.0962H123.628V12.638H134.639V10.0962Z' fill='white' />
      <path
        d='M130.491 13.2422H127.687V25.1004H130.491V13.2422Z'
        fill='white'
      />
      <path
        d='M116.458 18.4277H120.738C120.505 16.7695 119.483 16.195 118.356 16.195C116.996 16.195 116.061 16.9805 115.763 18.4277C115.694 18.755 115.658 19.0786 115.647 19.4132C115.636 19.7041 115.654 20.0095 115.69 20.2968C115.814 21.2786 116.374 22.2277 117.294 22.6532C117.73 22.8568 118.218 22.9332 118.698 22.9332C120.439 22.9332 121.141 21.9114 121.458 21.4641L123.628 22.7186C122.312 24.4859 120.61 25.4168 118.439 25.4168C115.654 25.4168 112.978 23.6532 112.978 19.7405C112.978 16.1695 115.272 14.1514 118.272 14.1514C121.079 14.1514 122.247 15.8314 122.461 16.1514C123.33 17.3841 123.523 19.2314 123.541 20.2968H116.407C116.407 20.2968 116.348 20.1405 116.348 19.3841C116.348 18.8714 116.458 18.4277 116.458 18.4277Z'
        fill='white'
      />
      <path
        d='M151.479 23.3798V17.747C151.479 16.5979 151.457 15.6016 150.206 14.8343C149.272 14.2816 148.039 14.1543 147.017 14.1543C146.337 14.1543 143.806 14.1543 142.788 16.0052C142.45 16.6234 142.406 17.0707 142.363 17.5979L144.977 17.747C144.977 17.4488 144.955 17.1543 145.232 16.8125C145.486 16.4707 146.061 16.1325 147.126 16.1325C148.89 16.1325 148.934 17.2161 148.934 17.747V20.9798C148.934 21.2961 148.934 21.6161 148.785 21.9325C148.508 22.5688 147.592 23.1688 146.297 23.1688C145.275 23.1688 144.577 22.7434 144.577 21.9143C144.577 20.787 145.788 20.5761 146.705 20.4452C147.439 20.3252 147.937 20.2016 148.334 20.0634V17.9325C147.788 18.0888 147.283 18.1979 145.639 18.5325C144.621 18.7216 142.981 19.0634 142.217 20.5943C142.006 21.0198 141.875 21.507 141.875 22.0416C141.875 23.8707 143.319 25.3579 145.723 25.3579C147.188 25.3579 148.315 24.7398 148.868 24.2743C148.868 24.4234 148.912 24.8488 148.934 25.1034H151.952C151.483 24.467 151.483 24.1907 151.483 23.3834L151.479 23.3798Z'
        fill='white'
      />
      <path
        d='M166.257 22.0093C166.905 22.8057 167.872 23.3875 169.356 23.3875C170.839 23.3875 171.483 22.6566 171.483 22.1184C171.483 21.4275 170.712 21.2566 170.367 21.1911C169.999 21.1038 168.236 20.7802 167.85 20.6747C165.29 20.0747 164.817 18.5875 164.817 17.6202C164.817 15.7947 166.476 14.1147 169.421 14.1147C171.057 14.1147 172.239 14.6311 172.97 15.1438C173.508 15.4893 173.85 15.8566 174.065 16.1147L172.239 17.8129C171.937 17.2311 171.207 16.1766 169.312 16.1766C168.087 16.1766 167.527 16.7147 167.527 17.2747C167.527 18.0493 168.323 18.2638 169.701 18.5038C171.937 18.9111 172.559 19.0166 173.25 19.5547C173.916 20.0748 174.348 20.9329 174.348 21.8347C174.348 23.5111 172.843 25.5111 169.508 25.5111C167.596 25.5111 165.701 24.9075 164.345 23.5984L166.261 22.0057L166.257 22.0093Z'
        fill='white'
      />
      <path
        d='M178.585 10.1577V12.7614H176.025V10.1577H178.585ZM178.585 14.4341V25.1904H176.025V14.4341H178.585Z'
        fill='white'
      />
      <path
        d='M184.607 16.2849V21.1467C184.607 22.4813 184.607 23.0595 185.836 23.0595C186.225 23.0595 186.545 23.0376 186.909 22.9322V25.1067C186.134 25.234 185.618 25.2813 185.04 25.2813C184.498 25.2813 182.694 25.2813 182.2 23.9686C182.029 23.5358 182.029 22.8486 182.029 21.7722V16.2886H180.461V14.4376H182.029V10.6304H184.611V14.4376H186.912V16.2886H184.611L184.607 16.2849Z'
        fill='white'
      />
    </svg>
  );
};

export const MaxIcon = () => {
  return (
    <svg
      width='170'
      height='38'
      viewBox='0 0 156 38'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M77.6141 0.790039V37.8315H76.2351V10.7508L63.6795 37.8315H61.8381L45.9864 3.63804V7.4664L60.0635 37.8315H58.5421L45.9864 10.7508V37.8315H44.6075V0.790039H46.1871L62.7602 36.5369L76.2381 7.46931V3.64095L62.8766 32.4584C62.6555 31.9668 62.4024 31.3995 62.1319 30.7857L76.0373 0.790039H77.617H77.6141ZM101.614 0.790039L86.6352 37.8315H88.1246L102.507 2.26204L103.572 4.61258L90.1406 37.8315H91.6301L104.36 6.34931L118.612 37.8315H120.128L103.36 0.790039H101.62H101.614ZM105.341 0.790039C105.341 0.790039 105.341 0.792949 105.341 0.795858L122.106 37.8315H123.621L106.853 0.790039H105.338H105.341ZM130.955 37.8111L130.987 37.8315H132.567L141.448 23.5944L150.33 37.8315H151.909L151.941 37.8111L141.448 20.9849L130.952 37.8111H130.955ZM152.026 0.790039H150.411L141.451 15.1522L132.491 0.790039H130.877C130.888 0.81913 130.903 0.848222 130.92 0.877313L140.64 16.4584L127.319 37.814C127.319 37.814 127.313 37.8286 127.31 37.8344H128.933L151.985 0.877313C152.002 0.848222 152.014 0.81913 152.029 0.790039H152.026ZM42.6875 37.8315V0.790039H41.3086V37.8315H42.6875ZM144.101 19.3442L155.621 0.877313L155.482 0.790039H154.048L142.475 19.3442L153.972 37.8315H155.554L155.586 37.8111L144.104 19.3442H144.101ZM137.838 17.8024C137.838 17.8024 137.847 17.814 137.853 17.8199C138.161 17.3544 138.437 16.9355 138.69 16.5602L128.855 0.792947H127.24C127.252 0.822037 127.266 0.851129 127.284 0.88022L137.841 17.8053L137.838 17.8024ZM79.5312 0.790039V37.8315H80.9101V0.790039H79.5312Z'
        fill='white'
      />
      <path
        d='M2.9547 7.38679C3.98743 7.39261 4.70307 6.93588 5.15398 6.41225C5.47107 6.03988 5.62816 5.67043 5.70961 5.46679L4.59543 4.99261C4.54889 5.13515 4.46452 5.36497 4.25798 5.64715C3.99325 5.97879 3.63252 6.26679 3.03907 6.26388C2.04125 6.25807 1.27907 5.45516 1.28489 4.00352C1.2907 2.64497 2.01507 1.79261 3.0507 1.79552C3.70816 1.79843 4.05725 2.15915 4.19107 2.35988C4.35107 2.60424 4.40343 2.87479 4.4238 2.99116L5.6107 2.67988C5.46816 2.23479 5.00852 0.716248 3.03034 0.704612C1.40998 0.698793 0.0107041 1.85661 -0.000932302 4.01515C-0.00965957 6.13879 1.27325 7.37806 2.9547 7.38679Z'
        fill='white'
      />
      <path
        d='M18.0384 6.53703C17.4188 6.53413 17.0115 6.28976 16.7468 5.95522L15.9438 6.61558C16.5082 7.16831 17.2995 7.42431 18.0995 7.42722C19.4958 7.43304 20.1271 6.59813 20.1329 5.89704C20.1329 5.51885 19.9555 5.15813 19.6791 4.93995C19.3911 4.71304 19.1293 4.6694 18.1955 4.49194C17.6195 4.39013 17.2878 4.29704 17.2878 3.97413C17.2878 3.73849 17.5235 3.5174 18.0384 3.52031C18.8297 3.52613 19.1351 3.9654 19.2573 4.20976L20.0253 3.50285C19.9351 3.39522 19.7926 3.24103 19.5686 3.09558C19.266 2.88031 18.7715 2.66212 18.0878 2.65922C16.8573 2.6534 16.1591 3.35449 16.1533 4.11958C16.1533 4.52394 16.3482 5.14358 17.4158 5.40249C17.5788 5.44904 18.3177 5.58576 18.4689 5.62358C18.6115 5.65267 18.9373 5.7254 18.9373 6.0134C18.9373 6.24031 18.6638 6.54286 18.0442 6.53995L18.0384 6.53703Z'
        fill='white'
      />
      <path
        d='M22.4134 7.42196C23.2425 7.42778 23.6847 7.04378 23.8738 6.86342V7.32014L24.9531 7.32596L24.9734 2.82851L23.8941 2.82269L23.8854 4.73105C23.8738 5.36233 23.8621 5.64742 23.6992 5.94414C23.5538 6.17396 23.2716 6.41832 22.8236 6.41542C22.4251 6.41542 22.1021 6.24378 21.9887 5.84524C21.9538 5.72887 21.9538 5.57469 21.9538 5.39723L21.9654 2.81396H20.8745L20.8629 5.41178C20.86 6.08378 20.8571 6.50851 21.1625 6.89542C21.3225 7.10196 21.6716 7.41905 22.4105 7.42196H22.4134Z'
        fill='white'
      />
      <path
        d='M26.0303 0.96839L26.0015 7.32178L27.0895 7.32671L27.1183 0.973327L26.0303 0.96839Z'
        fill='white'
      />
      <path
        d='M6.18444 15.7185C6.18735 14.9738 6.20189 14.5141 6.66153 14.165C6.99608 13.9148 7.40044 13.9061 7.63317 13.9003L7.63898 12.7948C6.63244 12.7628 6.32989 13.2923 6.19317 13.5279V12.8879L5.15171 12.8821L5.13135 17.3796H6.17571L6.18444 15.7185Z'
        fill='white'
      />
      <path
        d='M14.1113 15.4684C14.1113 15.1367 14.1142 14.6771 14.2859 14.3425C14.44 14.0487 14.7659 13.8422 15.115 13.8422C15.467 13.8422 15.752 14.0516 15.851 14.4473C15.8859 14.5724 15.8859 14.7993 15.883 15.1338L15.8713 17.4262L16.9419 17.432L16.9535 14.7876C16.9535 14.2029 16.9593 13.7171 16.683 13.336C16.456 13.0276 16.0168 12.792 15.5048 12.792C15.1528 12.792 14.7397 12.9054 14.4662 13.1033C14.2771 13.2284 14.1695 13.3738 14.1142 13.4524V12.9229L13.0262 12.9171L13.0059 17.4145L14.0968 17.4204L14.1055 15.4684H14.1113Z'
        fill='white'
      />
      <path
        d='M7.85377 7.35512C8.47341 7.35512 8.95341 7.09912 9.18614 6.90421C9.18614 6.9653 9.20359 7.14567 9.21232 7.2533L10.4923 7.25912C10.2945 6.98858 10.2945 6.87221 10.2945 6.52894L10.3061 4.14348C10.3061 3.65767 10.3032 3.23585 9.77377 2.91003C9.37814 2.67439 8.85741 2.61912 8.42395 2.61621C8.13595 2.61621 7.06541 2.61039 6.62905 3.39294C6.48359 3.65185 6.46323 3.84094 6.44577 4.06494L7.55123 4.13185C7.55123 4.00676 7.54541 3.88167 7.66177 3.73621C7.76941 3.59658 8.01377 3.45112 8.46468 3.45403C9.21232 3.45694 9.22686 3.91658 9.22686 4.14058L9.22105 5.01621V5.17912V5.51367C9.22105 5.64458 9.22104 5.7813 9.15704 5.91803C9.03777 6.18858 8.65086 6.43585 8.10105 6.43294C7.66759 6.43294 7.37377 6.25258 7.37377 5.90058C7.37377 5.42349 7.88868 5.33621 8.2785 5.28385C8.58977 5.23148 8.79923 5.17912 8.97086 5.12385V4.22203C8.74395 4.28603 8.52868 4.32967 7.83341 4.4693C7.39995 4.54785 6.70759 4.6904 6.37886 5.33621C6.28868 5.51367 6.23341 5.72021 6.23341 5.94712C6.2305 6.72094 6.8385 7.35221 7.85668 7.35803L7.85377 7.35512Z'
        fill='white'
      />
      <path
        d='M12.3366 6.89747C12.3366 6.89747 12.3366 6.89747 12.3395 6.90038L12.3482 5.25965C12.3395 5.17238 12.3337 5.0822 12.3366 4.98911C12.3366 4.9222 12.3424 4.8582 12.3482 4.79711L12.3569 3.17674H12.354V2.76656L11.2835 2.76074L11.2544 8.72438L12.3278 8.7302L12.3366 6.89456V6.89747Z'
        fill='white'
      />
      <path
        d='M15.6209 5.07647C15.6268 3.83429 14.9111 2.64447 13.5904 2.63574C13.1657 2.63574 12.866 2.76956 12.6566 2.9121L12.6508 4.02338C12.8631 3.7761 13.154 3.66847 13.4217 3.66847C13.7475 3.66847 14.5271 3.88956 14.5213 5.01538C14.5184 6.03356 13.9482 6.38847 13.4246 6.38556C13.122 6.38556 12.8428 6.26338 12.6391 6.02483L12.6333 7.13029C12.8573 7.27574 13.1598 7.40083 13.5642 7.40374C14.7686 7.40956 15.6093 6.41465 15.618 5.07356L15.6209 5.07647Z'
        fill='white'
      />
      <path
        d='M0.336127 11.0101L0.331055 12.0864L4.99137 12.1084L4.99644 11.032L0.336127 11.0101Z'
        fill='white'
      />
      <path
        d='M2.0472 12.3388L2.02441 17.3599L3.21131 17.3652L3.23409 12.3442L2.0472 12.3388Z'
        fill='white'
      />
      <path
        d='M30.2128 7.48534C31.132 7.48825 31.8535 7.09843 32.4149 6.3537L31.4986 5.81842C31.3619 6.00752 31.0622 6.43516 30.3262 6.43516C30.1255 6.43516 29.916 6.39734 29.7328 6.31297C29.3429 6.13261 29.1073 5.72824 29.0579 5.31515C29.0433 5.19297 29.0375 5.06206 29.0433 4.93988C29.0491 4.79734 29.0637 4.6577 29.0957 4.52388C29.2237 3.91297 29.6222 3.58134 30.1982 3.58424C30.6724 3.58424 31.1059 3.83152 31.1989 4.53261L29.3866 4.52679C29.3866 4.52679 29.34 4.71297 29.34 4.93116C29.3371 5.25406 29.3633 5.31807 29.3633 5.31807L32.3829 5.33261C32.3771 4.8817 32.2986 4.09915 31.932 3.57552C31.8419 3.4417 31.3531 2.72606 30.1633 2.72024C28.8949 2.71443 27.9175 3.56388 27.9117 5.07661C27.9029 6.73188 29.0346 7.48243 30.2128 7.48825V7.48534Z'
        fill='white'
      />
      <path
        d='M9.65742 17.5079C10.28 17.5079 10.7571 17.2519 10.9927 17.057C10.9927 17.1181 11.0101 17.2956 11.016 17.4032L12.296 17.409C12.0952 17.1385 12.0982 17.0221 12.0982 16.6788L12.1098 14.2934C12.1127 13.8076 12.104 13.3857 11.5745 13.0599C11.1789 12.8243 10.6581 12.769 10.2276 12.7661C9.93669 12.7661 8.86615 12.7603 8.42979 13.5399C8.28433 13.7988 8.26688 13.9879 8.24651 14.2119L9.35197 14.2788C9.35197 14.1567 9.34324 14.0287 9.4596 13.8832C9.57015 13.7407 9.81451 13.5981 10.2654 13.601C11.0101 13.601 11.0276 14.0636 11.0247 14.2876V15.1632L11.0189 15.3261V15.6607C11.0189 15.7916 11.0189 15.9283 10.9549 16.065C10.8356 16.3356 10.4487 16.5828 9.89888 16.5799C9.46542 16.5799 9.16869 16.3996 9.1716 16.0476C9.1716 15.5705 9.68651 15.4832 10.0734 15.4308C10.3847 15.3785 10.5971 15.3261 10.7658 15.2708V14.369C10.5389 14.4359 10.3265 14.4796 9.62833 14.6163C9.19779 14.6948 8.50251 14.8374 8.17669 15.4861C8.0836 15.6636 8.02833 15.8701 8.02833 16.097C8.02542 16.8679 8.63342 17.5021 9.64869 17.5079H9.65742Z'
        fill='white'
      />
      <path
        d='M19.671 16.7217C19.0427 16.7188 18.6354 16.4716 18.3648 16.1312L17.5503 16.8003C18.1176 17.3617 18.9205 17.6177 19.7321 17.6206C21.143 17.6265 21.7859 16.7857 21.7888 16.073C21.7888 15.6919 21.6114 15.3254 21.3321 15.1072C21.0412 14.8803 20.7765 14.8308 19.831 14.6534C19.2492 14.5516 18.9117 14.4585 18.9117 14.1297C18.9117 13.8912 19.1503 13.6643 19.671 13.6672C20.4739 13.6701 20.7794 14.1181 20.9045 14.3654L21.6812 13.6497C21.591 13.5392 21.4456 13.385 21.2187 13.2396C20.9074 13.0185 20.407 12.7974 19.7176 12.7945C18.4667 12.7887 17.7627 13.4956 17.7597 14.2694C17.7597 14.6766 17.9547 15.3079 19.0368 15.5668C19.2027 15.6134 19.9474 15.7559 20.1016 15.7908C20.247 15.817 20.5728 15.8897 20.5728 16.1836C20.5728 16.4105 20.2965 16.7217 19.6681 16.7188L19.671 16.7217Z'
        fill='white'
      />
      <path
        d='M22.5104 12.9395L22.4897 17.4951L23.5719 17.5L23.5926 12.9444L22.5104 12.9395Z'
        fill='white'
      />
      <path
        d='M22.52 11.1284L22.5146 12.231L23.5968 12.2362L23.6022 11.1337L22.52 11.1284Z'
        fill='white'
      />
      <path
        d='M25.0408 16.0618C25.0379 16.5186 25.035 16.8066 25.1077 16.9928C25.3142 17.5513 26.0793 17.5542 26.3091 17.5542C26.5535 17.5542 26.7746 17.5368 27.1004 17.4844V16.5651C26.9491 16.6088 26.8124 16.6175 26.6495 16.6175C26.1288 16.6175 26.1317 16.3702 26.1317 15.8058L26.1433 13.7491L27.1179 13.7549V12.9695L26.1462 12.9666L26.155 11.3549L25.0611 11.3491L25.0524 12.9578H24.3891L24.3862 13.7375H25.0495L25.0408 16.0618Z'
        fill='white'
      />
    </svg>
  );
};

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  '&:hover': {
    opacity: 0.8,
  },
}));

const StyledHeading = styled(Typography)(({ theme }) => ({
  fontSize: '12px !important',
  fontWeight: 600,
  color: '#A8AEB5',
  marginBottom: '16px',
  display: 'block',
  letterSpacing: '0.05em',
}));

const StyledLink = styled(Link)(({ theme }) => ({
  fontSize: '15px',
  fontWeight: 600,
  color: 'white',
  textDecoration: 'none',
  '&:hover': {
    textDecoration: 'underline',
  },
}));

const Footer = () => (
  <Box
    component='footer'
    sx={{
      width: '100%',
      bgcolor: '#011589',
      color: 'white',
      pt: { xs: 4, md: 8 },
      pb: 4,
      px: 2,
    }}
  >
    {/* Main Content Section */}
    <Container maxWidth='xl'>
      {/* Mobile Layout - 2 Column Grid */}
      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
        {/* First Row: Terminal 1 and Terminal 2 */}
        <Box
          sx={{
            pb: 4,
            borderBottom: 1,
            borderColor: '#3a4bb3',
            mb: 4,
            marginLeft: '0 !important',
            paddingLeft: '0 !important',
            fontSize: '12px !important',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: 4,
          }}
        >
          {/* Terminal 1 */}
          <Box>
            <StyledHeading variant='overline'>
              STAY AT KLIA TERMINAL 1
            </StyledHeading>
            <Stack spacing={1.5}>
              <StyledLink href='/new-version/klia-1/sleep-lounge' passHref>
                Sleep Lounge
              </StyledLink>
            </Stack>
          </Box>

          {/* Terminal 2 */}
          <Box>
            <StyledHeading variant='overline'>
              STAY AT KLIA TERMINAL 2
            </StyledHeading>
            <Stack spacing={1.5}>
              <StyledLink href='/new-version/klia-2/max' passHref>
                MAX
              </StyledLink>
              <StyledLink href='/new-version/klia-2/landside' passHref>
                Landside
              </StyledLink>

              <StyledLink href='/new-version/klia-2/airside' passHref>
                Airside
              </StyledLink>
            </Stack>
          </Box>
        </Box>

        {/* Second Row: Discover and Reach Us */}
        <Box
          sx={{
            pb: 4,
            borderBottom: 1,
            borderColor: '#3a4bb3',
            mb: 4,
            marginLeft: '0 !important',
            paddingLeft: '0 !important',
            fontSize: '12px !important',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: 4,
          }}
        >
          {/* Discover */}
          <Box>
            <StyledHeading variant='overline'>DISCOVER</StyledHeading>
            <Stack spacing={1.5}>
              {[
                { text: 'About Us', href: '/about-us' },
                { text: 'Capsule Highlight', href: '/highlight' },
                { text: 'Our Commitment', href: '/our-commitment' },
                { text: 'Newsroom', href: '/news' },
                { text: 'FAQ', href: '/faq' },
                { text: 'CSR', href: '/csr' },
              ].map((item) => (
                <StyledLink key={item.text} href={item.href} passHref>
                  {item.text}
                </StyledLink>
              ))}
            </Stack>
          </Box>

          {/* Reach Us */}
          <Box>
            <StyledHeading variant='overline'>REACH US</StyledHeading>
            <Stack spacing={1.5} sx={{ mb: 3 }}>
              <StyledLink href='/contact-us' passHref>
                Contact
              </StyledLink>
              <StyledLink href='/career' passHref>
                Career
              </StyledLink>
            </Stack>
            {/* Social Icons */}
            <Stack direction='row' sx={{ gap: '4px' }}>
              <Link href='#'>
                <StyledIconButton
                  aria-label='Instagram'
                  sx={{ color: 'white' }}
                >
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='TikTok' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='Facebook' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='LinkedIn' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z' />
                  </svg>
                </StyledIconButton>
              </Link>
            </Stack>
          </Box>
        </Box>
      </Box>

      {/* Desktop Layout */}
      <Box sx={{ display: { xs: 'none', md: 'block' } }}>
        <Box
          sx={{
            pb: 6,
            borderBottom: 1,
            borderColor: '#3a4bb3',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            gap: '40px',
          }}
        >
          {/* Terminal 1 */}
          <Box sx={{ maxWidth: '180px', width: '100%' }}>
            <StyledHeading variant='overline'>
              STAY AT KLIA TERMINAL 1
            </StyledHeading>
            <Stack spacing={2}>
              <StyledLink href='/new-version/klia-1/sleep-lounge' passHref>
                Sleep Lounge
              </StyledLink>
            </Stack>
          </Box>

          {/* Terminal 2 */}
          <Box sx={{ maxWidth: '180px', width: '100%' }}>
            <StyledHeading variant='overline'>
              STAY AT KLIA TERMINAL 2
            </StyledHeading>
            <Stack spacing={2}>
              <StyledLink href='/new-version/klia-2/max' passHref>
                MAX
              </StyledLink>
              <StyledLink href='/new-version/klia-2/landside' passHref>
                Landside
              </StyledLink>
              <StyledLink href='/new-version/klia-2/airside' passHref>
                Airside
              </StyledLink>
            </Stack>
          </Box>

          {/* divider vertical */}
          <Divider
            orientation='vertical'
            sx={{
              height: '100%',
              minHeight: '260px', // Ensure minimum height
              width: '1px',
              backgroundColor: '#21FEDD',
              alignSelf: 'stretch',
              opacity: 1,
            }}
          />

          {/* Discover */}
          <Box sx={{ maxWidth: '180px', width: '100%' }}>
            <StyledHeading variant='overline'>DISCOVER</StyledHeading>
            <Stack spacing={2}>
              {[
                { text: 'About Us', href: '/about-us' },
                { text: 'Capsule Highlight', href: '/highlight' },
                { text: 'Our Commitment', href: '/our-commitment' },
                { text: 'Newsroom', href: '/news' },
                { text: 'FAQ', href: '/faq' },
                { text: 'CSR', href: '/csr' },
              ].map((item) => (
                <StyledLink key={item.text} href={item.href} passHref>
                  {item.text}
                </StyledLink>
              ))}
            </Stack>
          </Box>

          {/* Reach Us */}
          <Box sx={{ maxWidth: '180px', width: '100%' }}>
            <StyledHeading variant='overline'>REACH US</StyledHeading>
            <Stack spacing={2} sx={{ mb: 4 }}>
              <StyledLink href='/contact-us' passHref>
                Contact
              </StyledLink>
              <StyledLink href='/career' passHref>
                Career
              </StyledLink>
            </Stack>
            {/* Social Icons */}
            <Stack direction='row' spacing={0.5}>
              <Link href='#'>
                <StyledIconButton
                  aria-label='Instagram'
                  sx={{ color: 'white' }}
                >
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='TikTok' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='Facebook' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' />
                  </svg>
                </StyledIconButton>
              </Link>
              <Link href='#'>
                <StyledIconButton aria-label='LinkedIn' sx={{ color: 'white' }}>
                  <svg
                    width='20'
                    height='20'
                    fill='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z' />
                  </svg>
                </StyledIconButton>
              </Link>
            </Stack>
          </Box>
        </Box>
      </Box>
    </Container>

    {/* Bottom Bar */}
    <Container maxWidth='xl' sx={{ pt: 4, pb: 1 }}>
      {/* Mobile Bottom Layout */}
      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
        {/* Logo and Branding */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
            mb: 4,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexWrap: 'wrap',
              flexDirection: 'column',
              gap: 4,
              mb: 5,
            }}
          >
            <CapsuleTransitIcon />
            <MaxIcon />
          </Box>
        </Box>

        {/* Location */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: 1,
            mb: 3,
          }}
        >
          <svg
            width='20'
            height='20'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            viewBox='0 0 24 24'
          >
            <path d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z' />
            <circle cx='12' cy='9' r='2.5' />
          </svg>
          <Typography
            sx={{
              textAlign: 'center',
              fontSize: '14px',
              color: '#fff',
            }}
          >
            Kuala Lumpur International Airport
            <br />
            (KLIA), Malaysia
          </Typography>
        </Box>

        {/* Copyright */}
        <Typography
          sx={{
            fontSize: '14px',
            color: '#fff',
            textAlign: 'center',
          }}
        >
          © 2025 40FT Container Sdn Bhd
          <br />
          All rights reserved /{' '}
          <Box
            component='a'
            href='#'
            sx={{
              textDecoration: 'underline',
              color: '#fff',
            }}
          >
            Terms & Privacy
          </Box>
        </Typography>
      </Box>

      {/* Desktop Bottom Layout */}
      <Box
        sx={{
          display: { xs: 'none', md: 'grid' },
          alignItems: 'center',
          gridTemplateColumns: '1fr 1fr 1fr',
          justifyContent: 'space-between',
          borderBottom: 1,
          borderColor: '#3a4bb3',
          padding: '0px 0 30px 0',
          marginBottom: '20px',
        }}
      >
        {/* Location */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <svg
            width='20'
            height='20'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            viewBox='0 0 24 24'
          >
            <path d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z' />
            <circle cx='12' cy='9' r='2.5' />
          </svg>
          <Typography sx={{ fontSize: '14px', color: '#fff' }}>
            Kuala Lumpur International Airport <br /> (KLIA), Malaysia
          </Typography>
        </Box>

        {/* Logo and Branding */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 4,
          }}
        >
          <CapsuleTransitIcon />
          <MaxIcon />
        </Box>

        {/* Copyright */}
        <Typography
          sx={{
            fontSize: '0.75rem',
            color: '#fff',
            textAlign: 'right',
          }}
        >
          © 2025 40FT Container Sdn Bhd
          <br />
          All rights reserved /{' '}
          <Box
            component='a'
            href='#'
            sx={{
              textDecoration: 'underline',
              color: '#fff',
            }}
          >
            Terms & Privacy
          </Box>
        </Typography>
      </Box>
    </Container>
  </Box>
);

export default Footer;
