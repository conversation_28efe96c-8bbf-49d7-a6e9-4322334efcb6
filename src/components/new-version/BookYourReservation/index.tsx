'use client';

import React, { useState } from 'react';
import { Box, Typography, Button, styled } from '@mui/material';
import bgImage from '@/assets/images/klia2-landside.jpg';
import { Outlet } from '@/actions/getOutlets/types';
import CheckAvailabilityModal from '@/components/new-version/CheckAvailabilityModal';

const BookYourReservationContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '502px',
  background: `linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.8) 100%), url(${bgImage.src})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center center',
  backgroundRepeat: 'no-repeat',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  overflow: 'hidden',

  // Mobile-specific fixes
  [theme.breakpoints.down('md')]: {
    backgroundAttachment: 'scroll', // Use scroll instead of fixed for mobile
    height: '400px', // Fixed height for mobile
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
  },
}));

const BackgroundOverlay = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'rgba(0, 0, 0, 0.3)',
  zIndex: 1,
});

const CentralOverlay = styled(Box)({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  textAlign: 'center',
  color: 'white',
  zIndex: 4,
  maxWidth: '500px',
  width: '100%',
  padding: '20px',
});

const BookNowButton = styled(Button)(({ theme }) => ({
  background: '#21FEDD !important',
  color: '#1a1a1a !important',
  padding: '16px 32px',
  fontSize: '16px',
  fontWeight: 700,
  borderRadius: '6px',
  textTransform: 'none',
  boxShadow: '0 4px 20px rgba(0, 212, 170, 0.4)',
  transition: 'all 0.3s ease',
  marginTop: '20px',
  width: '100%',

  '&:hover': {
    background: '#21FEDD !important',
    boxShadow: '0 6px 25px rgba(0, 212, 170, 0.5)',
  },

  '&:active': {
    background: '#21FEDD !important',
    transform: 'translateY(0)',
  },

  '&:focus': {
    background: '#21FEDD !important',
  },
}));

const BookYourReservation = ({
  outletName,
  selectedOutlet,
}: {
  outletName?: string;
  selectedOutlet?: Outlet;
}) => {
  const [isCheckAvailabilityModalOpen, setIsCheckAvailabilityModalOpen] =
    useState(false);

  const handleOpenCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(true);
  };

  const handleCloseCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(false);
  };
  return (
    <BookYourReservationContainer>
      <BackgroundOverlay />
      <CheckAvailabilityModal
        open={isCheckAvailabilityModalOpen}
        onClose={handleCloseCheckAvailabilityModal}
        outlet={selectedOutlet}
      />

      <CentralOverlay>
        <Typography
          variant='h4'
          sx={{
            color: '#fff',
            mb: '4px',
            fontWeight: 500,
            lineHeight: 1,
            fontSize: { xs: '28px', md: '40px' },
          }}
        >
          Book your reservation for
        </Typography>
        <Typography
          variant='h4'
          sx={{
            color: '#fff',
            mb: 2,
            fontWeight: 500,
            fontSize: { xs: '28px', md: '40px' },
          }}
        >
          {outletName || 'CapsuleTransit'}
        </Typography>
        <Typography
          sx={{
            color: '#fff',
            fontSize: { xs: '24px', md: '28px' },
            fontWeight: 500,
            mt: { xs: '30px', md: '34px' },
            mb: 2,
          }}
        >
          Best price guaranteed.
        </Typography>

        <BookNowButton
          variant='contained'
          size='large'
          onClick={handleOpenCheckAvailabilityModal}
        >
          BOOK NOW
        </BookNowButton>
      </CentralOverlay>
    </BookYourReservationContainer>
  );
};

export default BookYourReservation;
