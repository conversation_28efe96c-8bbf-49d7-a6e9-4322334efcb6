'use client';

import React, { useMemo, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  Divider,
} from '@mui/material';
import { AccessTime } from '@mui/icons-material';
import CheckAvailabilityModal from '../CheckAvailabilityModal';
import { Outlet } from '@/actions/getOutlets/types';
import { ROOM_OPTIONS } from '@/constant/outlet';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import ModalRoomPhotos from '../ModalRoomPhotos';
import { RoomCard, BookButton } from './styled';

const RoomOptions = ({ selectedOutlet }: { selectedOutlet?: Outlet }) => {
  const roomOptions = useMemo(() => {
    return Object.keys(selectedOutlet?.imagesByRoomType || {}).map(
      (roomType) => {
        const outlet = selectedOutlet?.imagesByRoomType?.[roomType]?.outlet;
        const roomTypeKey = `${outlet}_${roomType}`;
        const roomDetails = ROOM_OPTIONS[roomTypeKey];

        return {
          id: roomType,
          title: roomType
            .split(' ')
            .map(
              (word) =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            )
            .join(' '),
          images: selectedOutlet?.imagesByRoomType?.[roomType]?.images || [],
          roomDetails,
        };
      }
    );
  }, [selectedOutlet]);

  const theme = useTheme();

  const [isCheckAvailabilityModalOpen, setIsCheckAvailabilityModalOpen] =
    useState(false);

  const handleOpenCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(true);
  };

  const handleCloseCheckAvailabilityModal = () => {
    setIsCheckAvailabilityModalOpen(false);
  };

  const [isOpenRoomPhotosModal, setIsOpenRoomPhotosModal] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const handleOpenModal = () => {
    setIsOpenRoomPhotosModal(true);
  };

  const handleCloseModal = () => {
    setIsOpenRoomPhotosModal(false);
  };

  const handleRoomPhotos = (room: any) => {
    setSelectedRoom(room);
    handleOpenModal();
  };

  return (
    <Box sx={{ py: 8, px: { xs: 2, md: 4, lg: 8 } }}>
      <Typography
        variant='h2'
        component='h2'
        align='center'
        sx={{
          mb: 6,
          fontWeight: 700,
          color: theme.palette.primary.main,
          fontSize: { xs: '2rem', md: '2.5rem' },
        }}
      >
        Room Options
      </Typography>

      <CheckAvailabilityModal
        open={isCheckAvailabilityModalOpen}
        onClose={handleCloseCheckAvailabilityModal}
        outlet={selectedOutlet}
      />
      <ModalRoomPhotos
        images={selectedRoom?.images || []}
        isOpen={isOpenRoomPhotosModal}
        handleClose={handleCloseModal}
      />

      <Grid container spacing={4}>
        {roomOptions.map((room, index) => {
          return (
            <Grid item xs={12} key={room.id}>
              <RoomCard>
                <Grid container>
                  {/* Title - Mobile only (full width) */}
                  <Grid
                    item
                    xs={12}
                    sx={{ display: { xs: 'block', md: 'none' } }}
                  >
                    <Box sx={{ p: { xs: 2, md: 3 } }}>
                      <Typography
                        variant='h4'
                        component='h3'
                        sx={{
                          fontWeight: 700,
                          color: theme.palette.primary.main,
                          fontSize: '1.25rem',
                          textAlign: 'left',
                        }}
                      >
                        {room.title}
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Left Section - Image and Room Details */}
                  <Grid item xs={6} md={6}>
                    <Box sx={{ p: { xs: 0, md: 2 } }}>
                      {/* Title - Desktop only */}
                      <Box
                        sx={{
                          display: { xs: 'none', md: 'flex' },
                          alignItems: 'center',
                          mb: 3,
                          gap: '16px',
                        }}
                      >
                        <Typography
                          variant='h4'
                          component='h3'
                          sx={{
                            fontWeight: 600,
                            color: '#011589',
                            fontSize: { xs: '22px', md: '28px' },
                          }}
                        >
                          {room.title}
                        </Typography>
                        <BookButton
                          variant='outlined'
                          onClick={handleOpenCheckAvailabilityModal}
                          sx={{
                            height: '36px',
                          }}
                        >
                          BOOK
                        </BookButton>
                      </Box>

                      {/* Image */}
                      <Box sx={{ paddingTop: '10px' }}>
                        <Box
                          sx={{
                            position: 'relative',
                            width: '100%',
                            maxWidth: '396px',
                            height: '360px',
                            maxHeight: '264px',
                            '& .swiper-button-next, & .swiper-button-prev': {
                              top: '56%',
                              transform: 'translateY(-50%)',
                              zIndex: 10,
                              backgroundColor: 'rgba(15, 14, 14, 0.5)',
                              width: '32px',
                              height: '48px',
                              '&:hover': {
                                backgroundColor: 'rgba(15, 14, 14, 0.8)',
                              },
                              boxShadow: theme.shadows[4],
                              transition: 'all 0.2s',
                              borderRadius: '4px',
                              '&::after': {
                                fontSize: '18px',
                                color: 'white',
                                fontWeight: 'bold',
                              },
                            },
                            '& .swiper-button-prev': {
                              left: '12px',
                            },
                            '& .swiper-button-next': {
                              right: '12px',
                            },
                          }}
                        >
                          <Swiper
                            modules={[Navigation]}
                            navigation={true}
                            style={{
                              height: '100%',
                              maxHeight: '264px',
                              maxWidth: '396px',
                              width: '100%',
                            }}
                            className={`outlets-swiper-${index}`}
                          >
                            {(room.images || []).map((image, imageIndex) => (
                              <SwiperSlide
                                key={imageIndex}
                                style={{
                                  height: '100%',
                                  width: '100%',
                                  display: 'flex',
                                }}
                              >
                                <img
                                  src={image.formats?.medium || image.url}
                                  alt={image.name || 'CapsuleTransit '}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                    display: 'block',
                                    borderRadius: '8px',
                                  }}
                                />
                              </SwiperSlide>
                            ))}
                          </Swiper>
                          <Box
                            sx={{
                              position: 'absolute',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              zIndex: 4,
                              display: { xs: 'block', md: 'none' },
                              background: 'rgba(0, 0, 0, 0.5)',
                              padding: '6px 16px',
                            }}
                            onClick={() => handleRoomPhotos(room)}
                          >
                            <Typography
                              sx={{
                                color: 'white',
                                fontSize: '14px',
                                fontWeight: 500,
                                textDecoration: 'underline',
                                cursor: 'pointer',
                                textAlign: 'center',
                                '&:hover': {
                                  textDecoration: 'none',
                                },
                              }}
                            >
                              View photos
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      {/* Room Details - Mobile only */}
                      <Box
                        sx={{
                          display: {
                            xs: 'block',
                            md: 'none',
                            padding: '2px 8px',
                          },
                          mb: 2,
                        }}
                      >
                        {/* Capacity */}
                        <Box
                          sx={{
                            mb: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                          }}
                        >
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{
                              fontWeight: 600,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '10px',
                            }}
                          >
                            Room For
                          </Typography>
                          <Typography
                            variant='body1'
                            sx={{
                              color: 'text.primary',
                              fontWeight: 500,
                              fontSize: '14px',
                            }}
                          >
                            {room.roomDetails?.capacity}
                          </Typography>
                        </Box>

                        {/* Bed Type */}
                        <Box
                          sx={{
                            mb: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                          }}
                        >
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{
                              fontWeight: 600,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '10px',
                            }}
                          >
                            Bed Type
                          </Typography>
                          <Typography
                            variant='body1'
                            sx={{
                              color: 'text.primary',
                              fontWeight: 500,
                              fontSize: '14px',
                            }}
                          >
                            {room.roomDetails?.bedType}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Room Photos Link - Desktop only */}
                      <Box
                        sx={{
                          textAlign: 'left',
                          display: {
                            xs: 'none',
                            md: 'block',
                            marginTop: '10px',
                          },
                        }}
                      >
                        <Button
                          variant='text'
                          onClick={() => handleRoomPhotos(room)}
                          sx={{
                            color: theme.palette.primary.main,
                            textDecoration: 'underline',
                            fontWeight: 'normal',
                            p: 0,
                            minWidth: 'auto',
                            '&:hover': {
                              textDecoration: 'none',
                              backgroundColor: 'transparent',
                            },
                          }}
                        >
                          Room photos
                        </Button>
                      </Box>
                    </Box>
                  </Grid>

                  {/* Divider between left and right sections */}
                  <Grid
                    item
                    xs={0.5}
                    md={0.5}
                    sx={{ display: { xs: 'block', md: 'block' } }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'flex-start',
                        pt: { xs: 0, md: 3 }, // Match the padding of the left section
                      }}
                    >
                      <Divider
                        orientation='vertical'
                        sx={{
                          height: {
                            xs: 'calc(100% - 35px)',
                            md: 'calc(100% - 90px)',
                          }, // Adjust height to match image section
                          width: '1px',
                          backgroundColor: 'gray.200',
                          marginTop: { xs: '10px', md: '62px' },
                          mx: 'auto',
                          borderRightWidth: '2px',
                        }}
                      />
                    </Box>
                  </Grid>

                  {/* Right Section - Details and Pricing */}
                  <Grid item xs={5.5} md={5.5}>
                    <CardContent
                      sx={{
                        py: 1,
                        px: { xs: 2, md: 4 },
                        display: 'flex',
                        flexDirection: 'column',
                        marginTop: { xs: 0, md: '84px' },
                        height: 'fit-content',
                      }}
                    >
                      {/* Room Details - Desktop only */}
                      <Box sx={{ mb: 3, display: { xs: 'none', md: 'block' } }}>
                        {/* Zones (only for Single Capsule) */}
                        {room.roomDetails?.zones && (
                          <Box
                            sx={{
                              mb: '6px',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '16px',
                            }}
                          >
                            <Typography
                              variant='body2'
                              color='text.secondary'
                              sx={{
                                fontWeight: 600,
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px',
                                fontSize: '12px',
                              }}
                            >
                              Zones
                            </Typography>
                            <Typography
                              variant='body1'
                              sx={{
                                color: 'text.primary',
                                fontWeight: 500,
                                fontSize: '16px',
                              }}
                            >
                              {room.roomDetails?.zones?.join(' / ')}
                            </Typography>
                          </Box>
                        )}

                        {/* Capacity */}
                        <Box
                          sx={{
                            mb: '6px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                          }}
                        >
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{
                              fontWeight: 600,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '12px',
                            }}
                          >
                            Room For
                          </Typography>
                          <Typography
                            variant='body1'
                            sx={{
                              color: 'text.primary',
                              fontWeight: 500,
                              fontSize: '16px',
                            }}
                          >
                            {room.roomDetails?.capacity}
                          </Typography>
                        </Box>

                        {/* Bed Type */}
                        <Box
                          sx={{
                            mb: '6px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                          }}
                        >
                          <Typography
                            variant='body2'
                            color='text.secondary'
                            sx={{
                              fontWeight: 600,
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px',
                              fontSize: '12px',
                            }}
                          >
                            Bed Type
                          </Typography>
                          <Typography
                            variant='body1'
                            sx={{
                              color: 'text.primary',
                              fontWeight: 500,
                              fontSize: '16px',
                            }}
                          >
                            {room.roomDetails?.bedType}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Zones - Mobile only */}
                      {room.roomDetails?.zones && (
                        <Box
                          sx={{
                            mb: 2,
                            display: { xs: 'block', md: 'none' },
                          }}
                        >
                          <Box
                            sx={{
                              mb: '4px',
                              alignItems: { xs: 'flex-start', xss: 'center' },
                              gap: '8px',
                              display: { xs: 'flex', md: 'none' },
                              flexDirection: { xs: 'column', xxs: 'row' },
                            }}
                          >
                            <Typography
                              variant='body2'
                              color='text.secondary'
                              sx={{
                                fontWeight: 600,
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px',
                                fontSize: '10px',
                              }}
                            >
                              Zones
                            </Typography>
                            <Typography
                              variant='body1'
                              sx={{
                                color: 'text.primary',
                                fontWeight: 500,
                                fontSize: '14px',
                                lineHeight: '16px',
                              }}
                            >
                              {room.roomDetails?.zones?.join(' / ')}
                            </Typography>
                          </Box>
                        </Box>
                      )}

                      {/* Pricing Section */}
                      <Box sx={{ mb: { xs: 2, md: 0 } }}>
                        <Typography
                          variant='body2'
                          color='text.secondary'
                          sx={{
                            fontWeight: 600,
                            mb: '6px',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px',
                            fontSize: { xs: '10px', md: '12px' },
                          }}
                        >
                          Book by the Hours
                        </Typography>

                        <List sx={{ p: 0 }}>
                          {(room.roomDetails?.prices || []).map((price) => (
                            <ListItem key={price.hours} sx={{ px: 0, py: 0 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                <AccessTime
                                  sx={{
                                    color: 'gray.500',
                                    fontSize: { xs: '1rem', md: '1.25rem' },
                                    marginBottom: '1px',
                                  }}
                                />
                              </ListItemIcon>
                              <ListItemText
                                sx={{
                                  margin: 0,
                                  '& .MuiListItemText-primary': {
                                    margin: 0,
                                  },
                                }}
                                primary={
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      gap: { xs: 0.5, md: 1 },
                                      margin: 0,
                                      flexWrap: { xs: 'wrap', sm: 'nowrap' },
                                    }}
                                  >
                                    <Typography
                                      variant='body1'
                                      sx={{
                                        fontWeight: 500,
                                        fontSize: {
                                          xs: '14px',
                                          sm: '16px',
                                          md: '20px',
                                        },
                                        color: 'text.primary',
                                        minWidth: { xs: '60px', sm: 'auto' },
                                      }}
                                    >
                                      {price.hours} Hours
                                    </Typography>
                                    <Typography
                                      variant='body1'
                                      sx={{
                                        fontSize: {
                                          xs: '16px',
                                          sm: '20px',
                                          md: '24px',
                                        },
                                        color: theme.palette.primary.main,
                                        fontWeight: 600,
                                      }}
                                    >
                                      RM{price.price}
                                    </Typography>
                                  </Box>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Box>

                      {/* Book Button - Mobile only */}
                      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
                        <BookButton
                          variant='outlined'
                          onClick={handleOpenCheckAvailabilityModal}
                          sx={{
                            width: '100%',
                            mt: 2,
                          }}
                        >
                          BOOK
                        </BookButton>
                      </Box>
                    </CardContent>
                  </Grid>
                </Grid>
              </RoomCard>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default RoomOptions;
