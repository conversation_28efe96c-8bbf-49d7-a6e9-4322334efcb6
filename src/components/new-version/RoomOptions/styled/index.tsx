import { But<PERSON>, <PERSON> } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components
export const RoomCard = styled(Card)(({ theme }) => ({
  maxWidth: 912,
  margin: '0 auto',
  borderRadius: 16,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.15)',
  },
}));

export const BookButton = styled(Button)(({ theme }) => ({
  border: `1px solid #011589`,
  color: '#011589',
  backgroundColor: 'transparent',
  fontWeight: 600,
  borderRadius: '4px',
  px: 3,
  py: 1,
  '&:hover': {
    backgroundColor: '#011589',
    color: 'white',
  },
}));
