'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Navigation } from 'swiper/modules';
import { Box, Typography, Button, Container, IconButton } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { ReviewData } from '@/actions/getReviews/types';
import ReviewCard from '../../ReviewCard';
import { ReviewCardSkeleton } from '../../ReviewCard/components/ReviewCardSkeleton';

// CSS to ensure Swiper slides are properly constrained
const swiperStyles = `
  .reviews-swiper .swiper-slide {
    width: 400px !important;
    max-width: 400px !important;
    flex-shrink: 0 !important;
    height: auto !important;
    margin-right: 0 !important;
  }
  
  @media (max-width: 600px) {
    .reviews-swiper .swiper-slide {
      width: 340px !important;
      max-width: 340px !important;
      height: 300px !important;
    }
  }
  
  .reviews-swiper .swiper-wrapper {
    display: flex;
    align-items: stretch;
    gap: 30px;
  }
  
  .reviews-swiper {
    width: 100% !important;
    overflow: hidden !important;
  }
  
  .reviews-swiper .swiper-container {
    overflow: hidden !important;
  }
`;

interface ReviewsSectionProps {
  reviewsData: ReviewData[];
}

const ReviewsSection = ({ reviewsData }: ReviewsSectionProps) => {
  const isLoading = false;


  return (
    <>
      <style>{swiperStyles}</style>
      <Box
        sx={{
          width: '100%',
          minHeight: 500,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
        }}
      >
        {/* Left: Heading and Nav (Desktop) / Top (Mobile) */}
        <Box
          sx={{
            width: { xs: '100%', md: '40%' },
            bgcolor: 'primary.main',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            px: 4,
            py: 8,
            color: 'white',
            position: 'relative',
          }}
        >
          <Box>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 600,
                mb: 1,
                letterSpacing: '0.1em',
                display: 'block',
                fontSize: '15px',
                opacity: isLoading ? 0.7 : 1,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              OUR REVIEWS AND RECOGNITION
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '28px', md: '32px' },
                fontWeight: 600,
                mb: 2,
                lineHeight: 1.2,
                opacity: isLoading ? 0.7 : 1,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              Rest at the KLIA airport with a peace of mind
            </Typography>
            <Typography
              sx={{
                mb: 4,
                opacity: isLoading ? 0.6 : 0.9,
                fontSize: { xs: '16px', md: '16px' },
                fontWeight: 400,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              Trust not just our words, but the travellers from around the world
              who stayed with us.
            </Typography>
          </Box>
          {/* Desktop navigation buttons */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 1.5, mt: 2 }}>
            <IconButton
              className='custom-swiper-prev'
              disabled={isLoading}
              sx={{
                width: 48,
                height: 48,
                borderRadius: 1.5,
                bgcolor: 'white',
                color: '#011589',
                fontSize: '1.25rem',
                '&:hover': {
                  bgcolor: 'white',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'rgba(1, 21, 137, 0.5)',
                },
              }}
              aria-label='Previous Review'
            >
              <ChevronLeftIcon />
            </IconButton>
            <IconButton
              className='custom-swiper-next'
              disabled={isLoading}
              sx={{
                width: 48,
                height: 48,
                borderRadius: 1.5,
                bgcolor: 'white',
                color: '#011589',
                fontSize: '1.25rem',
                '&:hover': {
                  bgcolor: 'white',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'rgba(1, 21, 137, 0.5)',
                },
              }}
              aria-label='Next Review'
            >
              <ChevronRightIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Right: Swiper Carousel (Desktop) / Bottom (Mobile) */}
        <Box
          sx={{
            width: { xs: '100%', md: '60%' },
            bgcolor: '#e5e8f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'visible',
            minHeight: 500,
            px: 2,
            py: 4,
            // backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        >
          <Container maxWidth='xl' sx={{ px: 2, py: 2 }}>
            <Swiper
              modules={[Pagination, Navigation]}
              slidesPerView={1}
              spaceBetween={30}
              centeredSlides={false}
              allowTouchMove={true}
              grabCursor={true}
              navigation={{
                prevEl: '.custom-swiper-prev',
                nextEl: '.custom-swiper-next',
              }}
              breakpoints={{
                768: {
                  slidesPerView: 1,
                  spaceBetween: 30,
                  centeredSlides: false,
                },
                1024: {
                  slidesPerView: 2,
                  spaceBetween: 30,
                  centeredSlides: false,
                },
                1280: {
                  slidesPerView: 2,
                  spaceBetween: 30,
                  centeredSlides: false,
                },
              }}
              style={
                {
                  width: '100%',
                  height: 'auto',
                } as React.CSSProperties
              }
              className='reviews-swiper'
            >
              {isLoading
                ? // Show skeleton loading cards
                  Array.from({ length: 2 }).map((_, index) => (
                    <SwiperSlide
                      key={`skeleton-${index}`}
                      style={{
                        height: 'auto',
                        display: 'flex',
                        flexShrink: 0,
                      }}
                    >
                      <ReviewCardSkeleton index={index} />
                    </SwiperSlide>
                  ))
                : // Show actual reviews with smooth fade-in
                  reviewsData.map((review, index) => (
                    <SwiperSlide
                      key={index}
                      style={{
                        height: 'auto',
                        display: 'flex',
                        flexShrink: 0,
                      }}
                    >
                      <ReviewCard
                        review={review}
                        platform={review.platform}
                        platformUrl={review.platformUrl}
                        reviewPageUrl={review?.reviewPageUrl}
                      />
                    </SwiperSlide>
                  ))}
            </Swiper>

            {/* Mobile navigation buttons */}
            <Box
              sx={{
                display: { xs: 'flex', md: 'none' },
                justifyContent: 'center',
                gap: 1.5,
                mt: 4,
              }}
            >
              <IconButton
                className='custom-swiper-prev'
                disabled={isLoading}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1.5,
                  bgcolor: '#011589',
                  color: 'white',
                  fontSize: '1.25rem',
                  '&:hover': {
                    bgcolor: '#011589',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(1, 21, 137, 0.5)',
                    color: 'rgba(255, 255, 255, 0.5)',
                  },
                }}
                aria-label='Previous Review'
              >
                <ChevronLeftIcon />
              </IconButton>
              <IconButton
                className='custom-swiper-next'
                disabled={isLoading}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1.5,
                  bgcolor: '#011589',
                  color: 'white',
                  fontSize: '1.25rem',
                  '&:hover': {
                    bgcolor: '#011589',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(1, 21, 137, 0.5)',
                    color: 'rgba(255, 255, 255, 0.5)',
                  },
                }}
                aria-label='Next Review'
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>
          </Container>
        </Box>
      </Box>
    </>
  );
};

export default ReviewsSection;
