'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import { Box, Typography, Container } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import 'swiper/css';
import 'swiper/css/navigation';
import { ReviewData } from '@/actions/getReviews/types';
import { NavigationButton } from './styled';
import ReviewCard, { UnloadedReviewCard } from '../ReviewCard';
import { ReviewCardSkeleton } from '../ReviewCard/components/ReviewCardSkeleton';

const swiperStyles = `
  .guest-reviews-swiper .swiper-slide {
    width: 400px !important;
    max-width: 400px !important;
    flex-shrink: 0 !important;
    height: 340px !important;
    margin-right: 0 !important;
    
  }
  
  @media (max-width: 600px) {
    .guest-reviews-swiper .swiper-slide {
      width: 340px !important;
      max-width: 340px !important;
      height: 300px !important;
    }
  }

  .guest-reviews-swiper .swiper-slide .guest-reviews-swiper-slide, .guest-reviews-swiper .swiper-slide>div {
    height: 100% !important;
    width: 100% !important;
  }
  
  .guest-reviews-swiper .swiper-wrapper {
    display: flex;
    align-items: stretch;
    gap: 30px;
  }
  
  .guest-reviews-swiper {
    width: 100% !important;
    overflow: hidden !important;
  }
  
  .guest-reviews-swiper .swiper-container {
    overflow: hidden !important;
  }
`;

interface GuestReviewsProps {
  reviewsData: ReviewData[];
}

const GuestReviews = ({ reviewsData }: GuestReviewsProps) => {
  // Since we're getting data from server, we don't have loading states
  const isLoading = false;

  return (
    <Box
      sx={{
        bgcolor: '#E5E8F6',
        py: { xs: 6, md: 8 },
        position: 'relative',
      }}
    >
      <style>{swiperStyles}</style>
      <Container maxWidth='xl'>
        {/* Header Section */}
        <Box
          sx={{
            textAlign: 'center',
            mb: { xs: 4, md: 6 },
            position: 'relative',
          }}
        >
          <Typography
            variant='h2'
            sx={{
              fontSize: { xs: '2rem', md: '3rem' },
              fontWeight: 600,
              color: '#1A1A1A',
              position: 'relative',
              zIndex: 1,
              bgcolor: '#E5E8F6',
              px: 2,
              opacity: isLoading ? 0.7 : 1,
              transition: 'opacity 0.6s ease-in-out',
            }}
          >
            Guest Reviews
          </Typography>
        </Box>

        {/* Reviews Carousel */}
        <Box
          sx={{
            position: 'relative',
            px: { xs: 2, md: 0 },
            overflow: 'hidden',
            '& .swiper-slide': {
              width: 'auto',
              height: 'auto',
            },
          }}
        >
          <Swiper
            modules={[Navigation]}
            slidesPerView={1}
            spaceBetween={24}
            navigation={{
              prevEl: '.guest-reviews-prev',
              nextEl: '.guest-reviews-next',
            }}
            breakpoints={{
              768: {
                slidesPerView: 2,
                spaceBetween: 32,
              },
              890: {
                slidesPerView: 2,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 48,
              },
              1025: {
                slidesPerView: 3,
                spaceBetween: 52,
              },
              1100: {
                slidesPerView: 3,
                spaceBetween: 56,
              },
              1200: {
                slidesPerView: 3,
                spaceBetween: 60,
              },
              1248: {
                slidesPerView: 3,
                spaceBetween: 64,
              },
              1280: {
                slidesPerView: 3,
                spaceBetween: 68,
              },
            }}
            style={
              {
                width: '100%',
                height: 'auto',
                '--swiper-slide-height': 'auto',
                padding: '0 12px',
                '--swiper-slide-width': 'auto',
              } as React.CSSProperties
            }
            className='guest-reviews-swiper'
          >
            {isLoading ? (
              // Show skeleton loading cards
              Array.from({ length: 3 }).map((_, index) => (
                <SwiperSlide key={`skeleton-${index}`}>
                  <ReviewCardSkeleton index={index} />
                </SwiperSlide>
              ))
            ) : (
              reviewsData.map((review, index) => (
                <SwiperSlide key={index} className='guest-reviews-swiper-slide'>
                  <ReviewCard
                    review={review}
                    platform={review.platform}
                    platformUrl={review.platformUrl}
                    reviewPageUrl={review?.reviewPageUrl}
                  />
                </SwiperSlide>
              ))
            )}
          </Swiper>

          {/* Navigation Buttons */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
              mt: 4,
            }}
          >
            <NavigationButton
              className='guest-reviews-prev'
              aria-label='Previous Review'
              disabled={isLoading}
            >
              <ChevronLeftIcon />
            </NavigationButton>
            <NavigationButton
              className='guest-reviews-next'
              aria-label='Next Review'
              disabled={isLoading}
            >
              <ChevronRightIcon />
            </NavigationButton>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default GuestReviews;
