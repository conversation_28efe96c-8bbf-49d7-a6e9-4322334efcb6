import React, { useState } from 'react';
import {
  Box,
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Divider,
  Button,
  MenuItem,
  Select,
  FormControl,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { DatePicker, ArrowDropDownIcon } from '@mui/x-date-pickers';
import { Controller, useForm } from 'react-hook-form';
import { getCompatibleTimes } from '@/components/booking/constant/TimeSelection';
import { DURATIONS } from '@/constant/time-selection';
import {
  calculateCheckoutTime,
  formatCheckoutDisplay,
  handleChangeDate,
} from '@/utils/time-selection';
import { useRouter } from 'next/navigation';
import { Outlet } from '@/actions/getOutlets/types';

interface CheckAvailabilityModalProps {
  open: boolean;
  onClose: () => void;
  outlet?: Outlet;
}

const CheckAvailabilityModal: React.FC<CheckAvailabilityModalProps> = ({
  open,
  onClose,
  outlet,
}) => {
  const router = useRouter();
  const currentDateTime = new Date();
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const { control, handleSubmit, watch } = useForm<{
    date: Date | null;
    checkInTime: string;
    duration: number | string;
  }>({
    defaultValues: {
      date: null,
      checkInTime: '',
      duration: '',
    },
  });

  const selectedLotId = outlet?.lotId;
  const selectedDate = watch('date');
  const selectedCheckInTime = watch('checkInTime');
  const selectedDuration = watch('duration');

  const checkoutTime = calculateCheckoutTime({
    date: selectedDate,
    checkinTime: selectedCheckInTime,
    duration: Number(selectedDuration),
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const onSubmit = async (data: {
    date: Date | null;
    checkInTime: string;
    duration: number | string;
  }) => {
    try {
      console.log('data', data);

      const lotId = Number(selectedLotId);

      if (!lotId) {
        console.error('Selected outlet not found');
        return;
      }

      // Convert date to Unix timestamp (seconds since epoch)
      if (!data.date) {
        console.error('Date is required');
        return;
      }
      setIsSubmitting(true);
      const checkInDatetime = Math.floor(data.date.getTime() / 1000);

      // Store the form data in localStorage for the booking page
      const formData = {
        lotId: lotId,
        date: data.date.toISOString(),
        checkInTime: data.checkInTime,
        duration: data.duration,
        checkInDatetime: checkInDatetime,
      };

      localStorage.setItem('heroFormData', JSON.stringify(formData));

      // Redirect to the booking page with query parameters
      const queryParams = new URLSearchParams({
        lotId: lotId.toString(),
        date: data.date.toISOString(),
        checkInTime: data.checkInTime,
        duration: data.duration.toString(),
        checkInDatetime: checkInDatetime.toString(),
      });

      router.push(`/new-booking?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error fetching data:', error);
      setIsSubmitting(false);
      // Handle error here (show toast, error message, etc.)
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          maxWidth: '500px',
          width: '100%',
          margin: '16px',
        },
      }}
    >
      <DialogContent sx={{ padding: '24px', position: 'relative' }}>
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: '16px',
            top: '16px',
            color: '#666',
            zIndex: 1,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>

        {/* Title */}
        <Typography
          variant='h4'
          sx={{
            textAlign: 'center',
            color: '#011589',
            fontWeight: 'bold',
            mb: 2,
            fontSize: '24px',
          }}
        >
          Check Availability
        </Typography>

        {/* Divider */}
        <Divider sx={{ mb: 3 }} />
        <form onSubmit={handleSubmit(onSubmit)}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: { xs: 1.5, md: 2, lg: 3 },
            }}
          >
            {/* DATE */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant='caption'
                sx={{
                  fontWeight: 600,
                  mb: 0.5,
                  color: 'text.secondary',
                }}
              >
                DATE
              </Typography>
              <FormControl fullWidth>
                <Controller
                  name='date'
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <DatePicker
                      open={openDatePicker}
                      onClose={() => setOpenDatePicker(false)}
                      slotProps={{
                        textField: {
                          onClick: () => setOpenDatePicker(true),
                        },
                      }}
                      value={field.value}
                      onChange={(newValue) => {
                        const date = handleChangeDate(newValue);
                        field.onChange(date || null);
                      }}
                      format='dd/MM/yyyy'
                      slots={{
                        openPickerIcon: ArrowDropDownIcon,
                      }}
                      minDate={currentDateTime}
                      sx={{
                        fontWeight: 600,
                        maxHeight: '42px',
                        '& .MuiInputBase-root': {
                          maxHeight: '42px',
                        },
                      }}
                    />
                  )}
                />
              </FormControl>
            </Box>

            {/* CHECK-IN TIME */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant='caption'
                sx={{
                  fontWeight: 600,
                  mb: 0.5,
                  color: 'text.secondary',
                }}
              >
                CHECK-IN TIME
              </Typography>
              <FormControl fullWidth>
                <Controller
                  name='checkInTime'
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      value={field.value || ''}
                      sx={{
                        '& .MuiSelect-select': {
                          py: { xs: 1.25, md: 1.5 },
                          px: { xs: 1.5, md: 2 },
                          fontSize: { xs: '0.875rem', md: '1rem' },
                        },
                        maxHeight: '42px',
                      }}
                    >
                      <MenuItem value='' disabled>
                        {!selectedDate
                          ? 'Select date first'
                          : 'Select check-in time'}
                      </MenuItem>
                      {selectedDate &&
                        getCompatibleTimes(selectedDate).map((time) => (
                          <MenuItem key={time} value={time}>
                            {time}
                          </MenuItem>
                        ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Box>

            {/* STAY DURATION */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant='caption'
                sx={{
                  fontWeight: 600,
                  mb: 0.5,
                  color: 'text.secondary',
                }}
              >
                STAY DURATION
              </Typography>
              <FormControl fullWidth>
                <Controller
                  name='duration'
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      disabled={!selectedLotId}
                      value={field.value || ''}
                      sx={{
                        '& .MuiSelect-select': {
                          py: { xs: 1.25, md: 1.5 },
                          px: { xs: 1.5, md: 2 },
                          fontSize: { xs: '0.875rem', md: '1rem' },
                          color: !selectedLotId ? 'text.disabled' : undefined,
                        },
                        '& .MuiOutlinedInput-root': {
                          bgcolor: !selectedLotId ? 'grey.100' : undefined,
                        },
                        maxHeight: '42px',
                      }}
                    >
                      <MenuItem value='' disabled>
                        {!selectedLotId
                          ? 'Please select the outlet first'
                          : 'Select stay duration'}
                      </MenuItem>
                      {DURATIONS.map((time) => (
                        <MenuItem key={time.value} value={time.value}>
                          {time.label}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
                {!selectedLotId && (
                  <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
                    Please select the outlet first
                  </Typography>
                )}
                {checkoutTime && (
                  <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
                    <span style={{ color: '#989CA4' }}>Check-out at</span>{' '}
                    <span style={{ color: '#1A1A1A', fontWeight: 600 }}>
                      {formatCheckoutDisplay(checkoutTime)}
                    </span>
                  </Typography>
                )}
              </FormControl>
            </Box>

            {/* CHECK AVAILABILITY BUTTON */}
            <Box
              sx={{
                flex: 1,
                paddingTop: '24px',
              }}
            >
              <Button
                variant='contained'
                type='submit'
                disabled={isSubmitting}
                sx={{
                  bgcolor: 'primary.main',
                  color: 'white',
                  width: '100%',
                  fontWeight: 'bold',
                  px: { xs: 3, md: 4 },
                  py: { xs: 1.25, md: 1.5 },
                  borderRadius: 2,
                  boxShadow: 2,
                  '&:hover': { bgcolor: 'primary.dark' },
                  fontSize: '14px',
                  whiteSpace: 'nowrap',
                  maxHeight: '42px',
                }}
              >
                {isSubmitting ? 'CHECKING...' : 'CHECK AVAILABILITY'}
              </Button>
            </Box>
          </Box>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CheckAvailabilityModal;
