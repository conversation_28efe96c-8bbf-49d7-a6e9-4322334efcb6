'use client';

import React from 'react';
import { ThemeProvider, CssBaseline, Box } from '@mui/material';
import Navbar from '@/components/new-version/Navbar';
import Footer from '@/components/new-version/Footer';
import { theme } from './theme';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
function Layout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
      <CssBaseline />
      <Box
        sx={{
          bgcolor: 'background.default',
          minHeight: '100vh',
          width: '100%',
          maxWidth: '100vw',
          overflowX: 'hidden',
          position: 'relative',
          paddingTop: '76px',
        }}
      >
        <Navbar />
        <Box
          sx={{
            width: '100%',
            maxWidth: '100vw',
            overflowX: 'hidden',
          }}
        >
          {children}
        </Box>
        <Footer />
      </Box>
      </LocalizationProvider>
    </ThemeProvider>
  );
}

export default Layout;
