import { createTheme } from '@mui/material';

// Create a custom theme
export const theme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      xxs: 380, // ✅ new custom breakpoint for small devices
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    } as any,
  },
  palette: {
    primary: {
      main: '#011589',
      light: '#1a2b9a',
      dark: '#000f6b',
    },
    secondary: {
      main: '#00E0C6',
      light: '#33e6d0',
      dark: '#009c8a',
    },
    background: {
      default: '#ffffff',
    },
    CtColorScheme: {
      primary: '#77FBDE',
      secondary: '#001689',
      neonLogo: '#77FBDE',
      neon200: '#16FFDD',
      neon300: '#00E6C5',
      neon400: '#00DEBE',
      neon500: '#00C9AB',
      blue800: '#001689',
      yellow500: '#FFDF08',
      white: '#FFFFFF',
      grey100: '#E2E6E0',
      grey300: '#C8C8C8',
      grey400: '#87888B',
      black800: '#1A1A1a',
      black900: '#00000A',
      pink300: '#FA25A8',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 800,
    },
    h2: {
      fontWeight: 700,
    },
    h3: {
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 600,
        },
      },
    },
  },
});
