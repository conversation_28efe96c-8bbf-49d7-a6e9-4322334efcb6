// Helper function to calculate checkInDatetime from date and time
export const calculateCheckInDatetime = (
  date: Date | null,
  checkInTime: string
): number => {
  if (!date || !checkInTime) return 0;

  try {
    const [hours, minutes] = checkInTime.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return 0;

    const combinedDate = new Date(date);
    combinedDate.setHours(hours, minutes, 0, 0);
    const timestamp = Math.floor(combinedDate.getTime() / 1000);

    return timestamp;
  } catch (error) {
    console.error('Error calculating checkInDatetime:', error);
    return 0;
  }
};
