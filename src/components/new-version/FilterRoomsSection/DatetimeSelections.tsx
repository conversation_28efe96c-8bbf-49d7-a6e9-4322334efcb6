import React, { useState, useEffect } from 'react';
import { Box, Typography, FormControl, Select, MenuItem } from '@mui/material';
import { ArrowDropDownIcon, DatePicker } from '@mui/x-date-pickers';
import { DURATIONS } from '../HeroSection/constant';
import { getCompatibleTimes } from '@/components/booking/constant/TimeSelection';
import {
  calculateCheckoutTime,
  formatCheckoutDisplay,
} from '../HeroSection/utils';
import { FilterData } from './types';

const DatetimeSelections = ({
  filters,
  handleDateChange,
  handleCheckInTimeChange,
  handleStayDurationChange,
}: {
  filters: FilterData;
  handleDateChange: (date: Date | null) => void;
  handleCheckInTimeChange: (time: string) => void;
  handleStayDurationChange: (duration: number) => void;
}) => {
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const currentDateTime = new Date();
  const checkoutTime = calculateCheckoutTime({
    date: filters.date,
    checkinTime: filters.checkInTime,
    duration: filters.stayDuration,
  });

  return (
    <>
      {/* Date Selection */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography
          variant='caption'
          sx={{
            fontWeight: 600,
            mb: 1,
            color: '#989CA4',
            display: 'block',
            textTransform: 'uppercase',
            fontSize: '12px',
          }}
        >
          DATE
        </Typography>
        <DatePicker
          open={openDatePicker}
          onClose={() => setOpenDatePicker(false)}
          slotProps={{
            textField: {
              onClick: () => setOpenDatePicker(true),
            },
          }}
          value={filters.date}
          onChange={(newValue) => {
            handleDateChange(newValue);
          }}
          format='dd/MM/yyyy'
          slots={{
            openPickerIcon: ArrowDropDownIcon,
          }}
          minDate={currentDateTime}
          sx={{
            fontWeight: 600,
            maxHeight: '40px',
            width: '100%',
            color: '#0F0E0E',
            '& .MuiInputBase-root': {
              maxHeight: '40px',
              fontSize: '16px',
              border: `1px solid #0F0E0E !important`,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none !important',
            },
          }}
        />
      </Box>

      {/* Check-in Time Selection */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography
          variant='caption'
          sx={{
            fontWeight: 600,
            mb: 1,
            color: '#989CA4',
            display: 'block',
            textTransform: 'uppercase',
            fontSize: '12px',
          }}
        >
          CHECK-IN TIME
        </Typography>
        <FormControl fullWidth size='small'>
          <Select
            value={filters.checkInTime}
            onChange={(e) => handleCheckInTimeChange(e.target.value)}
            sx={{
              '& .MuiSelect-select': {
                py: 1.5,
                px: 2,
                fontSize: '16px',
                color: '#0F0E0E',
              },
              height: '100%',
              maxHeight: '40px',
              border: `1px solid #0F0E0E`,
              borderRadius: '4px',
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
            }}
          >
            {filters.date &&
              getCompatibleTimes(filters.date).map((time) => (
                <MenuItem key={time} value={time}>
                  {time}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      </Box>

      {/* Stay Duration Selection */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography
          variant='caption'
          sx={{
            fontWeight: 600,
            mb: 1,
            color: '#989CA4',
            display: 'block',
            textTransform: 'uppercase',
            fontSize: '12px',
          }}
        >
          STAY DURATION
        </Typography>
        <FormControl fullWidth size='small'>
          <Select
            value={filters.stayDuration}
            onChange={(e) => handleStayDurationChange(e.target.value as number)}
            sx={{
              '& .MuiSelect-select': {
                py: 1.5,
                px: 2,
                fontSize: '16px',
                color: '#0F0E0E',
              },
              height: '100%',
              maxHeight: '40px',
              border: `1px solid #0F0E0E`,
              borderRadius: '4px',
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
            }}
          >
            {DURATIONS.map((duration) => (
              <MenuItem key={duration.value} value={duration.value}>
                {duration.label}
              </MenuItem>
            ))}
          </Select>
          {!filters.lotId && (
            <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
              Please select the outlet first
            </Typography>
          )}
          {checkoutTime && (
            <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
              <span style={{ color: '#989CA4' }}>Check-out at</span>{' '}
              <span style={{ color: '#1A1A1A', fontWeight: 600 }}>
                {formatCheckoutDisplay(checkoutTime)}
              </span>
            </Typography>
          )}
        </FormControl>
      </Box>
    </>
  );
};

export default DatetimeSelections;
