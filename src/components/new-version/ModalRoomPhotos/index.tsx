'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Modal,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Close as CloseIcon,
  Fullscreen as FullscreenIcon,
} from '@mui/icons-material';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Keyboard, Zoom, Thumbs } from 'swiper/modules';
import type { Swiper as SwiperType } from 'swiper';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/zoom';
import 'swiper/css/thumbs';
import { TransformImage } from '@/actions/getRoomImages/types';

// Custom Swiper styles
const swiperStyles = `
  .room-modal-swiper .swiper-pagination {
    bottom: 20px !important;
    z-index: 1001 !important;
  }
  
  .room-modal-swiper .swiper-pagination-bullet {
    background: rgba(255, 255, 255, 0.5) !important;
    opacity: 1 !important;
    width: 12px !important;
    height: 12px !important;
    margin: 0 6px !important;
    transition: all 0.3s ease !important;
  }
  
  .room-modal-swiper .swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.2) !important;
  }
  
  .room-thumbnail-swiper .swiper-slide {
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }
  
  .room-thumbnail-swiper .swiper-slide-thumb-active {
    opacity: 1;
  }
  
  .swiper-zoom-container {
    cursor: grab;
  }
  
  .swiper-zoom-container:active {
    cursor: grabbing;
  }
`;

interface ModalRoomPhotosProps {
  isOpen?: boolean;
  handleClose: () => void;
  images: TransformImage[];
}

const ModalRoomPhotos: React.FC<ModalRoomPhotosProps> = ({
  isOpen,
  handleClose,
  images,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [swiperInstance, setSwiperInstance] = useState<SwiperType | null>(null);
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperType | null>(null);
  const [isImageLoading, setIsImageLoading] = useState(true);
  const modalRef = useRef<HTMLDivElement>(null);

  // Inject custom styles
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = swiperStyles;
    document.head.appendChild(styleElement);

    return () => {
      if (document.head.contains(styleElement)) {
        document.head.removeChild(styleElement);
      }
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          handleClose();
          break;
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        case 'f':
        case 'F':
          toggleFullscreen();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, handleClose]);

  const handleSlideChange = (swiper: SwiperType) => {
    setCurrentImageIndex(swiper.activeIndex);
  };

  const handlePrevious = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiperInstance) {
      swiperInstance.slideNext();
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      modalRef.current?.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  return (
    <Modal
      open={!!isOpen}
      onClose={handleClose}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
      }}
      slotProps={{
        backdrop: {
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.8) !important',
            backdropFilter: 'blur(4px)',
          },
        },
      }}
    >
      <Box
        ref={modalRef}
        sx={{
          position: 'relative',
          width: '95vw',
          height: '95vh',
          maxWidth: '1200px',
          maxHeight: '800px',
          bgcolor: 'transparent',
          borderRadius: 2,
          overflow: 'hidden',
          outline: 'none',
        }}
      >
        {/* Top Control Bar */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '60px',
            background:
              'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 2,
            zIndex: 1000,
          }}
        >
          <Typography
            variant='h6'
            sx={{
              color: 'white',
              fontWeight: 500,
              fontSize: '16px',
            }}
          >
            Room Photos ({currentImageIndex + 1} of {images.length})
          </Typography>

          <Box sx={{ display: 'flex', gap: 1 }}>

            <IconButton
              onClick={toggleFullscreen}
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
              size='small'
            >
              <FullscreenIcon />
            </IconButton>

            <IconButton
              onClick={handleClose}
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
              size='small'
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Main Swiper */}
        {images.length > 0 && (
          <Box sx={{ width: '100%', height: '100%' }}>
            <Swiper
              modules={[Navigation, Pagination, Keyboard, Zoom, Thumbs]}
              spaceBetween={0}
              slidesPerView={1}
              initialSlide={currentImageIndex}
              navigation={{
                enabled: true,
                prevEl: '.room-swiper-button-prev-custom',
                nextEl: '.room-swiper-button-next-custom',
              }}
              pagination={{
                enabled: true,
                clickable: true,
                dynamicBullets: true,
              }}
              keyboard={{
                enabled: true,
                onlyInViewport: false,
              }}
              zoom={{
                maxRatio: 3,
                minRatio: 1,
                toggle: true,
              }}
              thumbs={{
                swiper:
                  thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
              }}
              onSwiper={setSwiperInstance}
              onSlideChange={handleSlideChange}
              style={{
                width: '100%',
                height: '100%',
              }}
              className='room-modal-swiper'
            >
              {images.map((image, index) => (
                <SwiperSlide key={`${image.url}-${index}`}>
                  <Box
                    sx={{
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                    className='swiper-zoom-container'
                  >
                    {isImageLoading && index === currentImageIndex && (
                      <CircularProgress
                        sx={{
                          position: 'absolute',
                          color: 'white',
                        }}
                      />
                    )}
                    <img
                      src={image.url}
                      alt={image.name}
                      style={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain',
                        userSelect: 'none',
                        pointerEvents: 'none',
                      }}
                      onLoad={() => setIsImageLoading(false)}
                      onError={() => setIsImageLoading(false)}
                    />
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Custom Navigation Buttons */}
            <Box
              className='room-swiper-button-prev-custom'
              sx={{
                position: 'absolute',
                left: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1000,
                width: 35,
                height: 50,
                borderRadius: '4px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  transform: 'translateY(-50%) scale(1.1)',
                },
                '&:after': {
                  content: '"❮"',
                  color: 'white',
                  fontSize: '18px',
                  fontWeight: 'bold',
                },
              }}
            />

            <Box
              className='room-swiper-button-next-custom'
              sx={{
                position: 'absolute',
                right: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1000,
                width: 35,
                height: 50,
                borderRadius: '4px',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  transform: 'translateY(-50%) scale(1.1)',
                },
                '&:after': {
                  content: '"❯"',
                  color: 'white',
                  fontSize: '18px',
                  fontWeight: 'bold',
                },
              }}
            />

            {/* Bottom Info Bar */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                background:
                  'linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                px: 3,
                py: 2,
                zIndex: 1000,
              }}
            >
              <Typography
                variant='body2'
                sx={{
                  color: 'white !important',
                  fontSize: '14px !important',
                  fontWeight: 500,
                  backgroundColor: 'rgba(0, 0, 0, 0.6)',
                  px: 2,
                  py: 0.5,
                  borderRadius: 2,
                  backdropFilter: 'blur(4px)',
                }}
              >
                {currentImageIndex + 1} / {images.length}
              </Typography>
            </Box>

            {/* Thumbnail Navigation */}
            {images.length > 1 && (
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 80,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '80%',
                  maxWidth: '600px',
                  zIndex: 1000,
                }}
              >
                <Swiper
                  modules={[Thumbs]}
                  spaceBetween={8}
                  slidesPerView={6}
                  watchSlidesProgress
                  onSwiper={setThumbsSwiper}
                  breakpoints={{
                    320: { slidesPerView: 3 },
                    480: { slidesPerView: 4 },
                    640: { slidesPerView: 5 },
                    768: { slidesPerView: 6 },
                  }}
                  style={{
                    height: '60px',
                  }}
                  className='room-thumbnail-swiper'
                >
                  {images.map((image, index) => (
                    <SwiperSlide key={`thumb-${image.url}-${index}`}>
                      <Box
                        sx={{
                          width: '100%',
                          height: '60px',
                          borderRadius: 1,
                          overflow: 'hidden',
                          border:
                            index === currentImageIndex
                              ? '2px solid white'
                              : '2px solid transparent',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'scale(1.05)',
                          },
                        }}
                        onClick={() => swiperInstance?.slideTo(index)}
                      >
                        <img
                          src={image.formats?.thumbnail || image.url}
                          alt={image.name}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                        />
                      </Box>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Modal>
  );
};

export default ModalRoomPhotos;
