import React from 'react';
import {
  AppBar,
  Toolbar,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import smallLogo from '@/assets/icons/general/LogoPrimary.svg';
import largeLogo from '@/assets/icons/general/Logo-CT.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const Navbar = () => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = React.useState(false);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleDrawerClose = () => {
    setMobileOpen(false);
  };

  const onBookYourStayClick = () => {
    router.push('/book-your-stay');
  };

  return (
    <AppBar
      position='fixed'
      sx={{
        bgcolor: '#011589',
        boxShadow: '0 4px 10px 0 rgba(0,0,0,0.2)',
        zIndex: 1000,
        top: 0,
        left: 0,
        width: '100%',
      }}
    >
      <Toolbar
        sx={{
          maxWidth: '1440px',
          width: '100%',
          mx: 'auto',
          px: { xs: 2 },
          minHeight: { xs: '72px', md: '76px' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 4,
        }}
      >
        {/* Left Section: Hamburger + Navigation */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 3,
            flex: 1,
            justifyContent: 'flex-start',
          }}
        >
          {/* Hamburger Menu */}
          <IconButton
            sx={{
              color: 'white',
              '&:hover': { opacity: 0.8 },
              p: 1,
            }}
            size='medium'
            onClick={handleDrawerToggle}
          >
            <MenuIcon />
          </IconButton>

          {/* Desktop Navigation Items */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              gap: 4,
            }}
          >
            {/* KLIA Terminal 1 */}
            <Link href='/new-version/klia-1/sleep-lounge'>
              <Button
                sx={{
                  color: 'white',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  fontSize: { xs: '0.875rem', md: '14px' },
                  letterSpacing: '0.5px',
                  '&:hover': { opacity: 0.8 },
                  px: 0,
                  py: 1,
                }}
              >
                KLIA TERMINAL 1
              </Button>
            </Link>

            {/* KLIA Terminal 2 with Dropdown */}
            <Button
              sx={{
                color: 'white',
                fontWeight: 600,
                textTransform: 'uppercase',
                fontSize: { xs: '0.875rem', md: '14px' },
                letterSpacing: '0.5px',
                '&:hover': { opacity: 0.8 },
                px: 0,
                py: 1,
              }}
              endIcon={<KeyboardArrowDownIcon sx={{ fontSize: '1rem' }} />}
              onClick={handleMenu}
            >
              KLIA TERMINAL 2
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{ mt: 1 }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
              >
                <Link
                  href='/new-version/klia-2/max'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  MAX
                </Link>
                <Box
                  sx={{
                    backgroundColor: '#00CED1',
                    color: 'black',
                    fontSize: '0.75rem',
                    fontWeight: 'bold',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    textTransform: 'uppercase',
                    lineHeight: 1,
                  }}
                >
                  NEW
                </Box>
              </MenuItem>
              <MenuItem onClick={handleClose}>
                <Link
                  href='/new-version/klia-2/landside'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  Landside
                </Link>
              </MenuItem>
              <MenuItem onClick={handleClose}>
                <Link
                  href='/new-version/klia-2/airside'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  Airside
                </Link>
              </MenuItem>
            </Menu>
          </Box>
        </Box>

        {/* Center: Logo */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
          }}
        >
          {/* Mobile Logo */}
          <Box
            sx={{
              display: { xs: 'flex', md: 'none' },
              width: 120,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href='/new-version' style={{ width: '100%', height: '100%' }}>
              <Image
                src={smallLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>

          {/* Desktop Logo */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              width: 160,
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href='/new-version' style={{ width: '100%', height: '100%' }}>
              <Image
                src={largeLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>
        </Box>

        {/* Right: CTA Button */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'flex-end',
          }}
        >
          {/* Desktop Button */}
          <Button
            variant='contained'
            onClick={onBookYourStayClick}
            sx={{
              display: { xs: 'none', md: 'block' },
              bgcolor: '#21FEDD',
              color: '#0F0E0E',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              px: 4,
              height: '40px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 224, 198, 0.3)',
              '&:hover': {
                bgcolor: '#21FEDD',
                boxShadow: '0 4px 12px rgba(0, 224, 198, 0.4)',
              },
            }}
          >
            BOOK YOUR STAY
          </Button>

          {/* Mobile Button */}
          <Button
            variant='contained'
            onClick={onBookYourStayClick}
            sx={{
              display: { xs: 'block', md: 'none' },
              bgcolor: '#21FEDD',
              color: '#0F0E0E',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              px: 3,
              height: '40px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 224, 198, 0.3)',
              '&:hover': {
                bgcolor: '#21FEDD',
                boxShadow: '0 4px 12px rgba(0, 224, 198, 0.4)',
              },
            }}
          >
            BOOK
          </Button>
        </Box>
      </Toolbar>

      {/* Mobile Navigation Drawer */}
      <Drawer
        variant='temporary'
        anchor='left'
        open={mobileOpen}
        onClose={handleDrawerClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 280,
            bgcolor: '#011589',
            color: 'white',
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          {/* Logo in Drawer */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
              mb: 4,
            }}
          >
            <Box sx={{ width: 32, height: 32 }}>
              <img
                src={smallLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                }}
              />
            </Box>
            <Box
              sx={{
                color: '#00E0C6',
                fontWeight: 700,
                fontSize: '1.125rem',
                letterSpacing: '0.5px',
              }}
            >
              CapsuleTransit
            </Box>
          </Box>

          <Divider sx={{ bgcolor: 'rgba(255,255,255,0.2)', mb: 3 }} />

          {/* Navigation Items */}
          <List sx={{ p: 0 }}>
            <ListItem
              sx={{
                px: 0,
                py: 1.5,
                '&:hover': { bgcolor: 'rgba(0,224,198,0.1)' },
              }}
            >
              <ListItemText
                primary='KLIA TERMINAL 1'
                sx={{
                  '& .MuiListItemText-primary': {
                    color: 'white',
                    fontWeight: 600,
                    textTransform: 'uppercase',
                    fontSize: '0.875rem',
                    letterSpacing: '0.5px',
                  },
                }}
              />
            </ListItem>

            <ListItem
              sx={{
                px: 0,
                py: 1.5,
                '&:hover': { bgcolor: 'rgba(0,224,198,0.1)' },
              }}
            >
              <ListItemText
                primary='KLIA TERMINAL 2'
                sx={{
                  '& .MuiListItemText-primary': {
                    color: 'white',
                    fontWeight: 600,
                    textTransform: 'uppercase',
                    fontSize: '0.875rem',
                    letterSpacing: '0.5px',
                  },
                }}
              />
            </ListItem>
          </List>

          <Divider sx={{ bgcolor: 'rgba(255,255,255,0.2)', my: 3 }} />

          {/* CTA Button in Drawer */}
          <Button
            variant='contained'
            fullWidth
            onClick={onBookYourStayClick}
            sx={{
              bgcolor: '#21FEDD',
              color: '#0F0E0E',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              py: 1.5,
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 224, 198, 0.3)',
              '&:hover': {
                bgcolor: '#21FEDD',
                boxShadow: '0 4px 12px rgba(0, 224, 198, 0.4)',
              },
            }}
          >
            BOOK YOUR STAY
          </Button>
        </Box>
      </Drawer>
    </AppBar>
  );
};

export default Navbar;
