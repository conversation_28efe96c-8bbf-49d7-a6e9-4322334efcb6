import React from 'react';
import {
  Box,
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Divider,
  Link,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { MobileMapArea, DesktopMapArea } from './MapArea';

interface RestrictedAreaModalProps {
  open: boolean;
  onClose: () => void;
  onOpenPublicAreaModal: () => void;
}

const RestrictedAreaModal: React.FC<RestrictedAreaModalProps> = ({
  open,
  onClose,
  onOpenPublicAreaModal,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery('(max-width:768px)');

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='lg'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          maxWidth: '1200px',
          width: '100%',
          margin: '16px',
          maxHeight: '90vh',
          overflow: 'hidden',
        },
      }}
    >
      <DialogContent
        sx={{ padding: 0, position: 'relative' }}
      >
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: '16px',
            top: '16px',
            color: '#666',
            zIndex: 1,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
        <Box
          sx={{ 
            display: 'flex', 
            flexDirection: isMobile ? 'column' : 'row',
            maxHeight: 'calc(90vh - 32px)',
            overflow: 'auto'
          }}
        >
          {/* Left Side - Content */}
          <Box
            sx={{
              flex: 1,
              padding: '32px',
              backgroundColor: 'white',
              position: 'relative',
              minHeight: isMobile ? 'auto' : '500px',
            }}
          >
            {/* Title */}
            <Typography
              variant='h4'
              sx={{
                color: '#223a8a',
                fontSize: '1.75rem',
                fontWeight: 700,
                marginBottom: '16px',
                lineHeight: 1.2,
              }}
            >
              <Typography
                variant='body1'
                sx={{
                  color: '#1A1A1A',
                  fontSize: isMobile ? '24px' : '32px',
                  lineHeight: 1,
                  fontWeight: 500,
                }}
              >
                What is the{' '}
              </Typography>
              <Typography
                style={{ 
                  color: '#011589', 
                  fontSize: isMobile ? '24px' : '32px', 
                  fontWeight: 500 
                }}
              >
                Restricted Area?
              </Typography>
            </Typography>

            {/* Divider */}
            <Divider
              sx={{
                marginBottom: '24px',
                borderColor: '#E0E0E0',
                borderWidth: '1px',
              }}
            />

            {/* Description */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <Typography
                variant='body1'
                sx={{
                  color: '#333',
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                The restricted area is located{' '}
                <strong>after immigration and security checks</strong>.
              </Typography>

              <Typography
                variant='body1'
                sx={{
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                Public visitors cannot access this area without a valid boarding
                pass for a same-day departing flight.
              </Typography>

              <Typography
                variant='body1'
                sx={{
                  fontSize: '16px',
                  lineHeight: 1.6,
                }}
              >
                Transiting passengers with a connecting flight at KLIA Terminal
                2 can usually remain within the restricted area without exiting
                or clearing immigration, making it convenient to use our Airside
                outlet.
              </Typography>
            </Box>
          </Box>

          {/* Right Side - Map Image and Link */}
          <Box
            sx={{
              flex: 1,
              padding: '32px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: isMobile ? 'auto' : '500px',
            }}
          >
            {/* Map Component */}
            <Box
              sx={{
                width: '100%',
                maxWidth: '600px',
                minHeight: '300px',
                position: 'relative',
                marginBottom: '16px',
                backgroundColor: 'transparent',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {isMobile ? <MobileMapArea /> : <DesktopMapArea />}
            </Box>

            {/* Link below map */}
            <Link
              onClick={onOpenPublicAreaModal}
              sx={{
                color: '#223a8a',
                textDecoration: 'none',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              What is the Public Area? →
            </Link>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default RestrictedAreaModal;
