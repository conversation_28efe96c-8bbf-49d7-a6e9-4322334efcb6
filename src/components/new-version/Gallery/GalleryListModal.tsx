'use client';

import React from 'react';
import { Box, Grid, Modal, IconButton, Tabs, Tab } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

const GalleryListModal = ({
  showGalleryModal,
  setShowGalleryModal,
  imageCategories,
  filteredImages,
  selectedTab,
  handleTabChange,
}: {
  showGalleryModal: boolean;
  setShowGalleryModal: (show: boolean) => void;
  imageCategories: any[];
  filteredImages: any[];
  selectedTab: number;
  handleTabChange: (event: React.SyntheticEvent, newValue: number) => void;
}) => {
  return (
    <Modal
      open={showGalleryModal}
      onClose={() => setShowGalleryModal(false)}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Box
        sx={{
          position: 'relative',
          width: { xs: '95vw', sm: '90vw' },
          maxWidth: 1376,
          height: { xs: '95vh', sm: '90vh' },
          bgcolor: 'background.paper',
          borderRadius: { xs: 1, sm: 2 },
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header with tabs */}
        <Box
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.paper',
            minHeight: 48,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              px: { xs: 1, sm: 2 },
              py: 1,
              gap: 1,
            }}
          >
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              variant='scrollable'
              scrollButtons='auto'
              allowScrollButtonsMobile
              sx={{
                flexGrow: 1,
                minWidth: 0, // ✅ Let it shrink properly on mobile
                maxWidth: 'calc(100% - 48px)', // Still account for close button
                '& .MuiTab-root': {
                  minWidth: { xs: 'auto', sm: 120 },
                  px: { xs: 1, sm: 2 },
                  py: 1,
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  fontWeight: 500,
                  color: '#3B3F47',
                  whiteSpace: 'nowrap',
                  '&.Mui-selected': {
                    color: '#011589',
                    fontWeight: 600,
                  },
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#011589',
                  height: 3,
                },
                '& .MuiTabs-scrollButtons': {
                  width: 24,
                  flexShrink: 0,
                  color: 'text.secondary',
                  '&.Mui-disabled': { opacity: 0.3 },
                  display: { xs: 'flex', sm: 'none' }, // ✅ show small scroll buttons only on mobile
                },
                '& .MuiTabs-scroller': {
                  overflowX: 'auto !important', // ✅ enforce horizontal scroll
                  scrollbarWidth: 'none', // Firefox
                  '&::-webkit-scrollbar': { display: 'none' }, // Chrome/Safari/Edge
                },
                '& .MuiTabs-flexContainer': {
                  gap: { xs: 0.5, sm: 1 },
                },
              }}
            >
              {imageCategories.map((category) => (
                <Tab
                  key={category.name}
                  label={`${category.label} (${category.count})`}
                  sx={{ textTransform: 'none', minHeight: 'auto', py: 1 }}
                />
              ))}
            </Tabs>

            <IconButton
              onClick={() => setShowGalleryModal(false)}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'text.primary',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Image Grid */}
        <Box sx={{ flex: 1, overflow: 'auto', p: { xs: 1, sm: 2 } }}>
          <Grid
            container
            spacing={{ xs: 1, sm: 2, md: 3 }}
            sx={{ maxWidth: 1376, mx: 'auto' }}
          >
            {filteredImages.map((image, index) => (
              <Grid item key={image.imageData?.id} xs={12} sm={6} md={4} lg={3}>
                <Box
                  sx={{
                    position: 'relative',
                    width: '100%',
                    height: { xs: 265, md: 330 },
                    cursor: 'pointer',
                    overflow: 'hidden',
                    borderRadius: 1,
                    '&:hover .image-overlay': {
                      opacity: 1,
                    },
                  }}
                  onClick={() => {
                    // setSelectedImage(image);
                    // setCurrentImageIndex(index);
                  }}
                >
                  <img
                    src={image.formats?.small || image.url}
                    alt={image.name}
                    style={{
                      objectFit: 'cover',
                      width: '100%',
                      height: '100%',
                    }}
                    sizes='(max-width: 600px) 100vw, (max-width: 960px) 50vw, (max-width: 1280px) 33vw, 25vw'
                  />
                  <Box
                    className='image-overlay'
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      opacity: 0,
                      transition: 'opacity 0.3s ease',
                    }}
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Modal>
  );
};

export default GalleryListModal;
