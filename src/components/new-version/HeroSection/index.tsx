'use client';

import React, { useState } from 'react';
import { SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  Select,
  MenuItem,
  Button,
  Divider,
} from '@mui/material';
import { ArrowDropDownIcon, DatePicker } from '@mui/x-date-pickers';
import { useForm, Controller } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { StyledSwiper } from './components/StyledSwiper';
import { getCompatibleTimes } from '@/components/booking/constant/TimeSelection';
import {
  calculateCheckoutTime,
  formatCheckoutDisplay,
  handleChangeDate,
} from './utils';
import { DURATIONS } from '@/constant/time-selection';
import { Outlet } from '@/actions/getOutlets/types';

const HeroSection = ({
  outlets = [],
  lotId,
}: {
  outlets: Outlet[];
  lotId?: number;
}) => {
  const router = useRouter();
  const currentDateTime = new Date();
  const [openDatePicker, setOpenDatePicker] = useState(false);

  const { control, handleSubmit, watch } = useForm<{
    lotId: string;
    date: Date | null;
    checkInTime: string;
    duration: number | string;
  }>({
    defaultValues: {
      lotId: lotId?.toString() || '',
      date: null,
      checkInTime: '',
      duration: '',
    },
  });

  const selectedLotId = watch('lotId');
  const selectedDate = watch('date');
  const selectedCheckInTime = watch('checkInTime');
  const selectedDuration = watch('duration');

  // Check if all fields are filled
  const isAllFieldsFilled =
    selectedLotId && selectedDate && selectedCheckInTime && selectedDuration;

  const checkoutTime = calculateCheckoutTime({
    date: selectedDate,
    checkinTime: selectedCheckInTime,
    duration: Number(selectedDuration),
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const onSubmit = async (data: {
    lotId: string;
    date: Date | null;
    checkInTime: string;
    duration: number | string;
  }) => {
    try {
      console.log('data', data);

      const lotId = Number(data.lotId);

      if (!lotId) {
        console.error('Selected outlet not found');
        return;
      }

      // Convert date to Unix timestamp (seconds since epoch)
      if (!data.date) {
        console.error('Date is required');
        return;
      }
      setIsSubmitting(true);
      const checkInDatetime = Math.floor(data.date.getTime() / 1000);

      // Redirect to the booking page with query parameters
      const queryParams = new URLSearchParams({
        lotId: lotId.toString(),
        date: data.date.toISOString(),
        checkInTime: data.checkInTime,
        duration: data.duration.toString(),
        checkInDatetime: checkInDatetime.toString(),
      });

      router.push(`/new-booking?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error fetching data:', error);
      // Handle error here (show toast, error message, etc.)
      setIsSubmitting(false);
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        minHeight: '550px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-end',
        mb: { xs: 8, md: 0 },
      }}
    >
      {/* Swiper Carousel */}
      <StyledSwiper
        modules={[Pagination]}
        pagination={{
          clickable: true,
          dynamicBullets: false,
          hideOnClick: false,
          enabled: true,
        }}
        sx={{ width: '100%', height: '550px' }}
      >
        {outlets.map((outlet, index) => (
          <SwiperSlide key={index}>
            <Box
              sx={{
                position: 'relative',
                width: '100%',
                height: '550px',
                backgroundImage: `url(${
                  outlet.images?.[0]?.formats?.large ||
                  outlet.images?.[0]?.formats?.medium ||
                  outlet.images?.[0]?.url
                })`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  inset: 0,
                  bgcolor: outlet.overlay,
                  zIndex: 0,
                }}
              />
              <Box
                sx={{
                  position: 'relative',
                  zIndex: 10,
                  maxWidth: 'screen-xl',
                  mx: 'auto',
                  px: { xs: 2, md: 3 },
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  height: '100%',
                }}
              >
                <Box
                  sx={{
                    width: { xs: '100%', md: '50%' },
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: { xs: 'center', md: 'flex-start' },
                    height: '100%',
                    textAlign: { xs: 'center', md: 'left' },
                    pb: { xs: 16, md: 0 },
                  }}
                >
                  <Typography
                    variant='overline'
                    sx={{
                      color: 'white',
                      fontWeight: 600,
                      mb: { xs: 0.5, md: 1 },
                      display: 'block',
                    }}
                  >
                    {outlet.outlet}
                  </Typography>
                  <Typography
                    variant='h1'
                    sx={{
                      color: 'white',
                      mb: { xs: 1.5, md: 2 },
                      maxWidth: '2xl',
                      lineHeight: 'tight',
                      fontSize: {
                        xs: '1.5rem',
                        sm: '1.875rem',
                        md: '2.25rem',
                        lg: '3rem',
                      },
                    }}
                  >
                    {outlet.title}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'white',
                      fontWeight: 500,
                      mb: { xs: 3, md: 4 },
                      display: 'block',
                    }}
                  >
                    {outlet.subtitle}
                  </Typography>
                </Box>
                <Box
                  sx={{ display: { xs: 'none', md: 'block' }, width: '50%' }}
                />
              </Box>
            </Box>
          </SwiperSlide>
        ))}
      </StyledSwiper>
      {/* Booking Card overlays the carousel */}
      <Box
        sx={{
          position: 'absolute',
          left: '50%',
          transform: 'translateX(-50%)',
          bottom: 0,
          top: '77%',
          width: '100%',
          maxWidth: '1144px',
          zIndex: 30,
          px: { xs: 2, md: 0 },
        }}
      >
        <Paper
          elevation={3}
          sx={{
            bgcolor: 'white',
            borderRadius: 3,
            p: { xs: 2, md: 3, lg: 4 },
            display: 'flex',
            flexDirection: 'column',
            gap: 0,
            // borderBottom: '4px solid #a259ff',
            position: 'relative',
          }}
        >
          {/* OUTLET row */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              alignItems: { xs: 'flex-start', md: 'center' },
              justifyContent: 'space-between',
              gap: { xs: 0.5, md: 1 },
              mb: 1,
            }}
          >
            <Typography
              variant='caption'
              sx={{
                fontWeight: 600,
                mb: 0.5,
                color: 'text.secondary',
                width: { xs: '100%', md: 'auto' },
              }}
            >
              OUTLET
            </Typography>
          </Box>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ mb: { xs: 1.5, md: 2 } }}>
              <FormControl fullWidth>
                <Controller
                  name='lotId'
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Select
                      displayEmpty
                      {...field}
                      value={field.value || ''}
                      sx={{
                        '& .MuiSelect-select': {
                          py: { xs: 1.25, md: 1.5 },
                          px: { xs: 1.5, md: 2 },
                          fontSize: { xs: '0.875rem', md: '1rem' },
                        },
                        maxHeight: '42px',
                      }}
                    >
                      <MenuItem value='' disabled>
                        Select the outlet you want to stay
                      </MenuItem>
                      {outlets.map((outlet, index) => (
                        <MenuItem key={index} value={outlet.lotId}>
                          {outlet.terminal} - {outlet.title}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Box>

            <Divider sx={{ mb: 1 }} />

            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', lg: 'row' },
                alignItems: { xs: 'stretch', lg: 'flex-start' },
                gap: { xs: 1.5, md: 2, lg: 3 },
              }}
            >
              {/* DATE */}
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant='caption'
                  sx={{
                    fontWeight: 600,
                    mb: 0.5,
                    color: 'text.secondary',
                  }}
                >
                  DATE
                </Typography>
                <FormControl fullWidth>
                  <Controller
                    name='date'
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <DatePicker
                        open={openDatePicker}
                        onClose={() => setOpenDatePicker(false)}
                        slotProps={{
                          textField: {
                            onClick: () => setOpenDatePicker(true),
                          },
                        }}
                        value={field.value}
                        onChange={(newValue) => {
                          const date = handleChangeDate(newValue);
                          field.onChange(date || null);
                        }}
                        format='dd/MM/yyyy'
                        slots={{
                          openPickerIcon: ArrowDropDownIcon,
                        }}
                        minDate={currentDateTime}
                        sx={{
                          fontWeight: 600,
                          maxHeight: '42px',
                          '& .MuiInputBase-root': {
                            maxHeight: '42px',
                          },
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Box>

              {/* CHECK-IN TIME */}
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant='caption'
                  sx={{
                    fontWeight: 600,
                    mb: 0.5,
                    color: 'text.secondary',
                  }}
                >
                  CHECK-IN TIME
                </Typography>
                <FormControl fullWidth>
                  <Controller
                    name='checkInTime'
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        value={field.value || ''}
                        sx={{
                          '& .MuiSelect-select': {
                            py: { xs: 1.25, md: 1.5 },
                            px: { xs: 1.5, md: 2 },
                            fontSize: { xs: '0.875rem', md: '1rem' },
                          },
                          maxHeight: '42px',
                        }}
                      >
                        <MenuItem value='' disabled>
                          {!selectedDate
                            ? 'Select date first'
                            : 'Select check-in time'}
                        </MenuItem>
                        {selectedDate &&
                          getCompatibleTimes(selectedDate).map((time) => (
                            <MenuItem key={time} value={time}>
                              {time}
                            </MenuItem>
                          ))}
                      </Select>
                    )}
                  />
                </FormControl>
              </Box>

              {/* STAY DURATION */}
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant='caption'
                  sx={{
                    fontWeight: 600,
                    mb: 0.5,
                    color: 'text.secondary',
                  }}
                >
                  STAY DURATION
                </Typography>
                <FormControl fullWidth>
                  <Controller
                    name='duration'
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        disabled={!selectedLotId}
                        value={field.value || ''}
                        sx={{
                          '& .MuiSelect-select': {
                            py: { xs: 1.25, md: 1.5 },
                            px: { xs: 1.5, md: 2 },
                            fontSize: { xs: '0.875rem', md: '1rem' },
                            color: !selectedLotId ? 'text.disabled' : undefined,
                          },
                          '& .MuiOutlinedInput-root': {
                            bgcolor: !selectedLotId ? 'grey.100' : undefined,
                          },
                          maxHeight: '42px',
                        }}
                      >
                        <MenuItem value='' disabled>
                          {!selectedLotId
                            ? 'Please select the outlet first'
                            : 'Select stay duration'}
                        </MenuItem>
                        {DURATIONS.map((time) => (
                          <MenuItem key={time.value} value={time.value}>
                            {time.label}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                  {!selectedLotId && (
                    <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
                      Please select the outlet first
                    </Typography>
                  )}
                  {isAllFieldsFilled && checkoutTime && (
                    <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
                      <span style={{ color: '#989CA4' }}>Check-out at</span>{' '}
                      <span style={{ color: '#1A1A1A', fontWeight: 600 }}>
                        {formatCheckoutDisplay(checkoutTime)}
                      </span>
                    </Typography>
                  )}
                </FormControl>
              </Box>

              {/* CHECK AVAILABILITY BUTTON */}
              <Box
                sx={{
                  flex: 1,
                  paddingTop: '24px',
                }}
              >
                <Button
                  variant='contained'
                  type='submit'
                  disabled={isSubmitting}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    fontWeight: 'bold',
                    px: { xs: 3, md: 4 },
                    py: { xs: 1.25, md: 1.5 },
                    borderRadius: '4px',
                    boxShadow: 2,
                    '&:hover': { bgcolor: 'primary.dark' },
                    fontSize: '14px',
                    whiteSpace: 'nowrap',
                    maxHeight: '42px',
                    minWidth: '218px',
                    width: '100%',
                  }}
                >
                  {isSubmitting ? 'CHECKING...' : 'CHECK AVAILABILITY'}
                </Button>
              </Box>
            </Box>
          </form>
        </Paper>
      </Box>
    </Box>
  );
};

export default HeroSection;
