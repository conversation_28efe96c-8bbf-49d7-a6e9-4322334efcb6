import { Swiper } from 'swiper/react';
import { styled } from '@mui/material/styles';

// Styled components for custom styling
export const StyledSwiper = styled(Swiper)(({ theme }) => ({
  '& .swiper-pagination': {
    position: 'absolute !important',
    zIndex: '20 !important',
    pointerEvents: 'auto !important',
  },
  '& .swiper-pagination-bullet': {
    width: '12px !important',
    height: '12px !important',
    background: 'rgba(255, 255, 255, 0.5) !important',
    opacity: '1 !important',
    borderRadius: '50% !important',
    cursor: 'pointer !important',
    transition: 'all 0.3s ease !important',
    margin: '0 !important',
    display: 'inline-block !important',
    position: 'relative !important',
  },
  '& .swiper-pagination-bullet-active': {
    background: '#00E0C6 !important',
    transform: 'scale(1.2) !important',
  },
  [theme.breakpoints.down('md')]: {
    '& .swiper-pagination': {
      bottom: '7rem !important',
      left: '50% !important',
      top: 'auto !important',
      right: 'auto !important',
      transform: 'translateX(-50%) !important',
      width: 'auto !important',
      height: 'auto !important',
      display: 'flex !important',
      flexDirection: 'row !important',
      gap: '0.5rem !important',
      zIndex: '30 !important',
      position: 'absolute !important',
    },
    '& .swiper-pagination-bullet': {
      width: '10px !important',
      height: '10px !important',
      background: 'rgba(255, 255, 255, 0.8) !important',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2) !important',
    },
    '& .swiper-pagination-bullet-active': {
      background: '#00E0C6 !important',
      boxShadow: '0 2px 8px rgba(0, 224, 198, 0.4) !important',
    },
  },
  [theme.breakpoints.up('md')]: {
    '& .swiper-pagination': {
      right: '2rem !important',
      top: '50% !important',
      bottom: 'auto !important',
      left: 'auto !important',
      transform: 'translateY(-50%) !important',
      width: 'auto !important',
      height: 'auto !important',
      display: 'flex !important',
      flexDirection: 'column !important',
      gap: '1rem !important',
    },
  },
}));
