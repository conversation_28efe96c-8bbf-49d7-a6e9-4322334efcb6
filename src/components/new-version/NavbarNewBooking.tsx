import React from 'react';
import { AppBar, Toolbar, Button, Box } from '@mui/material';
import smallLogo from '@/assets/icons/general/LogoPrimary.svg';
import largeLogo from '@/assets/icons/general/Logo-CT.svg';
import Image from 'next/image';
import Link from 'next/link';
import CloseIcon from '../icons/CloseIcon';

const NavbarNewBooking = ({
  onClose,
}: {
  onClose?: () => void;
}) => {
  return (
    <AppBar
      position='fixed'
      sx={{
        bgcolor: '#011589',
        boxShadow: '0 4px 10px 0 rgba(0,0,0,0.2)',
        zIndex: 1000,
        top: 0,
        left: 0,
        right: 0,
      }}
    >
      <Toolbar
        sx={{
          maxWidth: '1440px',
          width: '100%',
          mx: 'auto',
          px: { xs: 2 },
          minHeight: { xs: '72px', md: '76px' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 4,
        }}
      >
        {/* Left Section: Hamburger + Navigation */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 3,
            flex: 1,
            justifyContent: 'flex-start',
          }}
        ></Box>

        {/* Center: Logo */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
          }}
        >
          {/* Mobile Logo */}
          <Box
            sx={{
              display: { xs: 'flex', md: 'none' },
              width: 120,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href="/new-version" style={{ width: '100%', height: '100%' }}>
              <Image
                src={smallLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>

          {/* Desktop Logo */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              width: 160,
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href="/new-version" style={{ width: '100%', height: '100%' }}>
              <Image
                src={largeLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>
        </Box>

        {/* Right: CTA Button */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'flex-end',
          }}
        >
          {/* Desktop Button */}
          <Button
            variant='contained'
            sx={{
              backgroundColor: 'transparent !important',
              color: '#fff',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              px: 4,
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'transparent !important',
                boxShadow: 'none !important',
              },
            }}
            endIcon={<CloseIcon />}
            onClick={onClose}
          ></Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default NavbarNewBooking;
