'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  IconButton,
  Divider,
  Stack,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface RulesAndRestrictionsDialogProps {
  open: boolean;
  onClose: () => void;
}

const RulesAndRestrictionsDialog: React.FC<RulesAndRestrictionsDialogProps> = ({
  open,
  onClose,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='md'
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : '12px',
          maxHeight: '90vh',
          margin: isMobile ? 0 : '16px',
        },
      }}
    >
      {/* Header */}
      <Stack
        direction='row'
        justifyContent='space-between'
        alignItems='center'
        sx={{
          borderBottom: `1px solid ${theme.palette.divider}`,
          pl: 3,
          pr: 1,
          py: 1,
        }}
      >
        <DialogTitle
          sx={{
            fontSize: isMobile ? '1.25rem' : '1.5rem',
            fontWeight: 700,
            p: 0,
            color: theme.palette.text.primary,
          }}
        >
          Hotel Policy
        </DialogTitle>
        <IconButton
          onClick={onClose}
          sx={{
            color: theme.palette.text.secondary,
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </Stack>

      {/* Content */}
      <DialogContent
        sx={{
          maxHeight: isMobile ? 'calc(100vh - 120px)' : '60vh',
          px: 3,
          py: 2,
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#f1f1f1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#c1c1c1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            backgroundColor: '#a8a8a8',
          },
        }}
      >
        {/* Payment and Cancellation Policy Section */}
        <Box sx={{ marginBottom: '32px' }}>
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              color: '#1A1A1A',
              marginBottom: '16px',
              fontSize: { xs: '14px', md: '16px' },
            }}
          >
            PAYMENT AND CANCELLATION POLICY
          </Typography>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                marginBottom: '12px',
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              Full payment will be charged once your booking is confirmed. No
              are no additional fees for credit or debit cards and all prices
              shown are inclusive of local taxes.
            </Typography>
          </Box>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                marginBottom: '12px',
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              Discounted and promotional rates are non-refundable. Any
              amendments or cancellation bookings with discounted or promotional
              rates will be charged in full.
            </Typography>
          </Box>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              In the case of modification of check-in dates, cancellation or
              no-show will, 100% of the room rate including any other applicable
              charges and taxes will be charged to your credit/debit card.
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ marginY: '24px' }} />

        {/* Important Information Section */}
        <Box>
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              color: '#1A1A1A',
              marginBottom: '16px',
              fontSize: { xs: '14px', md: '16px' },
            }}
          >
            IMPORTANT INFORMATION
          </Typography>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                marginBottom: '12px',
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              Please make sure that the hotel and location you have selected
              matches the area of the airport your flight will depart from. If
              you are entering Malaysia or in transit to another destination,
              you will need valid entry into Malaysia (visa, landing card) to
              clear Malaysian immigration and access our hotel. The hotel will
              not be liable to any cost and will not refund any charges if the
              wrong booking have been made.
            </Typography>
          </Box>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                marginBottom: '12px',
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              If you are departing from Malaysia, you can easily access the
              hotel as It is located before immigration and customs checkpoint.
            </Typography>
          </Box>

          <Box sx={{ marginBottom: '16px' }}>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                marginBottom: '12px',
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              If you are landing at KLIA1, proceed through immigration, customs
              checkpoints and baggage reclaim, embark either the free shuttle
              bus service or ERL (Train) to the South Terminal and follow the
              directions to our hotel.
            </Typography>
          </Box>

          <Box>
            <Typography
              variant='body1'
              sx={{
                color: '#4A5568',
                lineHeight: 1.6,
                fontSize: { xs: '14px', md: '16px' },
              }}
            >
              Please log on to{' '}
              <Typography
                component='a'
                href='http://www.imi.gov.my/index.php/en/visa/types-of-visa.html'
                target='_blank'
                rel='noopener noreferrer'
                sx={{
                  color: '#2563EB',
                  textDecoration: 'underline',
                  '&:hover': {
                    textDecoration: 'none',
                  },
                }}
              >
                http://www.imi.gov.my/index.php/en/visa/types-of-visa.html
              </Typography>{' '}
              should you require a VISA to enter Malaysia and to exit the
              airport departure hall to access CapsuleTransit.
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      {/* Footer Actions */}
      <DialogActions
        sx={{
          padding: '16px 24px 24px',
          justifyContent: 'center',
        }}
      >
        <Button
          onClick={onClose}
          variant='contained'
          sx={{
            backgroundColor: '#011589',
            color: 'white',
            padding: '12px 32px',
            borderRadius: '4px',
            textTransform: 'none',
            fontWeight: 500,
            fontSize: '1rem',
            minWidth: '120px',
            '&:hover': {
              backgroundColor: '#011589',
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RulesAndRestrictionsDialog;
