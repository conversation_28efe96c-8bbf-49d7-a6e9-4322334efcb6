'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Stack,
  Chip,
  useTheme,
  useMediaQuery,
  IconButton,
  Fade,
  Skeleton,
} from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import ModalRoomPhotos from '../ModalRoomPhotos';
import { useRoomsSelectionContext } from './context';
import { RoomSelectionDetail, RoomPriceOption } from './types';

interface RoomSelectionProps {
  room: RoomSelectionDetail;
}

const RoomSelection: React.FC<RoomSelectionProps> = ({ room }) => {
  const {
    isLoadingOtherAvailableRoomOptions,
    selectedRooms,
    handleChangeOtherAvailableOption,
    handleSelectRoom,
    handleRemoveRoom,
  } = useRoomsSelectionContext();

  const { title, capacity, bedType, selectedOption, roomDetails, images } =
    room;

  const roomPriceOptions = roomDetails?.prices || [];
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  const countQuantity = selectedRooms[room.name]?.quantity || 0;

  const [isAnimating, setIsAnimating] = useState(false);
  const [tempSelectedOption, setTempSelectedOption] = useState(selectedOption);

  // Update tempSelectedOption when selectedOption prop changes
  useEffect(() => {
    setTempSelectedOption(selectedOption);
  }, [selectedOption]);

  // Handle animated option selection
  const handleAnimatedOptionSelect = (room: any, option: RoomPriceOption) => {
    setIsAnimating(true);

    // First phase: fade out current selection
    setTimeout(() => {
      setTempSelectedOption(option);
    }, 200);

    // Second phase: fade in new selection and call parent handler
    setTimeout(() => {
      setIsAnimating(false);
      handleChangeOtherAvailableOption?.(room, option);
    }, 600);
  };

  const handleSelect = () => {
    handleSelectRoom({ room: room, quantity: 1 });
  };

  const handleIncrement = () => {
    handleSelectRoom({ room: room, quantity: countQuantity + 1 });
  };

  const handleDecrement = () => {
    if (countQuantity > 0) {
      handleSelectRoom({ room: room, quantity: countQuantity - 1 });
    }
  };

  const handleRemove = () => {
    handleRemoveRoom?.(room);
  };

  const [isOpenRoomPhotosModal, setIsOpenRoomPhotosModal] = useState(false);

  const handleOpenModal = () => {
    setIsOpenRoomPhotosModal(true);
  };

  const handleCloseModal = () => {
    setIsOpenRoomPhotosModal(false);
  };

  if (isMobile) {
    return (
      <Paper
        elevation={2}
        sx={{
          maxWidth: '1376px',
          width: '100%',
          border: '1px solid',
          borderColor: '#4D5CAC',
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        <ModalRoomPhotos
          images={room.images}
          isOpen={isOpenRoomPhotosModal}
          handleClose={handleCloseModal}
        />
        {/* Mobile Layout */}
        <Box sx={{}}>
          {/* Title - Full Width */}
          <Typography
            variant='h5'
            component='h3'
            sx={{
              fontWeight: 600,
              color: '#0F0E0E',
              fontSize: '22px',
              width: '100%',
              textAlign: 'left',
              padding: '10px 16px',
            }}
          >
            {title}
          </Typography>

          {/* Content Section - Left and Right */}
          <Box
            sx={{
              display: 'flex',
              mb: 3,
              flexDirection: { xs: 'column', xxs: 'row' },
            }}
          >
            {/* Left Part - Image and Room Details */}
            <Box
              sx={{
                width: { xs: '100%', xxs: '150px' },
                display: { xs: 'flex', xxs: 'block' },
              }}
            >
              {/* Image Container */}
              <Box
                sx={{
                  width: '150px',
                  height: '150px',
                  position: 'relative',
                  bgcolor: 'grey.100',
                  overflow: 'hidden',
                  mb: 2,
                }}
              >
                <Swiper
                  modules={[Navigation, Pagination, Autoplay]}
                  navigation={false}
                  pagination={false}
                  loop={true}
                  autoplay={{
                    delay: 5000,
                    disableOnInteraction: false,
                  }}
                  spaceBetween={0}
                  slidesPerView={1}
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                  className='room-swiper-mobile'
                >
                  {images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <Box
                        component='img'
                        src={image?.url}
                        alt={image?.name || `${title} - Image ${index + 1}`}
                        sx={{
                          width: '150px',
                          height: '150px',
                          objectFit: 'cover',
                          display: 'block',
                        }}
                      />
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* View Photos Button Overlay */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    zIndex: 10,
                    maxHeight: '28px',
                    height: '100%',
                  }}
                >
                  <Typography
                    variant='body2'
                    onClick={handleOpenModal}
                    sx={{
                      color: 'white !important',
                      textAlign: 'center',
                      fontSize: '15px !important',
                      fontWeight: 'normal !important',
                      cursor: 'pointer',
                      textDecoration: 'underline',
                      lineHeight: '25px',
                    }}
                  >
                    View photos
                  </Typography>
                </Box>
              </Box>

              {/* Room Details */}
              <Stack spacing={1.5} sx={{ padding: '10px 16px' }}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '6px',
                  }}
                >
                  <Typography
                    variant='body2'
                    sx={{
                      textTransform: 'uppercase',
                      fontSize: '12px !important',
                      fontWeight: 600,
                      color: '#989CA4 !important',
                    }}
                  >
                    ROOM FOR
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{ fontWeight: 500, fontSize: '14px', color: '#3B3F47' }}
                  >
                    {capacity}
                  </Typography>
                </Box>

                <Box
                  sx={{ display: 'flex', flexDirection: 'column', gap: '6px' }}
                >
                  <Typography
                    variant='body2'
                    sx={{
                      textTransform: 'uppercase',
                      fontSize: '12px !important',
                      fontWeight: 600,
                      color: '#989CA4 !important',
                    }}
                  >
                    BED TYPE
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{ fontWeight: 500, fontSize: '14px', color: '#3B3F47' }}
                  >
                    {bedType}
                  </Typography>
                </Box>
              </Stack>
            </Box>

            {/* Right Part - Selection Options */}
            <Box
              sx={{
                width: { xs: '100%', xxs: 'calc(100% - 150px)' },
                borderTop: '1px solid #E0E0E0',
                borderLeft: '1px solid #E0E0E0',
                pl: '16px',
                pr: '16px',
                pt: '16px',
              }}
            >
              <Typography
                variant='body2'
                color='#989CA4 !important'
                sx={{
                  textTransform: 'uppercase',
                  fontSize: '10px !important',
                  fontWeight: 600,
                  mb: '8px',
                }}
              >
                YOU ARE NOW SELECTING
              </Typography>

              {/* Selected Option */}
              <Box
                sx={{
                  border: '1px solid',
                  borderColor: '#011589 !important',
                  borderRadius: '8px',
                  p: { xs: '5px 5px', xss: '5px 10px' },
                  mb: 3,
                  bgcolor: '#F2F3FD',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                <Fade in={!isAnimating} timeout={300}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      flexWrap: { xs: 'wrap', sm: 'nowrap' },
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      <AccessTimeIcon
                        sx={{
                          fontSize: { xs: '12px', xxs: '18px !important' },
                          color: '#011589 !important',
                          marginTop: { xs: '1x', sm: '3px' },
                        }}
                      />
                      <Typography
                        variant='body1'
                        sx={{
                          fontWeight: 500,
                          fontSize: {
                            xs: '14px',
                            xxs: '16px',
                            sm: '20px !important',
                          },
                          color: '#011589 !important',
                        }}
                      >
                        {tempSelectedOption.hours}{' '}
                        {tempSelectedOption.hours === 1 ? 'Hour' : 'Hours'}
                      </Typography>
                    </Box>
                    <Typography
                      variant='body1'
                      sx={{
                        fontWeight: 500,
                        fontSize: {
                          xs: '14px',
                          xxs: '16px',
                          sm: '20px !important',
                        },
                        color: '#011589 !important',
                      }}
                    >
                      RM{tempSelectedOption.price}
                    </Typography>
                  </Box>
                </Fade>

                {/* Loading animation overlay */}
                {isAnimating && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(242, 243, 253, 0.9)',
                    }}
                  >
                    <Box
                      sx={{
                        width: '20px',
                        height: '20px',
                        border: '2px solid #E0E0E0',
                        borderTop: '2px solid #011589',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' },
                        },
                      }}
                    />
                  </Box>
                )}
              </Box>

              <Typography
                variant='body2'
                color='#989CA4 !important'
                sx={{
                  textTransform: 'uppercase',
                  fontSize: '10px !important',
                  fontWeight: 600,
                  mb: '8px',
                }}
              >
                OTHER AVAILABLE OPTIONS
              </Typography>

              {/* Available Options List */}
              <Stack spacing={0.5}>
                {isLoadingOtherAvailableRoomOptions ? (
                  <Box>
                    <Skeleton variant='text' width={100} height={20} />
                  </Box>
                ) : (
                  roomPriceOptions.map((option, index) => (
                    <Box
                      key={index}
                      sx={{
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        position: 'relative',
                        overflow: 'hidden',
                        py: 0.5,
                        flexWrap: { xs: 'wrap', xxs: 'nowrap' },
                        borderRadius: '8px',
                        padding: '8px 10px',
                        // background: 'linear-gradient(135deg, #FAFBFF 0%, #F8F9FF 100%)',
                        border: '1px solid transparent',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        transform: 'translateY(0px)',
                        // boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-100%',
                          width: '100%',
                          height: '100%',
                          background:
                            'linear-gradient(90deg, transparent, rgba(1, 21, 137, 0.08), transparent)',
                          transition: 'left 0.6s ease',
                          zIndex: 1,
                        },
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          borderColor: '#011589',
                          background:
                            'linear-gradient(135deg, #F0F2FF 0%, #E8EBFF 100%)',
                          boxShadow: '0 8px 25px rgba(1, 21, 137, 0.15)',
                          '&::before': {
                            left: '100%',
                          },
                          '& .price-display': {
                            color: '#011589',
                            fontWeight: '600 !important',
                          },
                          '& .time-icon': {
                            color: '#011589',
                          },
                          '& .action-hint': {
                            opacity: 1,
                            transform: 'translateX(0px)',
                          },
                        },
                      }}
                      onClick={() => handleAnimatedOptionSelect(room, option)}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 2,
                          position: 'relative',
                        }}
                      >
                        <AccessTimeIcon
                          className='time-icon'
                          sx={{
                            color: '#3B3F47',
                            fontSize: '18px',
                            marginTop: '0px',
                            transition: 'all 0.3s ease',
                          }}
                        />
                        <Typography
                          variant='body2'
                          sx={{
                            fontSize: {
                              xs: '14px !important',
                              xxs: '16px !important',
                            },
                            fontWeight: '400 !important',
                          }}
                        >
                          {option.hours} {option.hours === 1 ? 'Hour' : 'Hours'}
                        </Typography>
                        {option.isBestDeal && (
                          <Chip
                            label='BEST DEAL!'
                            size='small'
                            sx={{
                              bgcolor: '#011589',
                              color: 'white',
                              fontSize: { xs: '9px', xxs: '10px !important' },
                              borderRadius: '4px',
                              fontWeight: '400 !important',
                              p: '8px',
                              '& .MuiChip-label': { px: 0 },
                            }}
                          />
                        )}
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          zIndex: 2,
                          position: 'relative',
                        }}
                      >
                        <Typography
                          className='price-display'
                          variant='body2'
                          sx={{
                            fontWeight: '400 !important',
                            fontSize: {
                              xs: '14px !important',
                              xxs: '16px !important',
                            },
                            transition: 'all 0.3s ease',
                          }}
                        >
                          RM{option.price}
                        </Typography>
                      </Box>
                    </Box>
                  ))
                )}
              </Stack>
            </Box>
          </Box>

          {/* Action Button Section - Full Width */}
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              pt: '16px',
              pb: '16px',
              borderTop: '1px solid #E0E0E0',
              backgroundColor: countQuantity > 0 ? '#011589' : '#DADCEC',
              transition: 'background-color 0.3s ease',
            }}
          >
            {countQuantity <= 0 ? (
              <Button
                variant='outlined'
                onClick={() => handleSelect()}
                sx={{
                  bgcolor: 'white',
                  borderColor: '#011589 !important',
                  color: '#011589',
                  textTransform: 'uppercase',
                  fontWeight: 600,
                  maxHeight: '40px',
                  px: 3,
                  py: 1.5,
                  borderRadius: '4px',
                  width: '80%',
                  '&:hover': {
                    bgcolor: '#011589',
                    color: 'white',
                  },
                }}
              >
                SELECT
              </Button>
            ) : (
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  alignItems: 'center',
                  gap: { xs: 1.5, sm: 2 },
                  width: '100%',
                  px: { xs: 1, sm: 2 },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'start',
                    gap: '8px',
                  }}
                >
                  {/* Room Counter Number */}
                  <Typography
                    variant='h3'
                    sx={{
                      fontSize: { xs: '48px' },
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                    }}
                  >
                    {countQuantity}
                  </Typography>

                  {/* Room Added Text */}
                  <Typography
                    variant='body1'
                    sx={{
                      fontSize: { xs: '12px' },
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                      maxWidth: '50px',
                    }}
                  >
                    ROOM ADDED
                  </Typography>
                </Box>

                {/* Controls Row */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'end',
                    alignItems: 'center',
                    width: '100%',
                    gap: '24px',
                  }}
                >
                  {/* Remove Text */}
                  <Typography
                    variant='body2'
                    onClick={() => handleRemove()}
                    sx={{
                      fontSize: { xs: '15px' },
                      fontWeight: 400,
                      color: '#DADCEC !important',
                      textAlign: 'center',
                      textDecoration: 'underline',
                      cursor: 'pointer',
                      '&:hover': {
                        color: '#E0E0E0',
                      },
                    }}
                  >
                    Remove
                  </Typography>

                  {/* Increment/Decrement Buttons */}
                  <Box
                    sx={{
                      display: 'flex',
                      gap: { xs: '12px' },
                    }}
                  >
                    <IconButton
                      onClick={handleDecrement}
                      disabled={countQuantity <= 1}
                      sx={{
                        width: { xs: '26px', sm: '28px' },
                        height: { xs: '26px', sm: '28px' },
                        bgcolor: countQuantity > 1 ? '#FFFFFF' : '#4150A7',
                        color: countQuantity > 1 ? '#011589' : 'white',
                        borderRadius: '6px',
                        ...(countQuantity > 1
                          ? {
                              '&:hover': {
                                bgcolor: '#F5F5F5',
                                color: '#011589',
                              },
                            }
                          : {}),
                        '&:disabled': {
                          bgcolor: '#4150A7',
                          color: '#011589',
                        },
                      }}
                    >
                      <RemoveIcon
                        sx={{ fontSize: { xs: '12px', sm: '14px' } }}
                      />
                    </IconButton>

                    <IconButton
                      onClick={handleIncrement}
                      sx={{
                        width: { xs: '26px', sm: '28px' },
                        height: { xs: '26px', sm: '28px' },
                        bgcolor: '#FFFFFF',
                        color: '#011589',
                        borderRadius: '6px',
                        '&:hover': {
                          bgcolor: '#F5F5F5',
                        },
                      }}
                    >
                      <AddIcon sx={{ fontSize: { xs: '12px', sm: '14px' } }} />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Box>
      </Paper>
    );
  }

  // Desktop Layout (existing code)
  return (
    <Paper
      elevation={2}
      sx={{
        maxWidth: '1376px',
        width: '100%',
        border: '1px solid',
        borderColor: '#4D5CAC',
        borderRadius: 2,
        overflow: 'hidden',
        marginLeft: 'auto !important',
        marginRight: 'auto !important',
      }}
    >
      <ModalRoomPhotos
        images={room.images}
        isOpen={isOpenRoomPhotosModal}
        handleClose={handleCloseModal}
      />
      <Box sx={{ display: 'flex', height: '325px' }}>
        {/* Slide Images Section - 330px width */}
        <Box
          sx={{
            width: '330px',
            height: '325px',
            position: 'relative',
            bgcolor: 'grey.100',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              '& .swiper-button-next, & .swiper-button-prev': {
                transform: 'translateY(-50%)',
                zIndex: 10,
                backgroundColor: 'rgba(15, 14, 14, 0.5)',
                width: '32px',
                height: '48px',
                '&:hover': {
                  backgroundColor: 'rgba(15, 14, 14, 0.8)',
                },
                boxShadow: theme.shadows[4],
                transition: 'all 0.2s',
                borderRadius: '4px',
                '&::after': {
                  fontSize: '18px',
                  color: 'white',
                  fontWeight: 'bold',
                },
              },
              '& .swiper-button-prev': {
                left: '12px',
                zIndex: 10,
                top: '55%'
              },
              '& .swiper-button-next': {
                right: '12px',
                zIndex: 10,
                top: '55%'
              },
              '& .swiper-button-disabled': {
                opacity: 0.3,
                cursor: 'not-allowed',
              },
              '& .swiper-pagination': {
                bottom: '16px',
                zIndex: 10,
              },
              '& .swiper-pagination-bullet': {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                opacity: 1,
                width: '8px',
                height: '8px',
                '&.swiper-pagination-bullet-active': {
                  backgroundColor: 'primary.main',
                },
              },
            }}
          >
            <Swiper
              modules={[Navigation, Pagination, Autoplay]}
              navigation={true}
              pagination={false}
              loop={true}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
              }}
              spaceBetween={0}
              slidesPerView={1}
              style={{
                width: '100%',
                height: '100%',
              }}
              className='room-swiper'
            >
              {images.map((image, index) => (
                <SwiperSlide key={index}>
                  <Box
                    component='img'
                    src={image?.url}
                    alt={image?.name || `${title} - Image ${index + 1}`}
                    sx={{
                      width: '330px',
                      height: '325px',
                      objectFit: 'cover',
                      display: 'block',
                    }}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        </Box>

        {/* Card Information Section - Remaining width */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            p: 3,
          }}
        >
          {/* Left Part - Room Details */}
          <Box sx={{ width: '50%', pr: 2, position: 'relative' }}>
            <Typography
              variant='h5'
              component='h3'
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 2,
                fontSize: '28px',
              }}
            >
              {title}
            </Typography>

            <Stack spacing={1.5} sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Typography
                  variant='body2'
                  color='#989CA4'
                  sx={{
                    textTransform: 'uppercase',
                    fontSize: '12px',
                    fontWeight: 600,
                  }}
                >
                  ROOM FOR
                </Typography>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 500, fontSize: '16px' }}
                >
                  {capacity}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Typography
                  variant='body2'
                  color='#989CA4'
                  sx={{
                    textTransform: 'uppercase',
                    fontSize: '12px',
                    fontWeight: 600,
                  }}
                >
                  BED TYPE
                </Typography>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 500, fontSize: '16px' }}
                >
                  {bedType}
                </Typography>
              </Box>
            </Stack>

            <Typography
              variant='body2'
              onClick={handleOpenModal}
              sx={{
                color: '#011589',
                textDecoration: 'underline',
                cursor: 'pointer',
                '&:hover': { color: '#021060' },
                fontSize: '15px',
                position: 'absolute',
                bottom: 0,
              }}
            >
              Room photos
            </Typography>
          </Box>

          {/* Vertical Divider */}
          <Box
            sx={{
              width: '1px',
              bgcolor: 'divider',
              mx: 2,
              my: 1,
            }}
          />

          {/* Right Part - Selection Options */}
          <Box sx={{ width: '50%', pl: 2 }}>
            <Typography
              variant='body2'
              color='#989CA4'
              sx={{
                textTransform: 'uppercase',
                fontSize: '12px',
                fontWeight: 600,
                mb: '8px',
              }}
            >
              YOU ARE NOW SELECTING
            </Typography>

            {/* Selected Option */}
            <Box
              sx={{
                border: '1px solid',
                borderColor: '#011589 !important',
                borderRadius: '8px',
                p: '5px 16px',
                mb: 3,
                bgcolor: '#F2F3FD',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <Fade in={!isAnimating} timeout={300}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AccessTimeIcon
                      sx={{
                        fontSize: 18,
                        color: '#011589 !important',
                        marginTop: '3px',
                      }}
                    />
                    <Typography
                      variant='body1'
                      sx={{
                        fontWeight: 500,
                        fontSize: '22px',
                        color: '#011589 !important',
                      }}
                    >
                      {tempSelectedOption.hours}{' '}
                      {tempSelectedOption.hours === 1 ? 'Hour' : 'Hours'}
                    </Typography>
                  </Box>
                  <Typography
                    variant='body1'
                    sx={{
                      fontWeight: 500,
                      fontSize: '22px',
                      color: '#011589 !important',
                    }}
                  >
                    RM{tempSelectedOption.price}
                  </Typography>
                </Box>
              </Fade>

              {/* Loading animation overlay */}
              {isAnimating && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(242, 243, 253, 0.9)',
                  }}
                >
                  <Box
                    sx={{
                      width: '24px',
                      height: '24px',
                      border: '3px solid #E0E0E0',
                      borderTop: '3px solid #011589',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                </Box>
              )}
            </Box>

            <Typography
              variant='body2'
              color='#989CA4'
              sx={{
                textTransform: 'uppercase',
                fontSize: '12px',
                fontWeight: 600,
                mb: '8px',
              }}
            >
              OTHER AVAILABLE OPTIONS
            </Typography>

            {/* Available Options List */}
            <Stack spacing={1.5}>
              {isLoadingOtherAvailableRoomOptions ? (
                <Box>
                  <Skeleton variant='text' width={100} height={20} />
                </Box>
              ) : (
                roomPriceOptions.map((option, index) => (
                  <Box
                    key={index}
                    sx={{
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      py: 0.5,
                      borderRadius: '8px',
                      padding: '8px 12px',
                      border: '1px solid transparent',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        borderColor: '#011589',
                        background:
                          'linear-gradient(135deg, #F0F2FF 0%, #E8EBFF 100%)',
                        boxShadow: '0 4px 12px rgba(1, 21, 137, 0.1)',
                        '& .price-display': {
                          color: '#011589',
                          fontWeight: '600 !important',
                        },
                        '& .time-icon': {
                          color: '#011589',
                        },
                      },
                    }}
                    onClick={() => handleAnimatedOptionSelect(room, option)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeIcon
                        className='time-icon'
                        sx={{
                          color: '#3B3F47',
                          fontSize: '18px',
                          marginTop: '0px',
                          transition: 'all 0.3s ease',
                        }}
                      />
                      <Typography
                        variant='body2'
                        sx={{ fontSize: '20px', fontWeight: 500 }}
                      >
                        {option.hours} {option.hours === 1 ? 'Hour' : 'Hours'}
                      </Typography>
                      {option.isBestDeal && (
                        <Chip
                          label='BEST DEAL!'
                          size='small'
                          sx={{
                            bgcolor: '#011589',
                            color: 'white',
                            fontSize: '12px',
                            borderRadius: '4px',
                            fontWeight: 600,
                            p: '8px',
                            '& .MuiChip-label': { px: 0 },
                          }}
                        />
                      )}
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        fontSize: '20px',
                      }}
                    >
                      <Typography
                        className='price-display'
                        variant='body2'
                        sx={{
                          fontWeight: 500,
                          fontSize: '20px',
                          transition: 'all 0.3s ease',
                        }}
                      >
                        RM{option.price}
                      </Typography>
                    </Box>
                  </Box>
                ))
              )}
            </Stack>
          </Box>
        </Box>

        {/* Select Button Section - 150px width */}
        <Box
          sx={{
            width: '150px',
            bgcolor: countQuantity > 0 ? '#011589' : '#DADCEC',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 2,
            transition: 'background-color 0.3s ease',
          }}
        >
          {countQuantity <= 0 && (
            <Button
              variant='outlined'
              onClick={() => handleSelect()}
              sx={{
                bgcolor: 'white',
                borderColor: '#011589 !important',
                color: '#011589',
                textTransform: 'uppercase',
                fontWeight: 600,
                maxHeight: '40px',
                px: 3,
                py: 1.5,
                borderRadius: 2,
                '&:hover': {
                  bgcolor: '#011589',
                  color: 'white',
                },
              }}
            >
              SELECT
            </Button>
          )}
          {countQuantity > 0 && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
                width: '100%',
              }}
            >
              {/* Room Counter Number */}
              <Typography
                variant='h3'
                sx={{
                  fontSize: '48px',
                  fontWeight: 700,
                  color: 'white',
                  textAlign: 'center',
                  mb: '8px',
                }}
              >
                {countQuantity}
              </Typography>

              {/* Room Added Text */}
              <Typography
                variant='body1'
                sx={{
                  fontSize: '12px',
                  fontWeight: 600,
                  color: 'white',
                  textAlign: 'center',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  mb: '8px',
                }}
              >
                ROOM ADDED
              </Typography>

              {/* Increment/Decrement Buttons */}
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                  mb: 2,
                }}
              >
                <IconButton
                  onClick={handleDecrement}
                  disabled={countQuantity <= 1}
                  sx={{
                    width: '32px',
                    height: '32px',
                    bgcolor: countQuantity > 1 ? '#FFFFFF' : '#4150A7',
                    color: countQuantity > 1 ? '#011589' : 'white',
                    borderRadius: '8px',
                    ...(countQuantity > 1
                      ? {
                          '&:hover': {
                            bgcolor: '#F5F5F5',
                            color: '#011589',
                          },
                        }
                      : {}),

                    '&:disabled': {
                      bgcolor: '#4150A7',
                      color: '#011589',
                    },
                  }}
                >
                  <RemoveIcon sx={{ fontSize: '16px' }} />
                </IconButton>

                <IconButton
                  onClick={handleIncrement}
                  sx={{
                    width: '32px',
                    height: '32px',
                    bgcolor: '#FFFFFF',
                    color: '#011589',
                    borderRadius: '8px',
                    '&:hover': {
                      bgcolor: '#F5F5F5',
                    },
                  }}
                >
                  <AddIcon sx={{ fontSize: '16px' }} />
                </IconButton>
              </Box>

              {/* Remove Text */}
              <Typography
                variant='body2'
                onClick={() => handleRemove()}
                sx={{
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'white',
                  textAlign: 'center',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                  '&:hover': {
                    color: '#E0E0E0',
                  },
                }}
              >
                Remove
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default RoomSelection;
