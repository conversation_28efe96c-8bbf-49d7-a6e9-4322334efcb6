import React, { createContext, useContext } from 'react';
import { SelectedRooms } from '../types';
import { PriceOption } from '@/constant/outlet';
import { RoomImagesApiResponse } from '@/actions/getRoomImages/types';

export interface RoomsSelectionContextType {
  // Props passed from parent
  selectedRooms: SelectedRooms;
  selectedDuration?: number;
  isLoadingOtherAvailableRoomOptions?: boolean;
  handleChangeOtherAvailableOption?: (room: any, price: PriceOption) => void;
  handleSelectRoom: (data: { room: any; quantity: number }) => void;
  handleRemoveRoom: (room: any) => void;
  roomImageData: RoomImagesApiResponse;
}

interface RoomsSelectionProviderProps {
  children: React.ReactNode;
  selectedRooms: SelectedRooms;
  selectedDuration?: number;
  isLoadingOtherAvailableRoomOptions?: boolean;
  handleChangeOtherAvailableOption?: (room: any, price: PriceOption) => void;
  handleSelectRoom: (data: { room: any; quantity: number }) => void;
  handleRemoveRoom: (room: any) => void;
  roomImageData: RoomImagesApiResponse;
}

// Create the context
const RoomsSelectionContext = createContext<
  RoomsSelectionContextType | undefined
>(undefined);

// Provider component
export const RoomsSelectionProvider: React.FC<RoomsSelectionProviderProps> = ({
  children,
  selectedRooms = {},
  selectedDuration = 0,
  isLoadingOtherAvailableRoomOptions = false,
  handleChangeOtherAvailableOption,
  handleSelectRoom,
  handleRemoveRoom,
  roomImageData,
}) => {
  // Context value
  const contextValue: RoomsSelectionContextType = {
    // Props
    selectedRooms,
    selectedDuration,
    isLoadingOtherAvailableRoomOptions,
    handleChangeOtherAvailableOption,
    handleSelectRoom,
    handleRemoveRoom,
    roomImageData,
  };

  return (
    <RoomsSelectionContext.Provider value={contextValue}>
      {children}
    </RoomsSelectionContext.Provider>
  );
};

// Custom hook to use the context
export const useRoomsSelectionContext = (): RoomsSelectionContextType => {
  const context = useContext(RoomsSelectionContext);
  if (context === undefined) {
    throw new Error(
      'useRoomsSelectionContext must be used within a RoomsSelectionContext'
    );
  }
  return context;
};

// Export the context for advanced use cases
export { RoomsSelectionContext };
