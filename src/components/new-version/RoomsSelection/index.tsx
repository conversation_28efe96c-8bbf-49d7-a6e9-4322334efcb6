'use client';

import React, { useMemo } from 'react';
import RoomSelection from './RoomSelection';
import RoomSkeleton from './RoomSkeleton';
import { Box, Typography, Container, Stack } from '@mui/material';
import useGetRoomImages from '@/hooks/useGetRoomImages';
import { OUTLETS, ROOM_OPTIONS } from '@/constant/outlet';
import { useRoomsSelectionContext } from './context';
import { RoomPriceOption, RoomSelectionDetail } from './types';

const RoomsSelectionList = ({
  rooms,
  isLoading,
  lotId,
  otherAvailableRoomOptions,
}: {
  rooms?: any[];
  isLoading?: boolean;
  lotId: number;
  otherAvailableRoomOptions?: {
    [k: string]: ({ roomTypeName: string } & RoomPriceOption)[];
  };
}) => {
  const { selectedDuration, roomImageData } = useRoomsSelectionContext();
  // Check if we have any filters applied
  const hasFilters = selectedDuration && selectedDuration > 0;

  const { data: _resultRoomImage } = useGetRoomImages({
    isAutoFetch: !roomImageData?.data?.length,
  });

  const resultRoomImage = roomImageData?.data?.length
    ? roomImageData
    : _resultRoomImage;

  const outlet = OUTLETS.find((outlet) => outlet.lotId === Number(lotId));

  const roomsData = useMemo<RoomSelectionDetail[]>(() => {
    const imagesByOutlet =
      resultRoomImage?.groupImagesByType?.[String(outlet?.key)?.toLowerCase()];

    return (rooms || [])
      .map((room) => {
        const images =
          imagesByOutlet?.[String(room.name).toLowerCase()]?.images || [];

        const outletName = outlet?.key?.toLowerCase();

        // const outlet = selectedOutlet?.imagesByRoomType?.[roomType]?.outlet;
        const roomTypeKey = `${outletName}_${String(room.name).toLowerCase()}`;
        const roomDetails = ROOM_OPTIONS[roomTypeKey] || {};

        const prices = otherAvailableRoomOptions?.[room.name] || [];

        const selectedOption = {
          hours: selectedDuration,
          price: room.price,
        };

        return {
          ...room,
          title: room.name,
          selectedOption,
          roomDetails: {
            ...roomDetails,
            prices: prices.filter(
              (price) => Number(price.hours) !== Number(selectedOption.hours)
            ),
          },
          images,
        };
      })
      .filter((room) => room.images.length > 0);
  }, [
    outlet?.key,
    rooms,
    resultRoomImage?.groupImagesByType,
    selectedDuration,
    otherAvailableRoomOptions,
  ]);

  return (
    <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
      <Container maxWidth='xl'>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant='h2'
            component='h1'
            sx={{
              fontWeight: 600,
              fontStyle: 'medium',
              color: '#0F0E0E',
              mb: 2,
              fontSize: { xs: '38px', md: '40px' },
            }}
          >
            Select Your Option
          </Typography>
        </Box>

        {/* Rooms List */}
        <Stack spacing={4}>
          {!hasFilters ? (
            // Show message when no filters are applied
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant='h5' color='text.secondary' sx={{ mb: 2 }}>
                Please select your preferences above
              </Typography>
              <Typography variant='body1' color='text.secondary'>
                Choose a date, time, and duration to see available rooms
              </Typography>
            </Box>
          ) : isLoading ? (
            // Show skeleton loading for 3 rooms
            Array.from({ length: 3 }).map((_, index) => (
              <RoomSkeleton key={`skeleton-${index}`} />
            ))
          ) : roomsData.length > 0 ? (
            roomsData.map((room) => <RoomSelection key={room.id} room={room} />)
          ) : (
            // Show message when no rooms are available
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant='h6' color='text.secondary'>
                No rooms available for the selected criteria
              </Typography>
            </Box>
          )}
        </Stack>
      </Container>
    </Box>
  );
};

export default RoomsSelectionList;
