import React from 'react';
import {
  Box,
  Skeleton,
  Paper,
  Stack,
  useTheme,
  useMediaQuery,
} from '@mui/material';

const RoomSkeleton: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  // Animation variants for staggered loading
  const skeletonVariants = {
    animate: {
      opacity: [0.3, 0.7, 0.3],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  if (isMobile) {
    return (
      <Paper
        elevation={2}
        sx={{
          maxWidth: '1376px',
          width: '100%',
          border: '1px solid',
          borderColor: '#E0E0E0',
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        <Box sx={{ p: 2 }}>
          {/* Title Skeleton */}
          <Skeleton
            variant="text"
            width="80%"
            height={28}
            sx={{ mb: 2 }}
            animation="wave"
          />

          <Box sx={{ display: 'flex', mb: 3 }}>
            {/* Left Part - Image and Room Details */}
            <Box sx={{ width: '150px' }}>
              {/* Image Skeleton */}
              <Skeleton
                variant="rectangular"
                width={150}
                height={150}
                sx={{ mb: 2, borderRadius: 1 }}
                animation="wave"
              />

              {/* Room Details Skeletons */}
              <Stack spacing={1}>
                <Skeleton variant="text" width="100%" height={20} animation="wave" />
                <Skeleton variant="text" width="80%" height={20} animation="wave" />
                <Skeleton variant="text" width="60%" height={20} animation="wave" />
              </Stack>
            </Box>

            {/* Right Part - Pricing Options */}
            <Box sx={{ flex: 1, ml: 2 }}>
              <Stack spacing={2}>
                {/* Price Options Skeletons */}
                {[1, 2, 3].map((index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Skeleton variant="text" width={60} height={24} animation="wave" />
                    <Skeleton variant="text" width={80} height={24} animation="wave" />
                    <Skeleton variant="rectangular" width={100} height={36} sx={{ borderRadius: 1 }} animation="wave" />
                  </Box>
                ))}
              </Stack>
            </Box>
          </Box>
        </Box>
      </Paper>
    );
  }

  // Desktop Layout
  return (
    <Paper
      elevation={2}
      sx={{
        maxWidth: '1376px',
        width: '100%',
        border: '1px solid',
        borderColor: '#E0E0E0',
        borderRadius: 2,
        overflow: 'hidden',
      }}
    >
      <Box sx={{ p: 3 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          {/* Title Skeleton */}
          <Box sx={{ flex: 1 }}>
            <Skeleton variant="text" width="60%" height={32} sx={{ mb: 1 }} animation="wave" />
            <Skeleton variant="text" width="40%" height={20} animation="wave" />
          </Box>

          {/* View Photos Button Skeleton */}
          <Skeleton
            variant="rectangular"
            width={120}
            height={40}
            sx={{ borderRadius: 1 }}
            animation="wave"
          />
        </Box>

        {/* Content Row */}
        <Box sx={{ display: 'flex', gap: 3 }}>
          {/* Left Part - Image Gallery */}
          <Box sx={{ width: '300px', flexShrink: 0 }}>
            {/* Main Image Skeleton */}
            <Skeleton
              variant="rectangular"
              width={300}
              height={200}
              sx={{ borderRadius: 1, mb: 2 }}
              animation="wave"
            />

            {/* Thumbnail Images Skeleton */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[1, 2, 3].map((index) => (
                <Skeleton
                  key={index}
                  variant="rectangular"
                  width={80}
                  height={60}
                  sx={{ borderRadius: 1 }}
                  animation="wave"
                />
              ))}
            </Box>
          </Box>

          {/* Center Part - Room Details */}
          <Box sx={{ flex: 1 }}>
            <Stack spacing={2}>
              {/* Room Details Skeletons */}
              <Skeleton variant="text" width="100%" height={24} animation="wave" />
              <Skeleton variant="text" width="80%" height={20} animation="wave" />
              <Skeleton variant="text" width="70%" height={20} animation="wave" />
              <Skeleton variant="text" width="90%" height={20} animation="wave" />
            </Stack>
          </Box>

          {/* Right Part - Pricing Options */}
          <Box sx={{ width: '250px', flexShrink: 0 }}>
                          <Stack spacing={2}>
                {/* Price Options Skeletons */}
                {[1, 2, 3, 4].map((index) => (
                  <Box key={index} sx={{ textAlign: 'center' }}>
                    <Skeleton variant="text" width="100%" height={20} sx={{ mb: 1 }} animation="wave" />
                    <Skeleton variant="text" width="80%" height={24} sx={{ mb: 1 }} animation="wave" />
                    <Skeleton
                      variant="rectangular"
                      width="100%"
                      height={40}
                      sx={{ borderRadius: 1 }}
                      animation="wave"
                    />
                  </Box>
                ))}
              </Stack>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default RoomSkeleton;
