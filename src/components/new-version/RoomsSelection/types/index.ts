import { TransformImage } from '@/actions/getRoomImages/types';
import { PriceOption } from '@/constant/outlet';

export type RoomName = string;

export type SelectedRooms = {
  [k: RoomName]: {
    quantity: number;
    room: {
      name: string;
      selectedOption: PriceOption;
      availableOptions: PriceOption[];
      bedType: string;
      capacity: string;
      images: TransformImage[];
      roomDetails: { [k: string]: any };
      [k: string]: any;
    };
  };
};

export type RoomPriceOption = {
  hours: number;
  price: number;
  isBestDeal?: boolean;
};

export type RoomSelectionDetail = {
  title: string;
  capacity: number;
  bedType: string;
  selectedOption: RoomPriceOption;
  roomPriceOptions: RoomPriceOption[];
  images: TransformImage[];
  roomDetails: {
    prices: RoomPriceOption[];
    [k: string]: any;
  };
  [k: string]: any;
};
