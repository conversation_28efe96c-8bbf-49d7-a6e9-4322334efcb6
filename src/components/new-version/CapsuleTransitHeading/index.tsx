import React from 'react';
import { Typography, But<PERSON>, Box, Container, Stack } from '@mui/material';
import { OUTLETS } from '@/constant/outlet';
import AccessibleButton from '../AccessibleButton';

interface CapsuleTransitHeadingProps {
  lotId?: number;
}

const CapsuleTransitHeading: React.FC<CapsuleTransitHeadingProps> = ({
  lotId,
}) => {
  const outlet = OUTLETS.find((outlet) => outlet.lotId === lotId);
  const title = outlet?.title;
  const description = outlet?.description;

  return (
    <Container maxWidth='md'>
      <Box
        display='flex'
        flexDirection='column'
        alignItems='center'
        textAlign='center'
        py={6}
        px={2}
      >
        {/* Main Title */}
        <Typography
          variant='h2'
          component='h1'
          color='text.primary'
          fontWeight='bold'
          mb={2}
          sx={{
            fontSize: { xs: '2rem', md: '2.5rem' },
          }}
        >
          {title}
        </Typography>

        {/* Description */}
        <Typography
          variant='body1'
          color='text.primary'
          mb={4}
          maxWidth='md'
          sx={{
            fontSize: { xs: '1rem', md: '1.125rem' },
          }}
        >
          {description}
        </Typography>

        {/* Buttons Container */}

        <Stack direction='row' spacing={1} sx={{ mb: 1.5 }}>
          <Button
            variant='contained'
            size='small'
            sx={{
              bgcolor:
                outlet?.location === 'Terminal 1' ? '#FFF1AC' : '#D9F2EF',
              color: '#0F0E0E',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '4px',
              px: 2,
              py: 0.5,
              '&:hover': {
                bgcolor:
                  outlet?.location === 'Terminal 1' ? '#FFF1AC' : '#D9F2EF',
              },
            }}
          >
            {outlet?.terminal}
          </Button>
          <AccessibleButton isPublicArea={outlet?.isPublicArea} />
        </Stack>
      </Box>
    </Container>
  );
};

export default CapsuleTransitHeading;
