import { formatCheckoutDisplay } from '@/utils/time-selection';
import { Typography } from '@mui/material';
import React from 'react';

const CheckoutTime = ({ checkoutTime }: { checkoutTime: Date }) => {
  return (
    <Typography sx={{ fontSize: '14px', marginTop: '4px' }}>
      <span style={{ color: '#989CA4' }}>Check-out at</span>{' '}
      <span style={{ color: '#1A1A1A', fontWeight: 600 }}>
        {formatCheckoutDisplay(checkoutTime)}
      </span>
    </Typography>
  );
};

export default CheckoutTime;
