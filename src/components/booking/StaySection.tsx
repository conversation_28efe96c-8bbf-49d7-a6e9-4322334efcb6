import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useState, VoidFunctionComponent, useEffect } from 'react';
import CTButton from '../global/CTButton';
import { ChevronLeft, Help, HelpOutline } from '@mui/icons-material';
import Image, { StaticImageData } from 'next/image';
import IconArrowRight from '@/assets/icons/general/btn-icon-arrow-left.svg';
import { IBookingLocation } from '@/models/Booking';
import { useRouter } from 'next/navigation';

import T1Icon from './images/Icons/t1-icon.svg';
import T2Icon from './images/Icons/t2-icon.svg';
import TrainBusGIF from './images/CT-transport.gif';

import Terminal1DetailedIcon from './images/Icons/terminal-1-detailed-icon.svg';
import Terminal1DetailedIconHovered from './images/Icons/terminal-1-detailed-icon-hover.svg';
import RestrictedAreaIcon from './images/Icons/restricted-area-icon.svg';
import RestrictedAreaIconHovered from './images/Icons/restricted-area-icon-hover.svg';
import PublicAreaIcon from './images/Icons/public-area-icon.svg';
import PublicAreaIconHovered from './images/Icons/public-area-icon-hover.svg';

interface IKLIAOptions {
  terminalName: string;
  location: {
    icon: StaticImageData;
    iconHovered: StaticImageData;
    name: string;
    description?: string;
    remarks: string[];
    hotels: IHotel[];
  }[];
}

interface IHotel {
  name: string;
  detailedLocation: string;
}

const options = [{ title: 'KLIA Terminal 1' }, { title: 'KLIA Terminal 2' }];

const KLIA1Options: IKLIAOptions = {
  terminalName: 'KLIA Terminal 1',
  location: [
    {
      icon: Terminal1DetailedIcon,
      iconHovered: Terminal1DetailedIconHovered,
      name: 'Public Area',
      description:
        'At KLIA Terminal 1, our hotel provides a single-service sleep-only lounge for you to rest and take a nap',
      remarks: [
        'If you are landing in Malaysia, you will need valid entry into Malaysia (visa, landing card) to clear Malaysian Imigration and access our Sleep Lounge',
      ],
      hotels: [
        {
          name: 'Sleep Lounge',
          detailedLocation:
            'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia',
        },
      ],
    },
  ],
};

const KLIA2Options: IKLIAOptions = {
  terminalName: 'KLIA Terminal 2',
  location: [
    {
      icon: RestrictedAreaIcon,
      iconHovered: RestrictedAreaIconHovered,
      name: 'Restricted Area',
      remarks: [
        'If you are transiting between international flights, you can check into this hotel without needing to clear immigration.',
      ],
      hotels: [
        {
          name: 'Airside',
          detailedLocation:
            'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia',
        },
      ],
    },
    {
      icon: PublicAreaIcon,
      iconHovered: PublicAreaIconHovered,
      name: 'Public Area',
      remarks: [
        'If you are landing in Malaysia, you will need valid entry into Malaysia (visa, landing card) to clear Malaysian Imigration and access our Landside and MAX',
      ],
      hotels: [
        {
          name: 'Landside',
          detailedLocation:
            'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia',
        },
        {
          name: 'MAX',
          detailedLocation:
            'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia',
        },
      ],
    },
  ],
};

const StaySection = (props: {
  handleChangeSelectedHotel: (value: IBookingLocation) => void;
}) => {
  const [staySectionStepper, setStaySectionStepper] = useState<string>('');
  const router = useRouter();

  useEffect(() => {
    // Handle URL hash for direct booking
    const handleHashChange = () => {
      const hash = window.location.hash.substring(1); // Remove the # symbol
      if (hash) {
        const [terminal, outlet] = hash.split('-');
        if (terminal && outlet) {
          let hotelLocation = '';
          let hotelName = '';
          let detailedLocation = '';

          // Map the hash to the correct hotel
          switch (hash) {
            case 't1-sleeplounge':
              hotelLocation = 'KLIA Terminal 1';
              hotelName = 'Sleep Lounge';
              detailedLocation = 'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia';
              break;
            case 't2-airside':
              hotelLocation = 'KLIA Terminal 2';
              hotelName = 'Airside';
              detailedLocation = 'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia';
              break;
            case 't2-landside':
              hotelLocation = 'KLIA Terminal 2';
              hotelName = 'Landside';
              detailedLocation = 'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia';
              break;
            case 't2-max':
              hotelLocation = 'KLIA Terminal 2';
              hotelName = 'MAX';
              detailedLocation = 'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia';
              break;
          }

          if (hotelLocation && hotelName) {
            props.handleChangeSelectedHotel({
              hotelLocation,
              hotelName,
              hotelDetailedLocation: detailedLocation,
            });
          }
        }
      }
    };

    // Check hash on initial load
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, [props.handleChangeSelectedHotel]);

  const handleChangeStaySectionStepper = (title: string) => {
    setStaySectionStepper(title);
  };

  if (staySectionStepper === '') {
    return (
      <StaySectionHome
        handleChangeStaySectionStepper={handleChangeStaySectionStepper}
      />
    );
  } else if (staySectionStepper === options[0].title) {
    return (
      <KLIAStaySection
        content={KLIA1Options}
        handleChangeStaySectionStepper={handleChangeStaySectionStepper}
        handleChangeSelectedHotel={props.handleChangeSelectedHotel}
      />
    );
  } else if (staySectionStepper === options[1].title) {
    return (
      <KLIAStaySection
        content={KLIA2Options}
        handleChangeStaySectionStepper={handleChangeStaySectionStepper}
        handleChangeSelectedHotel={props.handleChangeSelectedHotel}
      />
    );
  }
};

const informationText =
  'If you wish to travel between KLIA Terminal 1 and Terminal 2, you can take either the free shuttle bus service (30 minutes ride) or ERL (Train 10 minutes ride).';

const StaySectionHome = (props: {
  handleChangeStaySectionStepper: (title: string) => void;
}) => {
  const theme = useTheme();
  const isHandheldDevice = useMediaQuery('(max-width:1050px)');
  const isSmallDevice = useMediaQuery('(max-width:400px)');

  const [informationHovered, setInformationHovered] = useState<boolean>(false);
  return (
    <Box
      display={'flex'}
      flexDirection={'column'}
      height={'100%'}
      justifyContent={'center'}
      alignItems={'center'}
      paddingY={isHandheldDevice ? 6 : 10}
    >
      <Typography
        variant="h4"
        textAlign={'center'}
        width={isHandheldDevice ? '350px' : '100%'}
      >
        Where are you departing / landing?
      </Typography>
      <Stack direction={'row'} spacing={isHandheldDevice ? 1 : '70px'}>
        <Box
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
          width={'35%'}
          height={'600px'}
        >
          <Box
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            marginBottom={2}
          >
            <Image
              src={T1Icon}
              alt="terminal-1-icon"
              style={{ width: isHandheldDevice ? 100 : 150, height: 'auto' }}
            />
          </Box>
          <Typography variant="h4" textAlign={'center'} marginBottom={5}>
            {options[0].title}
          </Typography>
          <CTButton
            onClick={() =>
              props.handleChangeStaySectionStepper(options[0].title)
            }
            text={isHandheldDevice ? 'Select' : 'SELECT LOCATION'}
            variant="secondary"
          />
        </Box>
        <Box
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
          alignItems={'center'}
          width={'30%'}
        >
          <Image
            src={TrainBusGIF}
            alt="transit-icon"
            style={{
              marginBottom: '50px',
              objectFit: 'contain',
              width: isHandheldDevice ? '90%' : '100%',
              height: 'auto',
            }}
          />
          <Box
            display={'flex'}
            flexDirection={'column'}
            onMouseEnter={() => setInformationHovered(true)}
            onMouseLeave={() => setInformationHovered(false)}
            justifyContent={'center'}
            alignItems={'center'}
          >
            {informationHovered ? (
              <HelpOutline color="primary" />
            ) : (
              <Help color="primary" />
            )}
            {informationHovered && (
              <Box
                position={'absolute'}
                marginTop={
                  isSmallDevice ? '380px' : isHandheldDevice ? '330px' : '20vh'
                }
                display={'flex'}
                border={1}
                width={'300px'}
                bgcolor={theme.palette.primary.main}
                padding={2}
                zIndex={10}
              >
                {informationText}
              </Box>
            )}
          </Box>
        </Box>
        <Box
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
          width={'35%'}
          height={'600px'}
        >
          <Box
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            marginBottom={2}
          >
            <Image
              src={T2Icon}
              alt="terminal-2-icon"
              style={{ width: isHandheldDevice ? 100 : 150, height: 'auto' }}
            />
          </Box>
          <Typography variant="h4" textAlign={'center'} marginBottom={5}>
            {options[1].title}
          </Typography>
          <CTButton
            onClick={() =>
              props.handleChangeStaySectionStepper(options[1].title)
            }
            text={isHandheldDevice ? 'Select' : 'SELECT LOCATION'}
            variant="secondary"
          />
        </Box>
      </Stack>
    </Box>
  );
};

const KLIAStaySection = (props: {
  content: IKLIAOptions;
  handleChangeStaySectionStepper: (title: string) => void;
  handleChangeSelectedHotel: (value: IBookingLocation) => void;
}) => {
  const isHandheldDevice = useMediaQuery('(max-width:1050px)');
  const [hover, setHovered] = useState<string>('');

  const [unavailableDialogOpen, setUnavailableDialogOpen] =
    useState<boolean>(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] =
    useState<IHotel>();

  const handleOpenUnavailableDialog = () => {
    setUnavailableDialogOpen(true);
  };

  const handleCloseUnavailableDialog = () => {
    setUnavailableDialogOpen(false);
  };

  const handleOpenConfirmationDialog = (hotelName: IHotel) => {
    setConfirmationDialogOpen(hotelName);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(undefined);
  };

  const onHotelSelect = (hotel: IHotel) => {
    props.handleChangeSelectedHotel({
      hotelLocation: props.content.terminalName,
      hotelName: hotel.name,
      hotelDetailedLocation: hotel.detailedLocation,
    });
  };

  return (
    <Box
      display={'flex'}
      flexDirection={'column'}
      height={'100%'}
      justifyContent={'center'}
      alignItems={'center'}
      paddingY={isHandheldDevice ? 6 : 10}
      paddingX={3}
    >
      <Stack
        direction={isHandheldDevice ? 'column' : 'row'}
        width={'100%'}
        marginBottom={6}
        alignItems={isHandheldDevice ? 'center' : 'start'}
      >
        <Box display={'flex'} width={isHandheldDevice ? '70px' : '33%'}>
          <Button onClick={() => props.handleChangeStaySectionStepper('')}>
            <ChevronLeft />
            <Typography variant="h6" color={'primary'} fontWeight={600}>
              Back
            </Typography>
          </Button>
        </Box>
        <Box
          display={'flex'}
          justifyContent={'center'}
          alignItems={'center'}
          width={isHandheldDevice ? '100%' : '33%'}
        >
          <Typography variant="h4">{props.content.terminalName}</Typography>
        </Box>
      </Stack>
      <Stack
        direction={isHandheldDevice ? 'column' : 'row'}
        width={'100%'}
        justifyContent={'center'}
        alignItems={isHandheldDevice ? 'center' : 'start'}
        spacing={5}
      >
        {props.content.location.map((location, index) => (
          <Box
            key={index}
            display={'flex'}
            flexDirection={'column'}
            alignItems={'center'}
            marginTop={10}
            width={isHandheldDevice ? '350px' : '480px'}
            onMouseEnter={() => setHovered(location.name)}
            onMouseLeave={() => setHovered('')}
          >
            <Stack spacing={2}>
              <Box display={'flex'} justifyContent={'center'} height={'80px'}>
                {hover === location.name ? (
                  <Image src={location.iconHovered} alt={location.name} />
                ) : (
                  <Image src={location.icon} alt={location.name} />
                )}
              </Box>
              <Typography variant="h6" fontWeight={600} textAlign={'center'}>
                {location.name}
              </Typography>
              <Typography textAlign={'center'}>
                {location.description}
              </Typography>
              {location.remarks.map((remark, index) => (
                <Typography
                  key={index}
                  textAlign={'center'}
                  marginTop={index > 0 ? 4 : 0}
                >
                  {remark}
                </Typography>
              ))}
            </Stack>
            <Stack spacing={3} marginTop={isHandheldDevice ? 6 : 10}>
              {location.hotels.map((hotel, index) => (
                <Button
                  onClick={() => handleOpenConfirmationDialog(hotel)}
                  key={index}
                >
                  <Typography variant="h4" marginRight={3}>
                    {hotel.name}
                  </Typography>
                  <Image src={IconArrowRight} alt="CT-Right-Up" />
                </Button>
              ))}
            </Stack>
            {isHandheldDevice &&
              props.content.location.length - 1 !== index && (
                <Divider
                  variant="middle"
                  sx={{
                    width: '350px',
                    marginTop: 4,
                  }}
                />
              )}
          </Box>
        ))}
      </Stack>
      <UnavailableBookingDialog
        open={unavailableDialogOpen}
        handleClose={handleCloseUnavailableDialog}
        handleChangeSelectedHotel={props.handleChangeSelectedHotel}
        terminalName={'KLIA Terminal 1'}
        hotelName={'Sleep Lounge'}
        detailedLocation={
          'Lot L1-1, 2, 3 & 4, Level 1, Gateway Terminal KLIA2, Jalan KLIA 2/1, 64000 KLIA, Sepang, Selangor, Malaysia'
        }
      />
      <ConfirmationModal
        open={confirmationDialogOpen}
        handleClose={handleCloseConfirmationDialog}
        redirectFunction={onHotelSelect}
      />
    </Box>
  );
};

const UnavailableBookingDialog = (props: {
  open: boolean;
  handleClose: VoidFunction;
  handleChangeSelectedHotel: (value: IBookingLocation) => void;
  terminalName: string;
  hotelName: string;
  detailedLocation: string;
}) => {
  const [password, setPassword] = useState<string>('');

  const handleGo = () => {
    if (password === 'ASDFGYHJKL')
      props.handleChangeSelectedHotel({
        hotelLocation: props.terminalName,
        hotelName: props.hotelName,
        hotelDetailedLocation: props.detailedLocation,
      });
  };
  return (
    <Dialog open={props.open} onClose={props.handleClose}>
      <DialogTitle fontSize={'1.5rem'} fontWeight={700}>
        We are putting on our final touches for Terminal 1.
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Please email us at{' '}
          <a href="mailto:<EMAIL>"><EMAIL></a>{' '}
          for assistance with your reservations. Thank you for
          your understanding.
        </DialogContentText>
        <Stack direction={'row'} display={'flex'} marginTop={4}>
          <TextField
            size="small"
            value={password}
            onChange={(event) => setPassword(event.target.value)}
          />
          <Button onClick={handleGo}>Go</Button>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

const confirmationModalTexts = {
  title: 'Document Confirmation',
  confirmButton: 'OK',
  backButton: 'Back',
};

const ConfirmationModal = (props: {
  open?: IHotel;
  handleClose: VoidFunction;
  redirectFunction: (hotel: IHotel) => void;
}) => {
  const onConfirmClick = () => {
    if (props.open) {
      props.redirectFunction(props.open);
    }
  };
  return (
    <Dialog open={Boolean(props.open)} onClose={props.handleClose}>
      <DialogTitle fontSize={'1.5rem'} fontWeight={700}>
        <Stack>{confirmationModalTexts.title}</Stack>
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          If you are <b>transiting</b>, please check that you have the proper
          visa clearance to exit immigration to access this hotel.
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          onClick={props.handleClose}
          sx={{
            '&:hover': {
              backgroundColor: 'CtColorScheme.neon500',
            },
          }}
        >
          {confirmationModalTexts.backButton}
        </Button>
        <Button
          variant="contained"
          onClick={onConfirmClick}
          sx={{
            '&:hover': {
              backgroundColor: 'CtColorScheme.neon500',
            },
          }}
        >
          {confirmationModalTexts.confirmButton}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StaySection;
