export const TimeSelection = [
  '00:00',
  '00:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
  '04:00',
  '04:30',
  '05:00',
  '05:30',
  '06:00',
  '06:30',
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
];

export const getCompatibleTimes = (date: Date) => {
  const now = new Date();

  return TimeSelection.filter((time) => {
    // Convert current time to Malaysia time (UTC+8)
    const malaysiaTimeOffset = 8 * 60; // Malaysia is UTC+8
    const localOffset = now.getTimezoneOffset(); // Get the local offset in minutes
    const malaysiaTime = new Date(
      now.getTime() + (malaysiaTimeOffset + localOffset) * 60000
    );

    // Format Malaysia time as HH:mm
    const comparisonTime = malaysiaTime.toTimeString().slice(0, 5);
    
    // Create date objects for proper comparison
    const selectedDateOnly = new Date(date);
    selectedDateOnly.setHours(0, 0, 0, 0);
    
    const todayOnly = new Date(malaysiaTime);
    todayOnly.setHours(0, 0, 0, 0);
    
    // If the selected date is today, filter out past times
    if (selectedDateOnly.getTime() === todayOnly.getTime()) {
      // Add some buffer time (e.g., 30 minutes) to account for booking processing
      const [hours, minutes] = comparisonTime.split(':').map(Number);
      const currentMinutes = hours * 60 + minutes + 30; // Add 30 minutes buffer
      const [timeHours, timeMinutes] = time.split(':').map(Number);
      const timeInMinutes = timeHours * 60 + timeMinutes;
      
      return timeInMinutes >= currentMinutes;
    }

    // For future dates, all times are available
    return true;
  });
};
