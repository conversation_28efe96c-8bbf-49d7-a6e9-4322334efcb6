<svg width="300" height="118" viewBox="0 0 300 118" fill="none" xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink">
    <rect opacity="0.3" width="300" height="80" fill="url(#pattern0_7071_75070)" />
    <g clip-path="url(#clip0_7071_75070)">
        <path opacity="0.3"
            d="M150 118C154.971 118 159 113.971 159 109C159 104.029 154.971 100 150 100C145.029 100 141 104.029 141 109C141 113.971 145.029 118 150 118Z"
            fill="#00DEBE" />
        <path opacity="0.3"
            d="M151.493 110.967C151.64 111.078 151.787 111.189 151.966 111.324C151.366 112.252 150.822 113.203 149.617 113.412C148.512 113.604 147.868 112.918 148.16 111.828C148.384 110.991 148.677 110.172 148.94 109.347C148.948 109.322 148.964 109.299 148.972 109.274C149.247 108.401 149.157 108.271 148.259 108.244C148.207 108.243 148.155 108.234 148.062 108.224C148.115 108.009 148.163 107.809 148.224 107.555H151.431C151.359 107.805 151.288 108.07 151.208 108.332C150.852 109.507 150.489 110.679 150.143 111.858C150.101 112.002 150.155 112.174 150.165 112.332C150.314 112.261 150.505 112.228 150.603 112.113C150.908 111.761 151.177 111.379 151.492 110.967H151.493Z"
            fill="white" />
        <path opacity="0.3"
            d="M150.716 106.716C150.127 106.714 149.641 106.227 149.642 105.642C149.644 105.062 150.142 104.558 150.717 104.555C151.3 104.551 151.779 105.043 151.778 105.642C151.777 106.24 151.304 106.718 150.716 106.716Z"
            fill="white" />
    </g>
    <defs>
        <pattern id="pattern0_7071_75070" patternContentUnits="objectBoundingBox" width="1"
            height="1">
            <use xlink:href="#image0_7071_75070" transform="scale(0.00166667 0.00625)" />
        </pattern>
        <clipPath id="clip0_7071_75070">
            <rect width="18" height="18" fill="white" transform="translate(141 100)" />
        </clipPath>
        <image id="image0_7071_75070" width="600" height="160" preserveAspectRatio="none"
            xlink:href="data:image/png;base64,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" />
    </defs>
</svg>
    