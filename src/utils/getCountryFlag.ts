// Convert 3-letter codes to 2-letter ISO codes
const iso3to2: Record<string, string> = {
  UZB: 'UZ',
  COL: 'CO',
  PAN: 'PA',
  BLM: 'BL',
  PRT: 'PT',
  BWA: 'BW',
  LBN: 'LB',
  BRB: 'BB',
  SLB: 'SB',
  ANT: 'AN', // deprecated but still used
  NCL: 'NC',
  SUR: 'SR',
  PCN: 'PN',
  ATG: 'AG',
  STP: 'ST',
  NZL: 'NZ',
  UGA: 'UG',
  NRU: 'NR',
  COG: 'CG',
  FRA: 'FR',
  SYR: 'SY',
  VGB: 'VG',
  BGD: 'BD',
  DMA: 'DM',
  ISR: 'IL',
  MAC: 'MO',
  LCA: 'LC',
  AGO: 'AO',
  MUS: 'MU',
  TKL: 'TK',
  SYC: 'SC',
  GRL: 'GL',
  LIE: 'LI',
  MDA: 'MD',
  AUS: 'AU',
  FJI: 'FJ',
  GIN: 'GN',
  PHL: 'PH',
  ATA: 'AQ',
  GRD: 'GD',
  GTM: 'GT',
  PRI: 'PR',
  MAR: 'MA',
  TLS: 'TL',
  AUT: 'AT',
  ESH: 'EH',
  SHN: 'SH',
  KWT: 'KW',
  MLI: 'ML',
  UKR: 'UA',
  CPV: 'CV',
  SRB: 'RS',
  JAM: 'JM',
  TCA: 'TC',
  PAK: 'PK',
  TCD: 'TD',
  USA: 'US',
  EST: 'EE',
  COM: 'KM',
  LTU: 'LT',
  NPL: 'NP',
  IRN: 'IR',
  IOT: 'IO',
  OMN: 'OM',
  SPM: 'PM',
  TUR: 'TR',
  TUV: 'TV',
  IND: 'IN',
  HND: 'HN',
  BHR: 'BH',
  MDV: 'MV',
  GGY: 'GG',
  BEL: 'BE',
  ROU: 'RO',
  IMN: 'IM',
  LBY: 'LY',
  NIU: 'NU',
  MLT: 'MT',
  TUN: 'TN',
  YEM: 'YE',
  FLK: 'FK',
  THA: 'TH',
  CAF: 'CF',
  NAM: 'NA',
  SLV: 'SV',
  MNE: 'ME',
  ALB: 'AL',
  MOZ: 'MZ',
  HKG: 'HK',
  HRV: 'HR',
  ARM: 'AM',
  BFA: 'BF',
  MYT: 'YT',
  SOM: 'SO',
  TTO: 'TT',
  MAF: 'MF',
  FIN: 'FI',
  MWI: 'MW',
  BTN: 'BT',
  ESP: 'ES',
  KNA: 'KN',
  ARG: 'AR',
  GHA: 'GH',
  CZE: 'CZ',
  MDG: 'MG',
  HUN: 'HU',
  LBR: 'LR',
  SXM: 'SX',
  CMR: 'CM',
  TON: 'TO',
  GUY: 'GY',
  GNQ: 'GQ',
  MSR: 'MS',
  CRI: 'CR',
  CAN: 'CA',
  PYF: 'PF',
  SGP: 'SG',
  CHN: 'CN',
  JPN: 'JP',
  JOR: 'JO',
  ASM: 'AS',
  TGO: 'TG',
  DOM: 'DO',
  TZA: 'TZ',
  SWZ: 'SZ',
  QAT: 'QA',
  WSM: 'WS',
  VNM: 'VN',
  SDN: 'SD',
  SVK: 'SK',
  FSM: 'FM',
  AZE: 'AZ',
  NLD: 'NL',
  KEN: 'KE',
  KOR: 'KR',
  VAT: 'VA',
  PLW: 'PW',
  DEU: 'DE',
  SWE: 'SE',
  CUW: 'CW',
  HTI: 'HT',
  LVA: 'LV',
  BLZ: 'BZ',
  TWN: 'TW',
  KIR: 'KI',
  IDN: 'ID',
  CUB: 'CU',
  BHS: 'BS',
  PER: 'PE',
  MEX: 'MX',
  URY: 'UY',
  SMR: 'SM',
  PRK: 'KP',
  SSD: 'SS',
  GMB: 'GM',
  REU: 'RE',
  EGY: 'EG',
  MNG: 'MN',
  NOR: 'NO',
  CHL: 'CL',
  XKX: 'XK', // not official
  ERI: 'ER',
  VUT: 'VU',
  LAO: 'LA',
  BRA: 'BR',
  GUM: 'GU',
  CYP: 'CY',
  TJK: 'TJ',
  GBR: 'GB',
  NER: 'NE',
  MMR: 'MM',
  ECU: 'EC',
  BOL: 'BO',
  KAZ: 'KZ',
  GRC: 'GR',
  AND: 'AD',
  SVN: 'SI',
  ETH: 'ET',
  MRT: 'MR',
  NIC: 'NI',
  SLE: 'SL',
  IRQ: 'IQ',
  FRO: 'FO',
  VEN: 'VE',
  BEN: 'BJ',
  ZWE: 'ZW',
  MHL: 'MH',
  ZAF: 'ZA',
  GNB: 'GW',
  RUS: 'RU',
  AFG: 'AF',
  SEN: 'SN',
  JEY: 'JE',
  SAU: 'SA',
  DNK: 'DK',
  LKA: 'LK',
  PNG: 'PG',
  LUX: 'LU',
  TKM: 'TM',
  RWA: 'RW',
  IRL: 'IE',
  DJI: 'DJ',
  BIH: 'BA',
  VCT: 'VC',
  CXR: 'CX',
  ARE: 'AE',
  SJM: 'SJ',
  ZMB: 'ZM',
  NGA: 'NG',
  CIV: 'CI',
  BMU: 'BM',
  VIR: 'VI',
  ABW: 'AW',
  ISL: 'IS',
  KGZ: 'KG',
  GAB: 'GA',
  ITA: 'IT',
  AIA: 'AI',
  PRY: 'PY',
  COK: 'CK',
  BGR: 'BG',
  GIB: 'GI',
  CYM: 'KY',
  COD: 'CD',
  CCK: 'CC',
  BRN: 'BN',
  KHM: 'KH',
  DZA: 'DZ',
  GEO: 'GE',
  BLR: 'BY',
  MKD: 'MK',
  CHE: 'CH',
  BDI: 'BI',
  WLF: 'WF',
  POL: 'PL',
  PSE: 'PS',
  MCO: 'MC',
  LSO: 'LS',
  MYS: 'MY',
  MNP: 'MP',
  NFK: 'NF', // Norfolk Island
  HMD: 'HM', // Heard Island and McDonald Islands
  UMI: 'UM', // United States Minor Outlying Islands
  BVT: 'BV', // Bouvet Island
  SGS: 'GS', // South Georgia and the South Sandwich Islands
  ATF: 'TF', // French Southern Territories
};

const countryToCode: Record<string, string> = {
  // A
  afghanistan: 'AF',
  albania: 'AL',
  algeria: 'DZ',
  andorra: 'AD',
  angola: 'AO',
  'antigua and barbuda': 'AG',
  argentina: 'AR',
  armenia: 'AM',
  australia: 'AU',
  austria: 'AT',
  azerbaijan: 'AZ',

  // B
  bahamas: 'BS',
  bahrain: 'BH',
  bangladesh: 'BD',
  barbados: 'BB',
  belarus: 'BY',
  belgium: 'BE',
  belize: 'BZ',
  benin: 'BJ',
  bhutan: 'BT',
  bolivia: 'BO',
  'bosnia and herzegovina': 'BA',
  botswana: 'BW',
  brazil: 'BR',
  brunei: 'BN',
  bulgaria: 'BG',
  'burkina faso': 'BF',
  burundi: 'BI',

  // C
  'cabo verde': 'CV',
  cambodia: 'KH',
  cameroon: 'CM',
  canada: 'CA',
  'central african republic': 'CF',
  chad: 'TD',
  chile: 'CL',
  china: 'CN',
  colombia: 'CO',
  comoros: 'KM',
  congo: 'CG',
  'costa rica': 'CR',
  croatia: 'HR',
  cuba: 'CU',
  cyprus: 'CY',
  'czech republic': 'CZ',
  czechia: 'CZ',

  // D
  'democratic republic of the congo': 'CD',
  denmark: 'DK',
  djibouti: 'DJ',
  dominica: 'DM',
  'dominican republic': 'DO',

  // E
  ecuador: 'EC',
  egypt: 'EG',
  'el salvador': 'SV',
  'equatorial guinea': 'GQ',
  eritrea: 'ER',
  estonia: 'EE',
  eswatini: 'SZ',
  ethiopia: 'ET',

  // F
  fiji: 'FJ',
  finland: 'FI',
  france: 'FR',

  // G
  gabon: 'GA',
  gambia: 'GM',
  georgia: 'GE',
  germany: 'DE',
  ghana: 'GH',
  greece: 'GR',
  grenada: 'GD',
  guatemala: 'GT',
  guinea: 'GN',
  'guinea-bissau': 'GW',
  guyana: 'GY',

  // H
  haiti: 'HT',
  honduras: 'HN',
  hungary: 'HU',

  // I
  iceland: 'IS',
  india: 'IN',
  indonesia: 'ID',
  iran: 'IR',
  iraq: 'IQ',
  ireland: 'IE',
  israel: 'IL',
  italy: 'IT',
  'ivory coast': 'CI',

  // J
  jamaica: 'JM',
  japan: 'JP',
  jordan: 'JO',

  // K
  kazakhstan: 'KZ',
  kenya: 'KE',
  kiribati: 'KI',
  kuwait: 'KW',
  kyrgyzstan: 'KG',

  // L
  laos: 'LA',
  latvia: 'LV',
  lebanon: 'LB',
  lesotho: 'LS',
  liberia: 'LR',
  libya: 'LY',
  liechtenstein: 'LI',
  lithuania: 'LT',
  luxembourg: 'LU',

  // M
  madagascar: 'MG',
  malawi: 'MW',
  malaysia: 'MY',
  maldives: 'MV',
  mali: 'ML',
  malta: 'MT',
  'marshall islands': 'MH',
  mauritania: 'MR',
  mauritius: 'MU',
  mexico: 'MX',
  micronesia: 'FM',
  moldova: 'MD',
  monaco: 'MC',
  mongolia: 'MN',
  montenegro: 'ME',
  morocco: 'MA',
  mozambique: 'MZ',
  myanmar: 'MM',

  // N
  namibia: 'NA',
  nauru: 'NR',
  nepal: 'NP',
  netherlands: 'NL',
  'new zealand': 'NZ',
  nicaragua: 'NI',
  niger: 'NE',
  nigeria: 'NG',
  'north korea': 'KP',
  'north macedonia': 'MK',
  norway: 'NO',

  // O
  oman: 'OM',

  // P
  pakistan: 'PK',
  palau: 'PW',
  panama: 'PA',
  'papua new guinea': 'PG',
  paraguay: 'PY',
  peru: 'PE',
  philippines: 'PH',
  poland: 'PL',
  portugal: 'PT',

  // Q
  qatar: 'QA',

  // R
  romania: 'RO',
  russia: 'RU',
  rwanda: 'RW',

  // S
  'saint kitts and nevis': 'KN',
  'saint lucia': 'LC',
  'saint vincent and the grenadines': 'VC',
  samoa: 'WS',
  'san marino': 'SM',
  'sao tome and principe': 'ST',
  'saudi arabia': 'SA',
  senegal: 'SN',
  serbia: 'RS',
  seychelles: 'SC',
  'sierra leone': 'SL',
  singapore: 'SG',
  slovakia: 'SK',
  slovenia: 'SI',
  'solomon islands': 'SB',
  somalia: 'SO',
  'south africa': 'ZA',
  'south korea': 'KR',
  'south sudan': 'SS',
  spain: 'ES',
  'sri lanka': 'LK',
  sudan: 'SD',
  suriname: 'SR',
  sweden: 'SE',
  switzerland: 'CH',
  syria: 'SY',

  // T
  taiwan: 'TW',
  tajikistan: 'TJ',
  tanzania: 'TZ',
  thailand: 'TH',
  'timor-leste': 'TL',
  togo: 'TG',
  tonga: 'TO',
  'trinidad and tobago': 'TT',
  tunisia: 'TN',
  turkey: 'TR',
  turkmenistan: 'TM',
  tuvalu: 'TV',

  // U
  uganda: 'UG',
  ukraine: 'UA',
  'united arab emirates': 'AE',
  'united kingdom': 'GB',
  'united states': 'US',
  uruguay: 'UY',
  uzbekistan: 'UZ',

  // V
  vanuatu: 'VU',
  'vatican city': 'VA',
  venezuela: 'VE',
  vietnam: 'VN',

  // Y
  yemen: 'YE',

  // Z
  zambia: 'ZM',
  zimbabwe: 'ZW',

  // Additional territories and commonly used names
  england: 'GB',
  scotland: 'GB',
  wales: 'GB',
  'northern ireland': 'GB',
  'hong kong': 'HK',
  macau: 'MO',
  'puerto rico': 'PR',
  'american samoa': 'AS',
  bermuda: 'BM',
  'british virgin islands': 'VG',
  'cayman islands': 'KY',
  'cook islands': 'CK',
  'faroe islands': 'FO',
  'french polynesia': 'PF',
  gibraltar: 'GI',
  greenland: 'GL',
  guadeloupe: 'GP',
  guam: 'GU',
  'isle of man': 'IM',
  jersey: 'JE',
  martinique: 'MQ',
  mayotte: 'YT',
  'new caledonia': 'NC',
  niue: 'NU',
  'norfolk island': 'NF',
  'northern mariana islands': 'MP',
  palestin: 'PS',
  palestine: 'PS',
  'pitcairn islands': 'PN',
  reunion: 'RE',
  'saint helena': 'SH',
  'saint pierre and miquelon': 'PM',
  tokelau: 'TK',
  'turks and caicos islands': 'TC',
  'us virgin islands': 'VI',
  'wallis and futuna': 'WF',
  'western sahara': 'EH',
};

// Function to convert 2-letter code to flag
export function countryCodeToFlag(code3LetterOrCountry: string) {
  const code =
    iso3to2[String(code3LetterOrCountry).toUpperCase()] ||
    countryToCode[String(code3LetterOrCountry).toLowerCase()];

  if (!code) return '';

  return code
    .toUpperCase()
    .replace(/./g, (char) => String.fromCodePoint(127397 + char.charCodeAt(0)));
}
