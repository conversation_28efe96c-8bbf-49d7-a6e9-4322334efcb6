import { addHours, format } from 'date-fns';

// Calculate checkout time
export const calculateCheckoutTime = ({
  date,
  checkinTime,
  duration,
}: {
  date?: Date | null;
  checkinTime?: string;
  duration?: number;
}) => {
  if (!date || !checkinTime || !duration) return null;

  try {
    // Parse the check-in time (format: "HH:MM")
    const [hours, minutes] = checkinTime.split(':').map(Number);

    // Create a new date object with the selected date and time
    const checkInDateTime = new Date(date);
    checkInDateTime.setHours(hours, minutes, 0, 0);

    // Add the duration hours
    const checkoutDateTime = addHours(checkInDateTime, duration);

    return checkoutDateTime;
  } catch (error) {
    console.error('Error calculating checkout time:', error);
    return null;
  }
};

export const formatCheckoutDisplay = (date: Date) => {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const dayName = dayNames[date.getDay()];
  const year = date.getFullYear();
  const formattedDate = format(date, 'd MMM');
  const formattedTime = format(date, 'h:mm a');

  return `${formattedDate} ${year} (${dayName}), ${formattedTime}`;
};

export const handleChangeDate = (selectedDate: Date | null) => {
  if (selectedDate) {
    const now = new Date();
    const malaysiaTimeOffset = 8 * 60; // Malaysia is UTC+8
    const localOffset = now.getTimezoneOffset(); // Get the local offset in minutes
    const malaysiaTime = new Date(
      now.getTime() + (malaysiaTimeOffset + localOffset) * 60000
    );

    const isToday = selectedDate.toDateString() === now.toDateString();

    if (isToday) {
      // Get the current hour and minutes
      let hours = malaysiaTime.getHours();
      let minutes = malaysiaTime.getMinutes();

      // Calculate the next 30-minute mark
      if (minutes > 30) {
        minutes = 0;
        hours += 1; // Move to the next hour
      } else {
        minutes = 30;
      }

      // Set the selectedDate to the rounded time
      selectedDate.setHours(hours);
      selectedDate.setMinutes(minutes);
      selectedDate.setSeconds(0);
      selectedDate.setMilliseconds(0);
    } else {
      selectedDate.setHours(0);
      selectedDate.setMinutes(0);
      selectedDate.setSeconds(0);
      selectedDate.setMilliseconds(0);
    }

    return selectedDate;
  }
};

export function formatDateWithWeekday(date: Date) {
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    weekday: 'short',
  };
  const formatted = new Intl.DateTimeFormat('en-GB', options).format(date);

  // "Tue, 27 May 2025" → rearrange
  const [weekday, day, month, year] = formatted.replace(',', '').split(' ');
  return `${day} ${month} ${year} (${weekday})`;
}

export function toAmPmTime(time24: string) {
  const [hourStr, minute] = time24.split(':');
  let hour = parseInt(hourStr, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12; // convert 0 → 12, 13 → 1, etc.
  return `${hour}:${minute} ${ampm}`;
}
