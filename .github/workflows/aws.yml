name: Deploy to <PERSON><PERSON>  

on:
  push:
    branches: [ main ]
env:
  ECR_REPOSITORY: capsulelandingpage
  ECR_REGISTRY: 204760077555.dkr.ecr.ap-southeast-1.amazonaws.com
  EKS_CLUSTER_NAME: eks-ginan 
  AWS_REGION: ap-southeast-1
  K8SNAMESPACE: ct-landingpage

jobs:
  
  build:
    
    name: Deployment
    runs-on: ubuntu-latest

    steps:

    - name: Set short git commit SHA
      id: commit
      uses: prompt/actions-commit-hash@v2

    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{env.AWS_REGION}}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}        
        IMAGE_TAG: ${{ steps.commit.outputs.short }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Update kube config
      run: aws eks update-kubeconfig --name $EKS_CLUSTER_NAME --region $AWS_REGION

    - name: Deploy to EKS
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}        
        IMAGE_TAG: ${{ steps.commit.outputs.short }}
      run: |
        sed -i.bak "s|DOCKER_IMAGE|$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG|g" manifests/deployment.yml && \
        kubectl apply -f manifests/deployment.yml -n ${{env.K8SNAMESPACE}}
        kubectl apply -f manifests/service.yml -n ${{env.K8SNAMESPACE}}